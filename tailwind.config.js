import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
const plugin = require("tailwindcss/plugin");
const rotateX = plugin(function ({ addUtilities }) {
    addUtilities({
        ".rotate-y-180": {
            transform: "rotateY(180deg)",
        },
    });
});
module.exports = {
    mode: 'jit',
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.vue',
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
    ],
    darkMode: "class",
    theme: {
        fontFamily: {
            sans: ['Cairo', ...defaultTheme.fontFamily.sans],
            serif: [...defaultTheme.fontFamily.serif],
            mono: [...defaultTheme.fontFamily.mono],
            // nunito: ['Nunito', ...defaultTheme.fontFamily.sans],
        },
        container: {
            center: true,
        },
        extend: {
            colors: {
                primary: {
                    DEFAULT: "#007172",
                    light: "#eaf1ff",
                    "default-light": "#F5F9F9",
                    "dark-light": "rgba(67,97,238,.15)",
                },
                secondary: {
                    DEFAULT: "#805dca",
                    light: "#ebe4f7",
                    "dark-light": "rgb(128 93 202 / 15%)",
                },
                teal: {
                    DEFAULT: "#025259",
                    light: "#E6ECEC",
                    "dark-light": "rgba(2,82,89,.15)",
                },
                orange: {
                    DEFAULT: "#D94F04",
                    light: "#FCE9E1",
                    "dark-light": "rgba(217,79,4,.15)",
                    lighter:"#fcf1ebbf",
                    'orange-light':"#FFFBF4"
                },
                amber: {  // Fixed amber object structure
                    DEFAULT: "#F29325",
                    light: "#FEF2E5",
                    "dark-light": "rgba(242,147,37,.15)",
                    "translucent": "rgba(249, 185, 56, 0.74)"
                },
                success: {
                    DEFAULT: "#00ab55",
                    light: "#ddf5f0",
                    "dark-light": "rgba(0,171,85,.15)",
                },
                danger: {
                    DEFAULT: "#BF0606",
                    light: "#fff5f5",
                    "dark-light": "rgba(231,81,90,.15)",
                    "translucent": "rgba(191, 6, 6, 0.72)"
                },
                warning: {
                    DEFAULT: "#e2a03f",
                    light: "#fff9ed",
                    "dark-light": "rgba(226,160,63,.15)",
                },
                info: {
                    DEFAULT: "#2196f3",
                    light: "#e7f7ff",
                    "dark-light": "rgba(33,150,243,.15)",
                },
                dark: {
                    DEFAULT: "#3b3f5c",
                    light: "#eaeaec",
                    "dark-light": "rgba(59,63,92,.15)",
                },
                black: {
                    DEFAULT: "#0e1726",
                    light: "#e3e4eb",
                    "dark-light": "rgba(14,23,38,.15)",
                    dark: "#060818",
                },
                white: {
                    DEFAULT: "#ffffff",
                    light: "#e0e6ed",
                    dark: "#888ea8",
                },
                gray: {
                    outline: "#E2E2E2",
                    background: "#F5F9F9",
                    text: "#323232",
                    secondary: "#6F6F6F",
                    medium: "#9DA4A7",
                    border: "#DADADA",
                    light:"#f5f5f5b8",
                    menu:"#F8F8F8"
                },
                blue: {
                    default: "#3B5998",
                    dark:"#082640",
                    darkness: "#17263c",
                    muted: "#506690"
                },
                peach: {
                    light: "#FCF1EB"
                },
                green: {
                    custom: "#268764",
                    light:"#FCFCFC"
                },
                backgrounds: {
                    dark: "#101010"
                }
            },
            borderRadius: {
                'small': '8px',
                'large': '24px',
                'medium': '16px'
            },
            borderWidth: {
                'thinner': '0.4px',
                'thin': '0.6px'
            },
            spacing: {
                4.5: "18px",
            },
            boxShadow: {
                "3xl": "0 2px 2px rgb(224 230 237 / 46%), 1px 6px 7px rgb(224 230 237 / 46%)",
                "soft": "0px 0px 4px 0px rgba(0, 0, 0, 0.16)",
                "soft-inset": "inset 0px 0px 1px 0px rgba(0, 0, 0, 0.16)",
            },
            typography: ({ theme }) => ({
                DEFAULT: {
                    css: {
                        "--tw-prose-invert-headings": theme("colors.white.dark"),
                        "--tw-prose-invert-links": theme("colors.white.dark"),
                        h1: {
                            fontSize: "40px",
                            marginBottom: "0.5rem",
                            marginTop: 0,
                        },
                        h2: {
                            fontSize: "32px",
                            marginBottom: "0.5rem",
                            marginTop: 0,
                        },
                        h3: {
                            fontSize: "28px",
                            marginBottom: "0.5rem",
                            marginTop: 0,
                        },
                        h4: {
                            fontSize: "24px",
                            marginBottom: "0.5rem",
                            marginTop: 0,
                        },
                        h5: {
                            fontSize: "20px",
                            marginBottom: "0.5rem",
                            marginTop: 0,
                        },
                        h6: {
                            fontSize: "16px",
                            marginBottom: "0.5rem",
                            marginTop: 0,
                        },
                        p: { marginBottom: "0.5rem" },
                        li: { margin: 0 },
                        img: { margin: 0 },
                    },
                },
            }),
            backgroundImage: {
                'headerPattern': "url('/assets/images/headerPattern.svg')",
            }
        },
    },
    plugins: [
        [forms],
        require("@tailwindcss/forms")({
            strategy: "class",
        }),
        require("@tailwindcss/typography"),
        rotateX,
    ],
};
