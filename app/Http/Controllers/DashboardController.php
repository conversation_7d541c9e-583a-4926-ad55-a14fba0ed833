<?php

namespace App\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Modules\GeneralEvent\Entities\Category;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Entities\Level;
use Modules\Order\Entities\Order;
use Modules\User\app\Models\User;

class DashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        // Total Events by status
        $eventStats = GeneralEvent::select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->pluck('total', 'status');

        // Total Users by Role
        $userStats = User::select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->pluck('total', 'status');

        // Totals
        $totalCategories = Category::count();
        $totalOrders = Order::count();
        $totalRefunds = Order::where('status', 'refund')->count(); // or use enum

        return view('dashboard.index', [
            'eventStats' => $eventStats,
            'userStats' => $userStats,
            'totalCategories' => $totalCategories,
            'totalOrders' => $totalOrders,
            'totalRefunds' => $totalRefunds,
        ]);
    }


    public function portalIndex()
    {

        if (Auth::check()) {

            $eventStats = GeneralEvent::select('status', DB::raw('count(*) as total'))
                ->groupBy('status')
                ->where('organizer_id',auth()->user()->id)
                ->pluck('total', 'status');

            $totalOrders = Order::whereHas('event',function($q){
                $q->where('organizer_id',auth()->user()->id);
            })->count();

            $totalLevels = Level::where('user_id',auth()->user()->id)->count();

            return Inertia::render('Portal/Index',[
                'eventStats' => $eventStats,
                'totalOrders' => $totalOrders,
                'totalLevels' => $totalLevels,
            ]);

        }

//        $latestEvents = GeneralEvent::latest()->take(10)->get(['id', 'name', 'description', 'type', 'status','start_date','end_date']);

        $latestEvents = GeneralEvent::with(['media'])
            ->latest()
            ->take(10)
            ->get();

        // Guest user - show public landing
        return Inertia::render('Portal/Home', [
            'canLogin' => route('login'),
            'canRegister' => route('register'),
            'latestEvents' => $latestEvents,
        ]);

        dd($latestEvents);

    }
}
