<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Http\Exceptions\ThrottleRequestsException;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $e)
    {
        if ($e instanceof ThrottleRequestsException && $request->is('login')) {
            $retryAfter = $e->getHeaders()['Retry-After'] ?? 60;

            return redirect()->route('login')->withErrors([
                'email' => "Too many login attempts. Please try again in {$retryAfter} seconds.",
            ]);
        }

        return parent::render($request, $e);
    }

}
