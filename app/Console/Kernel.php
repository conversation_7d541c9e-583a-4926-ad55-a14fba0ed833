<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Modules\GeneralEvent\Console\Commands\SendSessionReminder;
use Modules\GeneralEvent\Console\PublishScheduledEventsCommand;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
         $schedule->command(SendSessionReminder::class)->hourly();
         $schedule->command(PublishScheduledEventsCommand::class)->daily()->timezone('Asia/Riyadh');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
