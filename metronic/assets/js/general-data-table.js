function column_visibility() {
    const visibilityButtons = document.querySelectorAll(
        '[data-kt-table-toolbar="column-visibility"]'
    );
    visibilityButtons?.forEach((button) => {
        button.addEventListener(
            "click",
            function (e) {
                const dropdown = button.nextElementSibling;
                const columnContainer =
                    dropdown.querySelector(".column-container");
                if (
                    dropdown &&
                    dropdown.id === "kt_datatable_column_visibility_dropdown"
                ) {
                    const tableId = button
                        .closest("#kt_app_content .card")
                        .querySelector("table")
                        .getAttribute("id");
                    const table = window.LaravelDataTables[tableId];
                    const toggleableColumns = [];
                    table.columns().every(function (index) {
                        const column = this;
                        const header = $(column.header());

                        if (header.hasClass("toggle-vis")) {
                            toggleableColumns.push({
                                index: index,
                                name: header.text().trim(),
                            });
                        }
                    });

                    // Create checkboxes for each toggleable column
                    toggleableColumns.forEach((column) => {
                        const checkboxDiv = document.createElement("div");
                        checkboxDiv.classList.add(
                            "form-check",
                            "form-check-custom",
                            "form-check-solid",
                            "mb-3"
                        );

                        const checkbox = document.createElement("input");
                        checkbox.type = "checkbox";
                        checkbox.classList.add("form-check-input");
                        checkbox.id = `column_toggle_${column.index}`;
                        checkbox.checked = table.column(column.index).visible();

                        const label = document.createElement("label");
                        label.classList.add("form-check-label");
                        label.htmlFor = `column_toggle_${column.index}`;
                        label.textContent = column.name;

                        checkboxDiv.appendChild(checkbox);
                        checkboxDiv.appendChild(label);
                        columnContainer.appendChild(checkboxDiv);

                        // Add event listener to toggle column visibility
                        checkbox.addEventListener("change", function () {
                            table.column(column.index).visible(this.checked);
                        });
                    });
                }
            },
            {
                once: true,
            }
        );
    });
}

function filter_form() {
    const filterForms = document.querySelectorAll(
        '[data-kt-table-filter="form"]'
    );
    filterForms.forEach((form) => {
        let columnFilters = {};
        const tableId = form
            .closest("#kt_app_content .card")
            .querySelector("table")
            .getAttribute("id");
        const table = window.LaravelDataTables[tableId];
        
        // Try to load filters from URL parameters first
        const urlParams = new URLSearchParams(window.location.search);
        const filtersParam = urlParams.get(`filters_${tableId}`);
        
        if (filtersParam) {
            try {
                columnFilters = JSON.parse(decodeURIComponent(filtersParam));
                
                // Populate form with filters from URL
                Object.entries(columnFilters).forEach(([column, filter]) => {
                    const filterItem = form.querySelector(`.filter_item [data-kt-table-filter="value"][data-column="${column}"]`);
                    if (filterItem) {
                        filterItem.value = filter.value;
                        const operatorSelect = filterItem.closest('.filter_item').querySelector('[data-kt-table-filter="operator"]');
                        if (operatorSelect) {
                            $(operatorSelect).val(filter.operator).trigger('change');
                        }
                    }
                });
                
                // Apply filters to the table
                table.on("preXhr.dt", function (e, settings, data) {
                    data.columnFilters = columnFilters;
                });
                
                // Force reload to apply filters
                if (Object.keys(columnFilters).length > 0) {
                    table.ajax.reload();
                }
            } catch (e) {
                console.error("Error loading filters from URL:", e);
                // Fallback to localStorage if URL parsing fails
                
            }
        } else {
            // Fallback to localStorage if no URL parameters
           
        }
        
        
        const filterButton = form.querySelector(
            '[data-kt-table-filter="filter"]'
        );
        filterButton.addEventListener("click", function () {
            const filterItems = form.querySelectorAll(".filter_item");
            
            columnFilters = {};

            filterItems.forEach((item) => {
                const column = item
                    .querySelector('[data-kt-table-filter="value"]')
                    ?.getAttribute("data-column");
                const operator = item.querySelector(
                    '[data-kt-table-filter="operator"]'
                )?.value;
                const value = item
                    .querySelector('[data-kt-table-filter="value"]')
                    .value.trim();

                if (value) {
                    columnFilters[column] = {
                        operator: operator,
                        value: value,
                    };
                }
            });
            const isFiltersEmpty = Object.keys(columnFilters).length === 0;
            if (!isFiltersEmpty) {
                // Save filters to localStorage
                localStorage.setItem(`dt_filters_${tableId}`, JSON.stringify(columnFilters));
                
                // Update URL with filters
                updateUrlWithFilters(tableId, columnFilters);
                
                table.on("preXhr.dt", function (e, settings, data) {
                    data.columnFilters = columnFilters;
                });
                table.ajax.reload();
                table.rows().deselect();
            }
        });

        const resetButton = form.querySelector(
            '[data-kt-table-filter="reset"]'
        );
        resetButton.addEventListener("click", function () {
            const operatorSelects = form.querySelectorAll(
                '[data-kt-table-filter="operator"]'
            );
            const valueInputs = form.querySelectorAll(
                '[data-kt-table-filter="value"]'
            );

            operatorSelects.forEach((select) => {
                $(select).val("").trigger("change");
            });

            valueInputs.forEach((input) => {
                input.value = "";
            });

            const isFiltersEmpty = Object.keys(columnFilters).length === 0;

            if (!isFiltersEmpty) {
                columnFilters = {};
                
                // Remove filters from localStorage
                localStorage.removeItem(`dt_filters_${tableId}`);
                
                // Remove filters from URL
                removeFiltersFromUrl(tableId);
                
                table.on("preXhr.dt", function (e, settings, data) {
                    data.columnFilters = columnFilters;
                });
                table.ajax.reload();
                table.rows().deselect();
            }
        });
    });
}

// Helper function to update URL with filters
function updateUrlWithFilters(tableId, filters) {
    const url = new URL(window.location);
    const params = new URLSearchParams(url.search);
    
    // Set the filters parameter
    params.set(`filters_${tableId}`, encodeURIComponent(JSON.stringify(filters)));
    
    // Update URL without reloading the page
    url.search = params.toString();
    window.history.pushState({}, '', url);
}

// Helper function to remove filters from URL
function removeFiltersFromUrl(tableId) {
    const url = new URL(window.location);
    const params = new URLSearchParams(url.search);
    
    // Remove the filters parameter
    params.delete(`filters_${tableId}`);
    
    // Update URL without reloading the page
    url.search = params.toString();
    window.history.pushState({}, '', url);
}

function search_input() {
    const searchInputs = document.querySelectorAll(
        '[data-kt-table-filter="search"]'
    );
    searchInputs?.forEach((input) => {
        const debounce = (fn, delay) => {
            let timer;
            return function (...args) {
                clearTimeout(timer);
                timer = setTimeout(() => fn.apply(this, args), delay);
            };
        };

        const tableId = input
            .closest("#kt_app_content .card")
            .querySelector("table")
            .getAttribute("id");

        const table = window.LaravelDataTables[tableId];
        if (table.state && table.state.loaded()) {
            const savedState = table.state.loaded();
            if (savedState && savedState.search && savedState.search.search) {
                input.value = savedState.search.search;
            }
        }

        let previousSearchValue = input.value || "";

        const debouncedSearch = debounce(function (e) {
            const currentValue = e.target.value.trim();

            if (currentValue !== "") {
                table.search(currentValue).draw();
                previousSearchValue = currentValue;
            } else if (previousSearchValue !== "") {
                table.search("").draw(false);
                previousSearchValue = "";
            }
            table.rows().deselect();
        }, 500);

        input.addEventListener("input", debouncedSearch);
    });
}

function row_selection() {
    const headerCheckboxes = document.querySelectorAll(".header-checkbox");
    headerCheckboxes.forEach((checkbox) => {
        const tableId = checkbox.closest("table").id;
        // Check if all rows on current page are selected and update header checkbox
        const currentPageCheckboxes = document.querySelectorAll(
            `#${tableId} tbody input[type='checkbox']`
        );
        const checkedCheckboxes = document.querySelectorAll(
            `#${tableId} tbody input[type='checkbox']:checked`
        );

        // Update header checkbox state based on current page selection
        checkbox.checked =
            currentPageCheckboxes.length > 0 &&
            currentPageCheckboxes.length === checkedCheckboxes.length;
        checkbox.indeterminate =
            checkedCheckboxes.length > 0 &&
            checkedCheckboxes.length < currentPageCheckboxes.length;

        // Remove existing event listener before adding new one
        checkbox.removeEventListener("click", headerCheckboxHandler);

        // Handle header checkbox click
        function headerCheckboxHandler() {
            const table = window.LaravelDataTables[tableId];
            const checkboxes = document.querySelectorAll(
                `#${tableId} tbody input[type='checkbox']`
            );

            checkboxes.forEach((checkbox) => {
                checkbox.checked = this.checked;
                const row = checkbox.closest("tr");
                if (this.checked) {
                    row.classList.add("row-selected");
                } else {
                    row.classList.remove("row-selected");
                }
            });

            if (this.checked) {
                table.rows({ page: "current" }).select();
            } else {
                table.rows({ page: "current" }).deselect();
            }
        }

        checkbox.addEventListener("click", headerCheckboxHandler);

        // Update header checkbox state when row checkboxes change

        const tbody = document.querySelector(`#${tableId} tbody`);
        tbody.removeEventListener("change", rowCheckboxHandler);

        function rowCheckboxHandler(e) {
            if (e.target.type === "checkbox") {
                const currentPageCheckboxes = document.querySelectorAll(
                    `#${tableId} tbody input[type='checkbox']`
                );
                const checkedCheckboxes = document.querySelectorAll(
                    `#${tableId} tbody input[type='checkbox']:checked`
                );
                checkbox.checked =
                    currentPageCheckboxes.length > 0 &&
                    currentPageCheckboxes.length === checkedCheckboxes.length;
                checkbox.indeterminate =
                    checkedCheckboxes.length > 0 &&
                    checkedCheckboxes.length < currentPageCheckboxes.length;
            }
        }

        tbody.addEventListener("change", rowCheckboxHandler);
    });
}
