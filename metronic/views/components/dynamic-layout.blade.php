@php
    /**
     * Dynamic Layout Component
     *
     * A flexible, responsive layout component that can be used for different views and forms.
     *
     * @param bool $showSidebar - Whether to show the sidebar (default: true)
     * @param string $sidebarPosition - Position of sidebar: 'left' or 'right' (default: 'left')
     * @param bool $hasTabs - Whether to show tabs (default: false)
     * @param array $tabs - Array of tab configurations (default: [])
     * @param string $layout - Layout type: 'horizontal' or 'vertical' (default: 'horizontal')
     * @param bool $responsive - Whether the layout should be responsive (default: true)
     * @param string $title - Main title to display in the header
     * @param string $backRoute - Route to go back to
     * @param string $backLabel - Label for the back button
     * @param string $uniqueId - Unique identifier for the component
     */

    $showSidebar = $showSidebar ?? true;
    $sidebarPosition = $sidebarPosition ?? 'left';
    $hasTabs = $hasTabs ?? false;
    $tabs = $tabs ?? [];
    $layout = $layout ?? 'horizontal';
    $responsive = $responsive ?? true;
    $title = $title ?? 'Details';
    $backRoute = $backRoute ?? '#';
    $backLabel = $backLabel ?? 'Back';
    $uniqueId = $uniqueId ?? 'dynamic-layout-' . uniqid();

    // Calculate layout classes based on configuration
    $sidebarWidth = $sidebarWidth ?? 'col-lg-3';
    $contentWidth = $contentWidth ?? 'col-lg-9';

    // When in vertical layout, both sidebar and content are full width
    if ($layout === 'vertical') {
        $sidebarWidth = 'col-12';
        $contentWidth = 'col-12';
    }

    // Responsive classes for stacking on mobile
    $responsiveClass = $responsive ? 'flex-column flex-lg-row' : '';

    // Set hasTabs automatically if tabs array is provided
    if (!empty($tabs)) {
        $hasTabs = true;
    }
@endphp

<div id="{{ $uniqueId }}" class="dynamic-layout-container">
    <div class="card mb-5 mb-xl-10">
        <div class="card-header border-0 pt-6">
            <div class="card-title">
                <h2>{{ $title }}</h2>
            </div>
            <div class="card-toolbar">
                @if(isset($headerActions))
                    {{ $headerActions }}
                @else
                    <a href="{{ $backRoute }}" class="btn btn-light-primary">
                        <span class="svg-icon svg-icon-2">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.60001 11H21C21.6 11 22 11.4 22 12C22 12.6 21.6 13 21 13H9.60001V11Z" fill="currentColor"/>
                                <path opacity="0.3" d="M9.6 20V4L2.3 11.3C1.9 11.7 1.9 12.3 2.3 12.7L9.6 20Z" fill="currentColor"/>
                            </svg>
                        </span>
                        {{ $backLabel }}
                    </a>
                @endif
            </div>
        </div>
        <div class="card-body">
            <div class="row {{ $responsiveClass }}">
                @if($showSidebar && $sidebarPosition === 'left')
                    <div class="{{ $sidebarWidth }} {{ $layout === 'horizontal' ? 'pe-lg-10 mb-10 mb-lg-0' : 'mb-10' }}">
                        @if(isset($sidebar))
                            {{ $sidebar }}
                        @else
                            <div class="card shadow-sm">
                                <div class="card-body p-5">
                                    <p class="text-muted mb-0">Sidebar content goes here. Pass a 'sidebar' slot to customize.</p>
                                </div>
                            </div>
                        @endif
                    </div>
                @endif

                <div class="{{ $contentWidth }}">
                    @if($hasTabs)
                        <div class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                            @if(!empty($tabs))
                                {{-- Generate tabs dynamically from array --}}
                                @foreach($tabs as $index => $tab)
                                    <li class="nav-item">
                                        <a class="nav-link {{ $index === 0 ? 'active' : '' }}"
                                           data-bs-toggle="tab"
                                           href="#{{ $tab['id'] ?? 'tab_'.$index }}"
                                           aria-controls="{{ $tab['id'] ?? 'tab_'.$index }}">
                                            @if(!empty($tab['icon']))
                                                <span class="svg-icon svg-icon-2 me-2">
                                                    {!! $tab['icon'] !!}
                                                </span>
                                            @endif
                                            {{ $tab['title'] }}
                                        </a>
                                    </li>
                                @endforeach
                            @elseif(isset($tabNav))
                                {{-- Use custom tab navigation --}}
                                {{ $tabNav }}
                            @endif
                        </div>
                        <div class="tab-content" id="{{ $uniqueId }}-tab-content">
                            @if(!empty($tabs))
                                {{-- Generate tab content panels dynamically --}}
                                @foreach($tabs as $index => $tab)
                                    @php
                                        $tabId = $tab['id'] ?? 'tab_'.$index;
                                        $slotName = 'tab_' . ($tab['id'] ?? $index);
                                    @endphp
                                    <div class="tab-pane fade {{ $index === 0 ? 'show active' : '' }}"
                                         id="{{ $tabId }}">
                                        @if(isset(${$slotName}))
                                            {{ ${$slotName} }}
                                        @elseif(!empty($tab['content']))
                                            {!! $tab['content'] !!}
                                        @else
                                            <div class="card shadow-sm">
                                                <div class="card-body p-5">
                                                    <p class="text-muted mb-0">Tab content for '{{ $tab['title'] }}'. Pass a '{{ $slotName }}' slot to customize.</p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            @elseif(isset($tabContent))
                                {{-- Use custom tab content --}}
                                {{ $tabContent }}
                            @endif
                        </div>
                    @else
                        @if(isset($content))
                            {{ $content }}
                        @else
                            <div class="card shadow-sm">
                                <div class="card-body p-5">
                                    <p class="text-muted mb-0">Main content goes here. Pass a 'content' slot to customize.</p>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>

                @if($showSidebar && $sidebarPosition === 'right')
                    <div class="{{ $sidebarWidth }} {{ $layout === 'horizontal' ? 'ps-lg-10 mt-10 mt-lg-0' : 'mt-10' }}">
                        @if(isset($sidebar))
                            {{ $sidebar }}
                        @else
                            <div class="card shadow-sm">
                                <div class="card-body p-5">
                                    <p class="text-muted mb-0">Sidebar content goes here. Pass a 'sidebar' slot to customize.</p>
                                </div>
                            </div>
                        @endif
                    </div>
                @endif
            </div>

            @if(isset($footer))
                <div class="d-flex justify-content-end mt-10">
                    {{ $footer }}
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
    @if($hasTabs)
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Bootstrap tabs
                const tabElements = document.querySelectorAll('#{{ $uniqueId }} .nav-tabs .nav-link');
                tabElements.forEach(function(tab) {
                    tab.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Remove active class from all tabs
                        tabElements.forEach(function(t) {
                            t.classList.remove('active');
                        });

                        // Add active class to clicked tab
                        this.classList.add('active');

                        // Show corresponding tab content
                        const target = this.getAttribute('data-bs-target') || this.getAttribute('href');
                        const tabContents = document.querySelectorAll('#{{ $uniqueId }}-tab-content .tab-pane');

                        tabContents.forEach(function(content) {
                            content.classList.remove('show', 'active');
                        });

                        if (target) {
                            const activeContent = document.querySelector(target);
                            if (activeContent) {
                                activeContent.classList.add('show', 'active');
                            }
                        }
                    });
                });

                // Activate first tab by default if none is active
                if (tabElements.length > 0) {
                    const activeTab = document.querySelector('#{{ $uniqueId }} .nav-tabs .nav-link.active');
                    if (!activeTab) {
                        tabElements[0].click();
                    }
                }
            });
        </script>
    @endif

    @if($responsive)
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const layout = document.getElementById('{{ $uniqueId }}');
                const breakpoint = window.matchMedia('(max-width: 992px)');

                function handleBreakpointChange(e) {
                    if (e.matches) {
                        // Mobile view adjustments (if needed beyond Bootstrap's responsive classes)
                    } else {
                        // Desktop view adjustments (if needed beyond Bootstrap's responsive classes)
                    }
                }

                // Initialize
                handleBreakpointChange(breakpoint);

                // Add listener for screen size changes
                breakpoint.addEventListener('change', handleBreakpointChange);
            });
        </script>
    @endif
@endpush
