@php
    /**
     * Information Table Row Component
     *
     * A reusable component for displaying a row in the info-table component.
     *
     * @param string $label - Label to display in the first column
     * @param string $labelWidth - Width of the label column (default: 'w-25')
     * @param bool $badge - Whether to display the value as a badge (default: false)
     * @param string $badgeType - Type of badge (default: 'light-primary')
     */

    $label = $label ?? '';
    $labelWidth = $labelWidth ?? 'w-25';
    $badge = $badge ?? false;
    $badgeType = $badgeType ?? 'light-primary';
@endphp

<tr>
    <td class="fw-bold text-gray-600 {{ $labelWidth }}">{{ $label }}</td>
    <td class="text-gray-800 {{ !$badge ? 'fw-semibold' : '' }}">
        @if($badge)
            <span class="badge badge-{{ $badgeType }} fw-bold">{{ $slot }}</span>
        @else
            {{ $slot }}
        @endif
    </td>
</tr>
