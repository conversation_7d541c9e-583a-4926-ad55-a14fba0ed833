<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<!--begin::Head-->
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <meta property="og:locale" content="en_US"/>

    <link rel="shortcut icon" href="{{  asset(config('app.admin_theme_path'). '/media/logos/favicon.ico') }}"/>
    <title>@yield('title', 'Dashboard')</title>

    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700"/>
    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <!--begin::Global Stylesheets Bundle-->

    @if (app()->getLocale() === 'ar')
        <link href="{{ asset(config('app.admin_theme_path'). '/plugins/global/plugins.bundle.rtl.css') }}" rel="stylesheet" type="text/css"/>
        <link href="{{ asset(config('app.admin_theme_path'). '/css/style.bundle.rtl.css') }}" rel="stylesheet" type="text/css"/>
    @else
        <link href="{{  asset(config('app.admin_theme_path'). '/plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css"/>
        <link href="{{  asset(config('app.admin_theme_path'). '/css/style.bundle.css') }}" rel="stylesheet" type="text/css"/>
    @endif
    @stack('styles')

    <!--end::Global Stylesheets Bundle-->
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body" class="header-fixed header-tablet-and-mobile-fixed">
@php
    $isRtl = app()->getLocale() === 'ar';
@endphp
<!--begin::Theme mode setup on page load-->
@include('theme::admin.layout.partials.theme-mode')
<!--end::Theme mode setup on page load-->
<!--begin::Main-->
<!--begin::Root-->
<div class="d-flex flex-column flex-root">
    <!--begin::Page-->
    <div class="page d-flex flex-row flex-column-fluid">
        <!--begin::Wrapper-->
        <div class="wrapper d-flex flex-column flex-row-fluid" id="kt_wrapper">
            <!--begin::Header-->
            <div id="kt_header" class="header">
                @include('theme::admin.layout.partials.header')
            </div>
            {{--            @include('admin.layout.partials.toolbar')--}}
            <!--end::Header-->
            <!--begin::Content wrapper-->
            <div class="d-flex flex-column-fluid">
                <!--begin::Aside-->
                <div id="kt_aside" class="aside card" data-kt-drawer="true" data-kt-drawer-name="aside"
                     data-kt-drawer-activate="{default: true, lg: false}"
                     data-kt-drawer-overlay="true" data-kt-drawer-width="{default:'200px', '300px': '250px'}"
                     data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_aside_toggle
                ">

                    @include('theme::admin.layout.partials.sidebar')
                </div>
                <!--end::Aside-->
                <!--begin::Container-->
                <div class="d-flex flex-column flex-column-fluid container-fluid">
                    <!--begin::Post-->
                    <div class="content flex-column-fluid" id="kt_content">
                        @yield('content')

                    </div>
                    <!--end::Post-->

                </div>
                <!--end::Container-->
            </div>
            <!--end::Content wrapper-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end::Page-->
</div>
<!--end::Root-->
<!--end::Main-->
<!--begin::Scrolltop-->
<div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
    <i class="ki-duotone ki-arrow-up">
        <span class="path1"></span>
        <span class="path2"></span>
    </i>
</div>
<!--end::Scrolltop-->

<!--end::Javascript-->
<script src="{{ asset(config('app.admin_theme_path').'/plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ asset(config('app.admin_theme_path').'/plugins/custom/datatables/datatables.bundle.js') }}"></script>
<script src="{{ asset(config('app.admin_theme_path').'/js/formrepeater.bundle.js') }}"></script>
<script src="{{ asset(config('app.admin_theme_path').'/js/scripts.bundle.js') }}"></script>

@stack('scripts')
</body>
<!--end::Body-->
</html>
