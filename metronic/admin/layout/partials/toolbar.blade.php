{{-- resources/views/layouts/partials/toolbar.blade.php --}}
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-gray-900 fw-bold my-1 fs-3">{{ $title ?? 'Dashboard' }}</h1>
        <!--end::Title-->
        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="{{ route('dashboard') }}" class="text-gray-600 text-hover-primary">Home</a>
            </li>
            <!--end::Item-->
            
            @if(isset($breadcrumbs) && is_array($breadcrumbs))
                @foreach($breadcrumbs as $breadcrumb)
                    @if(!$loop->last)
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-gray-600">
                            @if(isset($breadcrumb['url']))
                                <a href="{{ $breadcrumb['url'] }}" class="text-gray-600 text-hover-primary">{{ $breadcrumb['title'] }}</a>
                            @else
                                {{ $breadcrumb['title'] }}
                            @endif
                        </li>
                        <!--end::Item-->
                    @else
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-gray-500">{{ $breadcrumb['title'] }}</li>
                        <!--end::Item-->
                    @endif
                @endforeach
            @endif
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
    
    @if(isset($showActions) && $showActions)
    <!--begin::Actions-->
    <div class="d-flex align-items-center py-2 py-md-1">
        <!--begin::Wrapper-->
        <div class="me-3">
            <!--begin::Menu-->
            <a href="#" class="btn btn-light fw-bold" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                <i class="ki-duotone ki-filter fs-5 text-gray-500 me-1">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>Filter</a>
            <!--begin::Menu 1-->
            <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true" id="kt_menu_filter">
                <!--begin::Header-->
                <div class="px-7 py-5">
                    <div class="fs-5 text-gray-900 fw-bold">Filter Options</div>
                </div>
                <!--end::Header-->
                <!--begin::Menu separator-->
                <div class="separator border-gray-200"></div>
                <!--end::Menu separator-->
                <!--begin::Form-->
                <div class="px-7 py-5">
                    {{ $filterContent ?? '' }}
                    <!--begin::Actions-->
                    <div class="d-flex justify-content-end">
                        <button type="reset" class="btn btn-sm btn-light btn-active-light-primary me-2" data-kt-menu-dismiss="true">Reset</button>
                        <button type="submit" class="btn btn-sm btn-primary" data-kt-menu-dismiss="true">Apply</button>
                    </div>
                    <!--end::Actions-->
                </div>
                <!--end::Form-->
            </div>
            <!--end::Menu 1-->
            <!--end::Menu-->
        </div>
        <!--end::Wrapper-->
        <!--begin::Button-->
        @if(isset($createRoute))
        <a href="{{ $createRoute }}" class="btn btn-dark fw-bold" id="kt_toolbar_primary_button">Create</a>
        @else
        <a href="#" class="btn btn-dark fw-bold" data-bs-toggle="modal" data-bs-target="#kt_modal_create_app" id="kt_toolbar_primary_button">Create</a>
        @endif
        <!--end::Button-->
    </div>
    <!--end::Actions-->
    @endif
</div>