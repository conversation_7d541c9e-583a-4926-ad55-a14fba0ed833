<!--begin::Repeater-->
<div id="{{ $name }}" class="{{ $classes }}">
    @if ($label)
        <label class="form-label fw-semibold fs-6 mb-2 {{ $required ? 'required' : '' }}">{{ $label }}</label>
    @endif

    @if ($description)
        <div class="text-muted fs-7 mb-3">{{ $description }}</div>
    @endif

    <!--begin::Form group-->
    <div class="form-group">
        <div data-repeater-list="{{ $name }}">
            <div data-repeater-item>
                <div class="form-group row mb-5">
                    {{ $slot }}

                    <div class="col-md-2">
                        <a href="javascript:;" data-repeater-delete class="btn btn-sm btn-light-danger mt-3 mt-md-8 delete-repeater-item">
                            <i class="ki-duotone ki-trash fs-5"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span></i>
                            {{ $deleteButtonText }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end::Form group-->

    <!--begin::Form group-->
    <div class="form-group mt-5">
        <a href="javascript:;" data-repeater-create class="btn btn-light-primary">
            <i class="ki-duotone ki-plus fs-3"></i>
            {{ $addButtonText }}
        </a>
    </div>
    <!--end::Form group-->

    @error($name)
        <span class="invalid-feedback d-block" role="alert">
            <strong>{{ $message }}</strong>
        </span>
    @enderror
</div>
<!--end::Repeater-->

@push('scripts')
<script>
    // Initialize the repeater when the document is ready
    $(document).ready(function() {
        // Function to initialize editors in a container
        function initEditors(container) {
            // Find all translation editors in the container
            $(container).find('[data-kt-translation-editor="true"]').each(function() {
                const editorId = $(this).attr('id');

                // Skip if already initialized
                if ($(this).hasClass('ql-container')) {
                    return;
                }

                // Generate a unique ID if needed
                if (!editorId || $(this).find('.ql-editor').length > 0) {
                    const newId = 'editor_' + Math.random().toString(36).substring(2, 15);
                    $(this).attr('id', newId);
                }

                const currentId = $(this).attr('id');
                const inputId = currentId + '_input';
                const inputElement = document.getElementById(inputId);
                const toolbarType = $(this).attr('data-kt-editor-toolbar') || 'full';
                const readOnly = $(this).attr('data-kt-editor-readonly') === 'true';
                const isRtl = currentId.includes('_ar_');

                // Define toolbar options based on type
                let toolbarOptions;

                if (toolbarType === 'basic') {
                    toolbarOptions = [
                        ['bold', 'italic', 'underline'],
                        ['clean']
                    ];
                } else if (toolbarType === 'minimal') {
                    toolbarOptions = [
                        ['bold', 'italic'],
                        ['clean']
                    ];
                } else {
                    // Full toolbar
                    toolbarOptions = [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': isRtl ? 'rtl' : 'ltr' }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'font': [] }],
                        [{ 'align': [] }],
                        ['clean'],
                        ['link', 'image', 'video']
                    ];
                }

                // Clear any existing content first
                $(this).empty();

                // Initialize Quill with RTL support for Arabic
                const quill = new Quill(`#${currentId}`, {
                    modules: {
                        toolbar: toolbarOptions
                    },
                    placeholder: inputElement ? inputElement.getAttribute('placeholder') || '' : '',
                    theme: 'snow',
                    readOnly: readOnly,
                    direction: isRtl ? 'rtl' : 'ltr'
                });

                // Set initial content
                if (inputElement && inputElement.value) {
                    quill.root.innerHTML = inputElement.value;
                }

                // Update hidden input on text-change
                quill.on('text-change', function() {
                    if (inputElement) {
                        inputElement.value = quill.root.innerHTML;
                    }
                });
            });
        }

        // Function to initialize select2 in a container
        // function initSelect2(container) {
        //     console.log(container)
        //     // Only initialize Select2 for elements that don't already have it
        //     $(container).find('[data-control="select2"]').each(function() {
        //         console.log(132)
        //         // Initialize Select2
        //         $(this).select2();
        //     });
        // }

        // Function to initialize flatpickr in a container
        function initFlatpickr(container) {
            $(container).find('[data-kt-date]').each(function() {
                const enableTime = $(this).attr('data-kt-date') === 'true';
                const format = $(this).attr('data-kt-date-format') || 'Y-m-d';
                const minDate = $(this).attr('data-kt-date-min') || null;
                const maxDate = $(this).attr('data-kt-date-max') || null;

                // Destroy existing instance if any
                if (this._flatpickr) {
                    this._flatpickr.destroy();
                }

                flatpickr(this, {
                    enableTime: enableTime,
                    dateFormat: format,
                    minDate: minDate,
                    maxDate: maxDate,
                    allowInput: true
                });
            });
        }

        // Function to initialize tagify in a container
        function initTagify(container) {
            $(container).find('[data-kt-tagify]').each(function() {
                // Skip if already initialized
                if (this.tagify) {
                    return;
                }
                new Tagify(this);
            });
        }

        // Initialize the repeater
        const repeater = $('#{{ $name }}').repeater({
            initEmpty: {{ $initEmpty ? 'true' : 'false' }},
            defaultValues: @json($defaultValues),
            isFirstItemUndeletable: true, // Prevent deleting the first item

            show: function () {
                // Store a reference to the newly cloned item
                const $newItem = $(this);

                // Re-init select2
                $(this).find('[data-kt-repeater="select2"]').select2();

                // Slide down the new item
                $newItem.slideDown();

                // Generate unique IDs for all elements in the cloned item
                const timestamp = new Date().getTime();
                const randomSuffix = Math.floor(Math.random() * 1000000);
                const uniqueSuffix = `_${timestamp}_${randomSuffix}`;

                // First, find all translation tab containers and update their IDs
                $(this).find('[id^="translation-tab-"]').each(function() {
                    const oldTabId = $(this).attr('id');
                    const tabIdParts = oldTabId.split('-');
                    const baseTabId = tabIdParts[tabIdParts.length - 1]; // Get the tabId part
                    const newTabId = 'translation-tab-' + baseTabId + uniqueSuffix;

                    // Update the tab container ID
                    $(this).attr('id', newTabId);

                    // Update the corresponding tab content container ID
                    const oldContentId = 'translation-tab-content-' + baseTabId;
                    const newContentId = 'translation-tab-content-' + baseTabId + uniqueSuffix;
                    $(this).closest('[data-repeater-item]').find(`#${oldContentId}`).attr('id', newContentId);

                    // Update all tab buttons and panes within this container
                    for (const lang of ['en', 'ar']) { // Update for each language
                        // Update tab button
                        const oldTabButtonId = `${lang}-tab-${baseTabId}`;
                        const newTabButtonId = `${lang}-tab-${baseTabId}${uniqueSuffix}`;
                        const tabButton = $(this).closest('[data-repeater-item]').find(`#${oldTabButtonId}`);

                        if (tabButton.length) {
                            // Update the button ID
                            tabButton.attr('id', newTabButtonId);

                            // Update the data-bs-target attribute
                            const oldTarget = `#${lang}-pane-${baseTabId}`;
                            const newTarget = `#${lang}-pane-${baseTabId}${uniqueSuffix}`;
                            tabButton.attr('data-bs-target', newTarget);

                            // Update aria-controls
                            tabButton.attr('aria-controls', `${lang}-pane-${baseTabId}${uniqueSuffix}`);
                        }

                        // Update tab pane
                        const oldPaneId = `${lang}-pane-${baseTabId}`;
                        const newPaneId = `${lang}-pane-${baseTabId}${uniqueSuffix}`;
                        const tabPane = $(this).closest('[data-repeater-item]').find(`#${oldPaneId}`);

                        if (tabPane.length) {
                            // Update the pane ID
                            tabPane.attr('id', newPaneId);

                            // Update aria-labelledby
                            tabPane.attr('aria-labelledby', newTabButtonId);
                        }
                    }
                });

                // Now update all other elements with IDs
                $(this).find('[id]').each(function() {
                    const oldId = $(this).attr('id');

                    // Skip elements that we've already processed (tab-related elements)
                    if (oldId.includes('translation-tab-') ||
                        oldId.includes('-tab-') ||
                        oldId.includes('-pane-')) {
                        return;
                    }

                    const newId = oldId + uniqueSuffix;

                    // Update the element's ID
                    $(this).attr('id', newId);

                    // Update any labels that reference this ID
                    $(this).closest('[data-repeater-item]').find(`label[for="${oldId}"]`).attr('for', newId);

                    // Update any hidden inputs associated with editors
                    if ($(this).attr('data-kt-translation-editor') === 'true') {
                        const oldInputId = oldId + '_input';
                        const newInputId = newId + '_input';
                        const inputElement = $(this).closest('[data-repeater-item]').find(`#${oldInputId}`);
                        if (inputElement.length) {
                            inputElement.attr('id', newInputId);
                        }
                    }
                });

                // // For Select2 elements, we need special handling
                // $newItem.find('[data-control="select2"]').each(function() {
                //     // // First, make sure any existing Select2 instance is completely removed
                //     if ($(this).hasClass('select2-hidden-accessible')) {
                //         $(this).select2('destroy');
                //     }
                //
                //     // Remove any adjacent Select2 containers
                //     $(this).next('.select2-container').remove();
                //
                //     // Now initialize Select2
                //     $(this).select2();
                // });

                // Initialize other components
                initFlatpickr($newItem);
                initTagify($newItem);
                // Initialize editors with a slight delay to ensure DOM is ready
                setTimeout(() => {
                    initEditors($newItem);
                }, 100);
            },

            hide: function (deleteElement) {
                // Check if this is the last item
                const items = $(this).closest('[data-repeater-list]').find('[data-repeater-item]');
                if (items.length <= 1) {
                    alert('You cannot delete the last item.');
                    return;
                }

                $(this).slideUp(deleteElement);
            },

            ready: function() {
                // Init select2
                $('[data-kt-repeater="select2"]').select2();
                // Initialize all components on page load
                const container = $('#{{ $name }}');
                // initSelect2(container);
                initFlatpickr(container);
                initTagify(container);
                initEditors(container);

                // Prevent deleting the last item
                container.on('click', '.delete-repeater-item', function(e) {
                    const items = $(this).closest('[data-repeater-list]').find('[data-repeater-item]');
                    if (items.length <= 1) {
                        e.preventDefault();
                        e.stopPropagation();
                        alert('You cannot delete the last item.');
                        return false;
                    }
                });
            }
        });
    });
</script>
@endpush
