<!--begin::Input group-->
<div class="mb-10">
    @if($label)
        <label class="form-label {{ $required ? 'required' : '' }}">{{ $label }}</label>
    @endif

    @if($description)
        <div class="text-muted fs-7 mb-3">{{ $description }}</div>
    @endif

    <input
        class="form-control {{ $solid ? 'form-control-solid' : '' }} {{ $classes }} @error($name) is-invalid @enderror"
        name="{{ $name }}"
        id="{{ $id }}"
        value="{{ $value }}"
        placeholder="{{ $placeholder }}"
        {{ $required ? 'required' : '' }}
        {{ $attributes }}
    />

    <!--begin::Error-->
    @error($name)
        <div class="invalid-feedback d-block">
            <strong>{{ $message }}</strong>
        </div>
    @enderror
    <!--end::Error-->
</div>
<!--end::Input group-->

@once

    @push('scripts')

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Tagify elements
            const tagifyElements = document.querySelectorAll('input[id^="kt_tagify_"]');

            tagifyElements.forEach(element => {
                if (element.getAttribute("data-kt-initialized") === "1") {
                    return;
                }

                try {
                    // Simple Tagify initialization
                    new Tagify(element);

                    // Mark as initialized
                    element.setAttribute("data-kt-initialized", "1");
                } catch (error) {
                    console.error('Error initializing Tagify:', error);
                }
            });
        });
    </script>
    @endpush
@endonce
