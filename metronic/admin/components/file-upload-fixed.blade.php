<!--begin::Input group-->
<div class="form-group row mb-7">
    <!--begin::Label-->
    @if ($label)
        <label class="col-lg-2 col-form-label text-lg-right {{ $required ? 'required' : '' }}">{{ $label }}</label>
    @endif
    <!--end::Label-->

    <!--begin::Col-->
    <div class="col-lg-10">
        <!--begin::Dropzone-->
        <div class="dropzone dropzone-queue mb-2 {{ $classes }}"
             id="kt_dropzonejs_{{ str_replace(['[', ']', '.'], '_', $name) }}"
             data-kt-dropzonejs="true"
             data-kt-dropzone-max-files="{{ $maxFiles }}"
             data-kt-dropzone-max-size="{{ $maxSize }}"
             data-kt-dropzone-multiple="{{ $multiple ? 'true' : 'false' }}"
             data-kt-dropzone-upload-url="{{ $uploadUrl ?? '#' }}"
             data-kt-dropzone-delete-url="{{ $deleteUrl ?? '#' }}"
             data-kt-dropzone-accepted-files="{{ $accept }}"
             data-kt-dropzone-name="{{ $name }}"
             data-kt-dropzone-required="{{ $required ? 'true' : 'false' }}"
             data-kt-dropzone-thumbnail-width="{{ $thumbnailWidth }}"
             data-kt-dropzone-thumbnail-height="{{ $thumbnailHeight }}"
        >
            <!--begin::Controls-->
            <div class="dropzone-panel mb-lg-0 mb-2">
                <a class="dropzone-select btn btn-sm btn-primary me-2">Attach files</a>
                <a class="dropzone-remove-all btn btn-sm btn-light-primary">Remove All</a>
                @if ($placeholder)
                    <span class="ms-3 text-muted fs-7">{{ $placeholder }}</span>
                @endif
            </div>
            <!--end::Controls-->

            <!--begin::Items-->
            <div class="dropzone-items wm-200px">
                <div class="dropzone-item" style="display:none">
                    <!--begin::File-->
                    <div class="dropzone-file">
                        <div class="dropzone-filename" title="some_image_file_name.jpg">
                            <span data-dz-name>some_image_file_name.jpg</span>
                            <strong>(<span data-dz-size>340kb</span>)</strong>
                        </div>

                        <div class="dropzone-error" data-dz-errormessage></div>
                    </div>
                    <!--end::File-->

                    <!--begin::Progress-->
                    <div class="dropzone-progress">
                        <div class="progress">
                            <div
                                class="progress-bar bg-primary"
                                role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" data-dz-uploadprogress>
                            </div>
                        </div>
                    </div>
                    <!--end::Progress-->

                    <!--begin::Toolbar-->
                    <div class="dropzone-toolbar">
                        <span class="dropzone-delete" data-dz-remove><i class="bi bi-x fs-1"></i></span>
                    </div>
                    <!--end::Toolbar-->
                </div>
            </div>
            <!--end::Items-->

            <!--begin::Preview template (hidden)-->
            <div class="d-none" id="kt_dropzone_preview_template_{{ str_replace(['[', ']', '.'], '_', $name) }}">
                <div class="dropzone-item">
                    <!--begin::File-->
                    <div class="dropzone-file">
                        <div class="dropzone-filename">
                            <span data-dz-name></span>
                            <strong>(<span data-dz-size></span>)</strong>
                        </div>
                        <div class="dropzone-error" data-dz-errormessage></div>
                    </div>
                    <!--end::File-->

                    <!--begin::Thumbnail-->
                    <div class="dropzone-thumbnail" data-dz-thumbnail-wrapper style="display: none;">
                        <img data-dz-thumbnail />
                    </div>
                    <!--end::Thumbnail-->

                    <!--begin::Progress-->
                    <div class="dropzone-progress">
                        <div class="progress">
                            <div class="progress-bar bg-primary" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" data-dz-uploadprogress></div>
                        </div>
                    </div>
                    <!--end::Progress-->

                    <!--begin::Toolbar-->
                    <div class="dropzone-toolbar">
                        <span class="dropzone-delete" data-dz-remove><i class="bi bi-x fs-1"></i></span>
                    </div>
                    <!--end::Toolbar-->
                </div>
            </div>
            <!--end::Preview template-->
        </div>
        <!--end::Dropzone-->

        <!--begin::Hidden input for value-->
        <input type="hidden" name="{{ $name }}{{ $multiple ? '[]' : '' }}" id="{{ $name }}_input" value="{{ $value }}" {{ $required ? 'required' : '' }}>
        <!--end::Hidden input-->

        <!--begin::Hint-->
        @if ($description)
            <span class="form-text text-muted">{{ $description }}</span>
        @endif
        <!--end::Hint-->

        <!--begin::Error-->
        @error($name)
            <div class="invalid-feedback d-block">
                <strong>{{ $message }}</strong>
            </div>
        @enderror
        <!--end::Error-->
    </div>
    <!--end::Col-->
</div>
<!--end::Input group-->

@once
    @push('styles')
    <link href="{{ asset('metronic/plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css" />
    <style>
        .dropzone-item {
            display: flex;
            align-items: center;
            margin-top: 0.75rem;
            padding: 0.5rem 1rem;
            background-color: #f5f8fa;
            border-radius: 0.475rem;
        }
        .dropzone-file {
            flex-grow: 1;
        }
        .dropzone-thumbnail {
            margin-left: 0.5rem;
            margin-right: 0.5rem;
        }
        .dropzone-thumbnail img {
            max-width: 50px;
            max-height: 50px;
            border-radius: 0.475rem;
        }
    </style>
    @endpush

    @push('scripts')
    <script src="{{ asset('metronic/plugins/global/plugins.bundle.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Dropzone elements
            const elements = document.querySelectorAll('[data-kt-dropzonejs="true"]');

            elements.forEach(element => {
                if (element.getAttribute("data-kt-initialized") === "1") {
                    return;
                }

                const dropzoneId = element.id;
                const inputId = element.getAttribute('data-kt-dropzone-name') + '_input';
                const inputElement = document.getElementById(inputId);
                const multiple = element.getAttribute('data-kt-dropzone-multiple') === 'true';
                const maxFiles = parseInt(element.getAttribute('data-kt-dropzone-max-files')) || 10;
                const maxFilesize = parseInt(element.getAttribute('data-kt-dropzone-max-size')) || 10;
                const acceptedFiles = element.getAttribute('data-kt-dropzone-accepted-files') || null;
                const uploadUrl = element.getAttribute('data-kt-dropzone-upload-url') || null;
                const deleteUrl = element.getAttribute('data-kt-dropzone-delete-url') || null;
                const required = element.getAttribute('data-kt-dropzone-required') === 'true';

                // Initialize dropzone
                const dropzoneOptions = {
                    url: uploadUrl || '#', // Use a dummy URL if no upload URL is provided
                    autoProcessQueue: false, // Don't process queue automatically
                    // For client-side only mode, we need to handle file reading ourselves
                    uploadMultiple: false,
                    createImageThumbnails: true,
                    paramName: element.getAttribute('data-kt-dropzone-name') || 'file',
                    maxFiles: multiple ? maxFiles : 1,
                    maxFilesize: maxFilesize,
                    acceptedFiles: acceptedFiles,
                    addRemoveLinks: false,
                    previewsContainer: element.querySelector('.dropzone-items'),
                    previewTemplate: document.getElementById('kt_dropzone_preview_template_' + dropzoneId.replace('kt_dropzonejs_', '')).querySelector('.dropzone-item').outerHTML,
                    clickable: element.querySelector('.dropzone-select'),
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    },
                    init: function() {
                        const dropzone = this;
                        const element = dropzone.element;

                        // Only process queue if upload URL is provided and user clicks upload
                        if (uploadUrl) {
                            // Add upload button if needed
                            const uploadButton = document.createElement('a');
                            uploadButton.className = 'dropzone-upload btn btn-sm btn-light-primary mx-2';
                            uploadButton.innerHTML = 'Upload';
                            element.querySelector('.dropzone-panel').appendChild(uploadButton);

                            uploadButton.addEventListener('click', function() {
                                dropzone.processQueue();
                            });
                        }

                        // Set existing files if available
                        if (inputElement && inputElement.value) {
                            try {
                                const existingFiles = JSON.parse(inputElement.value);
                                if (Array.isArray(existingFiles)) {
                                    existingFiles.forEach(function(file) {
                                        // Create the mock file
                                        const mockFile = {
                                            name: file.name,
                                            size: file.size,
                                            accepted: true,
                                            kind: 'image',
                                            dataURL: file.url,
                                            upload: {
                                                uuid: file.id || Math.random().toString(36).substring(2)
                                            }
                                        };

                                        // Call the default addedfile event handler
                                        dropzone.emit("addedfile", mockFile);

                                        // And optionally show the thumbnail of the file
                                        if (file.url) {
                                            dropzone.emit("thumbnail", mockFile, file.url);
                                        }

                                        // Make sure that there is no progress bar, etc...
                                        dropzone.emit("complete", mockFile);

                                        // If you use the maxFiles option, make sure you adjust it
                                        if (maxFiles && dropzone.files.length > maxFiles) {
                                            dropzone.removeFile(mockFile);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.error('Error parsing existing files', e);
                            }
                        }

                        // Setup remove all button
                        const removeAllButton = element.querySelector('.dropzone-remove-all');
                        if (removeAllButton) {
                            removeAllButton.addEventListener('click', function() {
                                dropzone.removeAllFiles(true);
                            });
                        }

                        // Update hidden input when files change
                        const updateInput = function() {
                            if (inputElement) {
                                const files = dropzone.files.map(file => {
                                    return {
                                        name: file.name,
                                        size: file.size,
                                        type: file.type,
                                        id: file.upload.uuid,
                                        url: file.dataURL || file.url || null
                                    };
                                });

                                if (multiple) {
                                    inputElement.value = JSON.stringify(files);
                                } else if (files.length > 0) {
                                    inputElement.value = JSON.stringify(files[0]);
                                } else {
                                    inputElement.value = '';
                                }

                                // Trigger change event
                                const event = new Event('change', { bubbles: true });
                                inputElement.dispatchEvent(event);

                                // Update required validation
                                if (required) {
                                    if (files.length === 0) {
                                        inputElement.setAttribute('required', 'required');
                                    } else {
                                        inputElement.removeAttribute('required');
                                    }
                                }
                            }
                        };

                        // Update on file add/remove
                        this.on("addedfile", function(file) {
                            // Show the file item
                            file.previewElement.style.display = '';

                            // If we're not uploading to server, read the file as data URL
                            if (!uploadUrl && file instanceof File) {
                                const reader = new FileReader();
                                reader.onload = function(event) {
                                    file.dataURL = event.target.result;
                                    updateInput();
                                };
                                reader.readAsDataURL(file);
                            } else {
                                updateInput();
                            }
                        });

                        this.on("removedfile", updateInput);

                        // Handle thumbnails for images
                        this.on("thumbnail", function(file, dataUrl) {
                            // Find the thumbnail wrapper
                            const thumbnailWrapper = file.previewElement.querySelector('[data-dz-thumbnail-wrapper]');
                            if (thumbnailWrapper) {
                                thumbnailWrapper.style.display = 'block';
                            }
                        });

                        this.on("success", function(file, response) {
                            // Clear any error messages
                            const errorElement = file.previewElement.querySelector('[data-dz-errormessage]');
                            if (errorElement) {
                                errorElement.textContent = '';
                            }

                            if (response && response.id) {
                                file.upload.uuid = response.id;
                                file.url = response.url;
                                updateInput();
                            }
                        });

                        // Handle errors properly
                        this.on("error", function(file, errorMessage, xhr) {
                            const errorElement = file.previewElement.querySelector('[data-dz-errormessage]');
                            if (errorElement) {
                                // If errorMessage is an object, convert it to a string
                                if (typeof errorMessage === 'object') {
                                    try {
                                        errorMessage = JSON.stringify(errorMessage);
                                    } catch (e) {
                                        errorMessage = 'Upload failed';
                                    }
                                }
                                errorElement.textContent = errorMessage;
                            }
                        });

                        // Handle file removal with server request
                        this.on("removedfile", function(file) {
                            if (deleteUrl && file.upload && file.upload.uuid) {
                                fetch(deleteUrl, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                                    },
                                    body: JSON.stringify({
                                        id: file.upload.uuid
                                    })
                                }).catch(error => {
                                    console.error('Error deleting file:', error);
                                });
                            }
                        });
                    }
                };

                // Initialize Dropzone
                new Dropzone(element, dropzoneOptions);

                element.setAttribute("data-kt-initialized", "1");
            });
        });
    </script>
    @endpush
@endonce
