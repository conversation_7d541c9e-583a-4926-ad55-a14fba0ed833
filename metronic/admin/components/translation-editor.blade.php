<div class="card shadow-none mb-7">
    @if($label)
    <div class="card-header">
        <h3 class="card-title {{$required ? 'required':''}}">
            {{ $label }}
        </h3>
    </div>
    @endif

    <!-- Tab Headers -->
    <div class="nav-tabs-wrapper">
        <ul class="nav nav-tabs p-3 gap-2" id="translation-tab-{{ $tabId }}" role="tablist">
            @foreach($languages as $index => $language)
                <li class="nav-item" role="presentation">
                    <button
                        class="nav-link rounded-1 {{ $index === 0 ? 'active' : '' }} {{ isset($activeLanguages[$language]) ? 'has-error' : '' }}"
                        id="{{ $language }}-tab-{{ $tabId }}"
                        data-bs-toggle="tab"
                        data-bs-target="#{{ $language }}-pane-{{ $tabId }}"
                        type="button"
                        role="tab"
                        aria-controls="{{ $language }}-pane-{{ $tabId }}"
                        aria-selected="{{ $index === 0 ? 'true' : 'false' }}"
                    >
                        {{ $language === 'en' ? 'English 🇺🇸' : 'Arabic 🇸🇦' }}
                        @if(isset($activeLanguages[$language]))
                            <i class="fas fa-exclamation-circle ms-1 text-danger"></i>
                        @endif
                    </button>
                </li>
            @endforeach
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content p-3" id="translation-tab-content-{{ $tabId }}">
        @foreach($languages as $index => $language)
            <div
                class="tab-pane fade {{ $index === 0 ? 'show active' : '' }}"
                id="{{ $language }}-pane-{{ $tabId }}"
                role="tabpanel"
                aria-labelledby="{{ $language }}-tab-{{ $tabId }}"
                tabindex="0"
            >
                <div class="translation-fields-container">
                    @php
                        $inputName = $name . '[' . $language . ']';
                        $errorKey = $name . '.' . $language;
                        $placeholderText = $placeholder ?? 'Enter ' . ucwords(str_replace('_', ' ', $name)) . ' (' . ($language === 'en' ? 'English' : 'Arabic') . ')';
                        $oldValue = old($errorKey);
                        $modelValue = null;

                        if ($model) {
                            try {
                                $modelValue = $model->getTranslation($name, $language, false);
                            } catch (\Exception $e) {
                                $modelValue = null;
                            }
                        }

                        $value = $oldValue !== null ? $oldValue : $modelValue;
                        $hasError = isset($activeLanguages[$language]) && $errors && $errors->has($errorKey);
                        $editorId = "editor_{$name}_{$language}_{$tabId}";
                    @endphp

                    <div class="form-field mb-5">
                        <!-- Editor Container -->
                        <div
                            id="{{ $editorId }}"
                            class="min-h-{{ $minHeight }} mb-2 @error($errorKey) is-invalid @enderror"
                            data-kt-translation-editor="true"
                            data-kt-editor-toolbar="{{ $toolbar }}"
                            data-kt-editor-readonly="{{ $readonly ? 'true' : 'false' }}"
                        ></div>

                        <!-- Hidden input to store editor content for form submission -->
                        <input
                            type="hidden"
                            name="{{ $inputName }}"
                            id="{{ $editorId }}_input"
                            value="{{ $value }}"
                            placeholder="{{ $placeholderText }}"
                            {{ $required ? 'required' : '' }}
                        >

                        @error($errorKey)
                            <div class="invalid-feedback d-block">
                                <strong>The {{ strtolower(ucfirst($name)) }} field is required.</strong>
                            </div>
                        @enderror
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    @if ($description)
        <div class="card-footer">
            <div class="text-muted fs-7">{{ $description }}</div>
        </div>
    @endif
</div>

<style>
    .translation-wrapper {
        border: 1px solid #e0e0e0;
        border-radius: 0.5rem !important;
        background-color: #fff;
    }

    .translation-wrapper .nav-tabs {
        border-bottom: 1px solid #e0e0e0;
    }

    .translation-wrapper .nav-link {
        color: #5e6278;
        border: none;
        border-radius: 0.25rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }

    .translation-wrapper .nav-link.active {
        background-color: #212121;
        color: #ffffff;
    }

    .translation-wrapper .nav-link.has-error {
        color: #f1416c;
    }

    .translation-wrapper .tab-content {
        padding: 1rem;
    }

    .translation-wrapper .form-control {
        border: 1px solid #e0e0e0;
        background-color: #f9f9f9;
    }

    .translation-wrapper .form-control:focus {
        background-color: #ffffff;
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.075) !important;
    }

    .translation-wrapper .form-field {
        display: flex;
        flex-direction: column;
        align-items: start;
    }

    .translation-wrapper .form-field .form-control {
        width: 100%;
    }

    .nav-tabs .nav-link.active{
        border-color: var(--bs-nav-tabs-link-hover-border-color);
        color: var(--bs-nav-link-color);
    }

    .nav-link {
        color: var(--bs-nav-tabs-link-active-color);
    }

    /* Editor specific styles */
    .ql-editor {
        min-height: v-bind('minHeight');
        direction: ltr;
    }

    [dir="rtl"] .ql-editor {
        direction: rtl;
        text-align: right;
    }

    .ql-snow .ql-toolbar {
        border-top-left-radius: 0.475rem;
        border-top-right-radius: 0.475rem;
    }

    .ql-snow .ql-container {
        border-bottom-left-radius: 0.475rem;
        border-bottom-right-radius: 0.475rem;
    }
</style>

@once
    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs with validation errors
            @if (!empty($activeLanguages))
                // Set a timeout to ensure Bootstrap has initialized the tabs
                setTimeout(function() {
                    @foreach ($languages as $language)
                        @if (isset($activeLanguages[$language]))
                            document.getElementById('{{ $language }}-tab-{{ $tabId }}').click();
                            // Only need to click the first tab with an error
                            @break
                        @endif
                    @endforeach
                }, 100);
            @endif

            // Initialize Quill editors
            const elements = document.querySelectorAll('[data-kt-translation-editor="true"]');

            elements.forEach(element => {
                if (element.getAttribute("data-kt-initialized") === "1") {
                    return;
                }

                const editorId = element.id;
                const inputId = editorId + '_input';
                const inputElement = document.getElementById(inputId);
                const toolbarType = element.getAttribute('data-kt-editor-toolbar') || 'full';
                const readOnly = element.getAttribute('data-kt-editor-readonly') === 'true';

                // Check if this is an Arabic editor
                const isRtl = editorId.includes('_ar_');

                // Define toolbar options based on type
                let toolbarOptions;

                if (toolbarType === 'basic') {
                    toolbarOptions = [
                        ['bold', 'italic', 'underline'],
                        ['clean']
                    ];
                } else if (toolbarType === 'minimal') {
                    toolbarOptions = [
                        ['bold', 'italic'],
                        ['clean']
                    ];
                } else {
                    // Full toolbar
                    toolbarOptions = [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': isRtl ? 'rtl' : 'ltr' }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'font': [] }],
                        [{ 'align': [] }],
                        ['clean'],
                        ['link', 'image', 'video']
                    ];
                }

                // Initialize Quill with RTL support for Arabic
                const quill = new Quill(`#${editorId}`, {
                    modules: {
                        toolbar: toolbarOptions
                    },
                    placeholder: inputElement ? inputElement.getAttribute('placeholder') || '' : '',
                    theme: 'snow',
                    readOnly: readOnly,
                    direction: isRtl ? 'rtl' : 'ltr'
                });

                // Set initial content
                if (inputElement && inputElement.value) {
                    quill.root.innerHTML = inputElement.value;
                }

                // Update hidden input on text-change
                quill.on('text-change', function() {
                    if (inputElement) {
                        inputElement.value = quill.root.innerHTML;
                    }
                });

                element.setAttribute("data-kt-initialized", "1");
            });
        });
    </script>
    @endpush
@endonce
