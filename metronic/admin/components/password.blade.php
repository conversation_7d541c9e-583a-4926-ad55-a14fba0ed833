<!--begin::Main wrapper-->
<div class="fv-row mb-7 fv-plugins-icon-container" data-kt-password-meter="true">
    <!--begin::Wrapper-->
    <div class="mb-1">

        <!--begin::Label-->
        @if ($label)
            <label class="form-label {{$required ? 'required':''}} " for="{{ $name }}">{{ $label }}</label>
        @endif
        <!--end::Label-->

        <!--begin::Input wrapper-->
        <div class="position-relative mb-3">
            <input
                class="form-control form-control-solid mb-3 mb-lg-0 {{ $classes }} @error($name) is-invalid @enderror"
                type="password"
                name="{{ $name }}"
                id="{{ $name }}"
                placeholder="{{ $placeholder }}"
                value="{{ (isset($model)) ? $model->{$name}: old($name, $value) }}"
                autocomplete="off"
            />

            <!--begin::Visibility toggle-->
            <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2"
                  data-kt-password-meter-control="visibility">
                    <i class="ki-duotone ki-eye-slash fs-1"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span></i>
                    <i class="ki-duotone ki-eye d-none fs-1"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>
            </span>
            <!--end::Visibility toggle-->
        </div>
        <!--end::Input wrapper-->

        <!--begin::Highlight meter-->
        <div class="d-flex align-items-center mb-3" data-kt-password-meter-control="highlight">
            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
        </div>
        <!--end::Highlight meter-->
    </div>
    <!--end::Wrapper-->

    <!--begin::Hint-->
    <div class="text-muted">
        {{ $description }}
    </div>
    <!--end::Hint-->
</div>
<!--end::Main wrapper-->
