<div class="fv-row mb-7 fv-plugins-icon-container">
    @if ($label)
        <label class="form-label fw-semibold fs-6 mb-2 {{$required ? 'required':''}} " for="{{ $name }}">{{ $label }}</label>
    @endif
{{--@dd($errors)--}}
    <input
        type="{{ $type }}"
        name="{{ $name }}"
        id="{{ $name }}"
        value="{{ (isset($model)) ? $model->{$name}: old($name, $value) }}"
        class="form-control form-control-solid mb-3 mb-lg-0 {{ $classes }} @error($name) is-invalid @enderror"
        placeholder="{{ $placeholder }}"
        {{ $required ? 'required' : '' }}
        {{ $attributes }}
    >

    @error($name)
    <span class="invalid-feedback" role="alert">
            <strong>{{ $message }}</strong>
        </span>
    @enderror
</div>
