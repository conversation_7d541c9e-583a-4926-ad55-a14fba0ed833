<div>
  <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-table-toolbar="column-visibility">
    <i class="ki-outline ki-eye fs-2"></i>Columns
  </button>
  <!--begin::Column Visibility Menu-->
  <div class="menu menu-sub menu-sub-dropdown w-auto" data-kt-menu="true" id="kt_datatable_column_visibility_dropdown">
    <!--begin::Header-->
    <div class="p-5">
      <div class="fs-5 text-gray-900 fw-bold">Column Visibility</div>
    </div>
    <!--end::Header-->
    <!--begin::Separator-->
    <div class="separator border-gray-200"></div>
    <!--end::Separator-->
    <!--begin::Content-->
    <div class="p-5 column-container">
      <!-- Column visibility checkboxes will be dynamically added here -->
    </div>
    <!--end::Content-->
  </div>
</div>

