<!--begin::Filter-->
<button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
  <i class="ki-outline ki-filter fs-2"></i>Filter</button>
<!--begin::Menu 1-->
<div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true">
  <!--begin::Header-->
  <div class="px-7 py-5">
    <div class="fs-5 text-gray-900 fw-bold">Filter Options</div>
  </div>
  <!--end::Header-->
  <!--begin::Separator-->
  <div class="separator border-gray-200"></div>
  <!--end::Separator-->
  <!--begin::Content-->
  <div class="px-7 py-5" data-kt-table-filter="form">
    @if(isset($columnFilters))
    @foreach($columnFilters as $column => $config)
    <!--begin::Input group-->
    <div class="mb-10 filter_item">
      <label class="form-label fs-6 fw-semibold">{{ $config['label'] ?? ucfirst(str_replace('_', ' ', $column)) }}:</label>
      <div class="d-flex flex-column">
        @if (isset($config['filter']))
        <select class="form-select form-select-solid fw-bold mb-3" data-kt-select2="true"
          data-placeholder="Select operator" data-allow-clear="true"
          data-kt-table-filter="operator" data-column="{{ $column }}" data-hide-search="true">
          <option></option>
          @foreach($config['filter'] as $label => $value)
          <option value="{{ $value }}">{{ $label }}</option>
          @endforeach
        </select>
        @endif
        <input type="text" class="form-control form-control-solid"
          data-kt-table-filter="value" data-column="{{ $column }}"
          placeholder="Enter value">
      </div>
    </div>
    <!--end::Input group-->
    @endforeach
    @endif

    <!--begin::Actions-->
    <div class="d-flex justify-content-end">
      <button type="reset" class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6" data-kt-menu-dismiss="true" data-kt-table-filter="reset">Reset</button>
      <button type="submit" class="btn btn-primary fw-semibold px-6" data-kt-menu-dismiss="true" data-kt-table-filter="filter">Apply</button>
    </div>
    <!--end::Actions-->
  </div>
  <!--end::Content-->
</div>
<!--end::Menu 1-->
<!--end::Filter-->