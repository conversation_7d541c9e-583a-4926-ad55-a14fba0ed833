<div class="card shadow-none mb-7">
    <!-- Tab Headers -->
    <div class="nav-tabs-wrapper">
        <ul class="nav nav-tabs p-3 gap-2" id="translation-tab-{{ $tabId }}" role="tablist">
            @foreach($languages as $index => $language)
                <li class="nav-item" role="presentation">
                    <button
                        class="nav-link rounded-1 {{ $index === 0 ? 'active' : '' }} {{ isset($activeLanguages[$language]) ? 'has-error' : '' }}"
                        id="{{ $language }}-tab-{{ $tabId }}"
                        data-bs-toggle="tab"
                        data-bs-target="#{{ $language }}-pane-{{ $tabId }}"
                        type="button"
                        role="tab"
                        aria-controls="{{ $language }}-pane-{{ $tabId }}"
                        aria-selected="{{ $index === 0 ? 'true' : 'false' }}"
                    >
                        {{ $language === 'en' ? 'English 🇺🇸' : 'Arabic 🇸🇦' }}
                        @if(isset($activeLanguages[$language]))
                            <i class="fas fa-exclamation-circle ms-1 text-danger"></i>
                        @endif
                    </button>
                </li>
            @endforeach
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content p-3" id="translation-tab-content-{{ $tabId }}">
        @foreach($languages as $index => $language)
            <div
                class="tab-pane fade {{ $index === 0 ? 'show active' : '' }}"
                id="{{ $language }}-pane-{{ $tabId }}"
                role="tabpanel"
                aria-labelledby="{{ $language }}-tab-{{ $tabId }}"
                tabindex="0"
            >
                <div class="translation-fields-container">
                    @foreach($keys as $key)
                        @php
                            $inputName = $key . '[' . $language . ']';
                            $errorKey = $key . '.' . $language;
                            $placeholder = 'Enter ' . ucwords(str_replace('_', ' ', $key)) . ' (' . ($language === 'en' ? 'English' : 'Arabic') . ')';
                            $oldValue = old($errorKey);
                            $modelValue = null;

                            if ($model) {
                                try {
                                    $modelValue = $model->getTranslation($key, $language, false);
                                } catch (\Exception $e) {
                                    $modelValue = null;
                                }
                            }

                            $value = $oldValue !== null ? $oldValue : $modelValue;
                            $hasError = isset($activeLanguages[$language]) && $errors && $errors->has($errorKey);
                            $isTextarea = in_array($key, $textareaFields);
                        @endphp

                        <div class="form-field">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <label class="form-label fw-bold mb-0 {{$required ? 'required':''}}">{{ ucwords(str_replace('_', ' ', $key)) }}</label>
                                @if($hasError)
                                    <div class="error-icon">
                                        <i class="fas fa-exclamation-circle text-danger"></i>
                                    </div>
                                @endif
                            </div>

                            @if($isTextarea)
                                <textarea
                                    name="{{ $inputName }}"
                                    class="form-control form-control-solid rounded-3 {{ $hasError ? 'is-invalid' : '' }}"
                                    placeholder="{{ $placeholder }}"
                                    rows="4"
                                >{{ $value }}</textarea>
                            @else
                                <div class="fv-row fv-plugins-icon-container">
                                    <input type="text"
                                        name="{{ $inputName }}"
                                        class="form-control form-control-solid mb-3 mb-lg-0 {{ $hasError ? 'is-invalid' : '' }}"
                                        placeholder="{{ $placeholder }}"
                                        value="{{ $value }}"
                                        {{ $required ? 'required' : '' }}
                                    />
                                    @error($errorKey)
                                        <div class="invalid-feedback" >
                                         <strong>The {{ strtolower(ucfirst($key)) }} field is required.</strong>
                                        </div>
                                    @enderror
                                </div>
                            @endif


                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach
    </div>
</div>

<style>
    .translation-wrapper {
        border: 1px solid #e0e0e0;
        border-radius: 0.5rem !important;
        background-color: #fff;
    }

    .translation-wrapper .nav-tabs {
        border-bottom: 1px solid #e0e0e0;
    }

    .translation-wrapper .nav-link {
        color: #5e6278;
        border: none;
        border-radius: 0.25rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }

    .translation-wrapper .nav-link.active {
        background-color: #212121;
        color: #ffffff;
    }

    .translation-wrapper .nav-link.has-error {
        color: #f1416c;
    }

    .translation-wrapper .tab-content {
        padding: 1rem;
    }

    .translation-wrapper .form-control {
        border: 1px solid #e0e0e0;
        background-color: #f9f9f9;
    }

    .translation-wrapper .form-control:focus {
        background-color: #ffffff;
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.075) !important;
    }

    .translation-wrapper .form-field {
        display: flex;
        flex-direction: column;
        align-items: start;
    }

    .translation-wrapper .form-field .form-control {
        width: 100%;
    }

    .nav-tabs .nav-link.active{
        border-color: var(--bs-nav-tabs-link-hover-border-color);
        color: var(--bs-nav-link-color);
    }

    .nav-link {
        color: var(--bs-nav-tabs-link-active-color);
    }
</style>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // This ensures that the tab with validation errors is shown on page load
        @if (!empty($activeLanguages))
            // Set a timeout to ensure Bootstrap has initialized the tabs
            setTimeout(function() {
                @foreach ($languages as $language)
                    @if (isset($activeLanguages[$language]))
                        document.getElementById('{{ $language }}-tab-{{ $tabId }}').click();
                        // Only need to click the first tab with an error
                        @break
                    @endif
                @endforeach
            }, 100);
        @endif
    });
</script>
@endpush
