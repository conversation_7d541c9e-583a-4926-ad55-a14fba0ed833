<div class="fv-row w-100 flex-md-root">
    @if ($label)
        <label class="{{ $required ? 'required' : '' }} fs-6 fw-semibold mb-2" for="{{ $name }}">{{ $label }}</label>
    @endif
    <!--begin::Input-->
    <div class="position-relative d-flex align-items-center">
        <!--begin::Icon-->
        <div class="symbol symbol-20px me-4 position-absolute ms-4">
            <span class="symbol-label {{ $iconBgClass }}">
                <i class="{{ $icon }}">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                </i>
            </span>
        </div>
        <!--end::Icon-->
        <!--begin::Datepicker-->
        <input
            class="form-control form-control-solid ps-12 {{ $classes }} @error($name) is-invalid @enderror"
            placeholder="{{ $placeholder }}"
            name="{{ $name }}"
            id="{{ $name }}"
            value="{{ (isset($model)) ? $model->{$name} : old($name, $value) }}"
            {{ $required ? 'required' : '' }}
            {{ $attributes }}
            data-kt-date="{{ $enableTime ? 'true' : 'false' }}"
            data-kt-date-format="{{ $format }}"
            @if($minDate) data-kt-date-min="{{ $minDate }}" @endif
            @if($maxDate) data-kt-date-max="{{ $maxDate }}" @endif
        />
        <!--end::Datepicker-->
    </div>
    <!--end::Input-->

    @if ($description)
        <div class="text-muted fs-7 mt-1">{{ $description }}</div>
    @endif

    @error($name)
        <span class="invalid-feedback" role="alert">
            <strong>{{ $message }}</strong>
        </span>
    @enderror
</div>

@once
    @push('scripts')
    <script>
        // Initialize Flatpickr datepickers
        document.addEventListener("DOMContentLoaded", function() {
            const elements = document.querySelectorAll('[data-kt-date]');

            elements.forEach(element => {
                if (element.getAttribute("data-kt-initialized") === "1") {
                    return;
                }

                const enableTime = element.getAttribute('data-kt-date') === 'true';
                const format = element.getAttribute('data-kt-date-format') || 'Y-m-d';
                const minDate = element.getAttribute('data-kt-date-min') || null;
                const maxDate = element.getAttribute('data-kt-date-max') || null;

                flatpickr(element, {
                    enableTime: enableTime,
                    dateFormat: format,
                    minDate: minDate,
                    maxDate: maxDate,
                    allowInput: true
                });

                element.setAttribute("data-kt-initialized", "1");
            });
        });
    </script>
    @endpush
@endonce
