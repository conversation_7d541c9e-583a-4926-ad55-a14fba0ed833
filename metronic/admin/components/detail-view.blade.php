<div class="card">
    <div class="card-header border-0 pt-6">
        <div class="card-title">
            <h2>{{ $title ?? ucfirst($modelName) . ' Details' }}</h2>
        </div>
        <div class="card-toolbar">
            <a href="{{ $backRoute }}" class="btn btn-light-primary">
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.5657 11.9343L10.5657 9.46876C10.2533 9.20865 9.74673 9.20865 9.43431 9.46876C9.12189 9.72887 9.12189 10.1524 9.43431 10.4125L13.4343 13.8482C13.7467 14.1083 14.2533 14.1083 14.5657 13.8482L18.5657 10.4125C18.8781 10.1524 18.8781 9.72887 18.5657 9.46876C18.2533 9.20865 17.7467 9.20865 17.4343 9.46876L14.4343 11.9343C14.1967 12.1401 13.8033 12.1401 13.5657 11.9343Z" fill="currentColor"/>
                    </svg>
                </span>
                Back to {{ ucfirst($modelNamePlural) }}
            </a>
        </div>
    </div>
    <div class="card-body pt-0">
        <div class="row mb-6">
            <div class="col-lg-12">
                <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
                    <tbody>
                    @foreach($fields as $field)
                        <tr class="fw-bold text-muted">
                            <td class="w-25">{{ $field['label'] }}:</td>
                            <td>
                                @if(isset($field['type']) && $field['type'] === 'boolean')
                                    @if($item->{$field['name']})
                                        <div class="badge badge-light-success">{{ $field['trueLabel'] ?? 'Active' }}</div>
                                    @else
                                        <div class="badge badge-light-danger">{{ $field['falseLabel'] ?? 'Inactive' }}</div>
                                    @endif
                                @elseif(isset($field['type']) && $field['type'] === 'translation')
                                    {{ $item->getTranslation($field['name'], $field['locale'], false) }}
                                @elseif(isset($field['type']) && $field['type'] === 'custom')
                                    {!! $field['value']($item) !!}
                                @else
                                    {{ $item->{$field['name']} }}
                                @endif
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        <div class="d-flex justify-content-end">
            @if($editRoute)
                <a href="{{ $editRoute }}" class="btn btn-primary">
                    Edit
                </a>
            @endif

            @if($deleteRoute)
                <form action="{{ $deleteRoute }}" method="POST" class="ms-2">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this item?')">
                        Delete
                    </button>
                </form>
            @endif

            @if($slot->isNotEmpty())
                <div class="ms-2">
                    {{ $slot }}
                </div>
            @endif
        </div>
    </div>
</div>
