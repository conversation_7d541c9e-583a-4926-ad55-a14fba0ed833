<div class="fv-row w-100 flex-md-root">

    <!--begin::Label-->
    @if ($label)
        <label class="form-label {{$required ? 'required':''}} " for="{{ $name }}">{{ $label }}</label>
    @endif
    <!--end::Label-->

    <!--begin::Select2 Multiselect-->
    <select
        class="form-select mb-2 form-select-solid {{ $classes }} @error($name) is-invalid @enderror"
        name="{{ $name }}[]" {{-- Add [] to name for array submission --}}


        data-placeholder="{{ $placeholder }}"
        data-allow-clear="{{ $allowClear ? 'true' : 'false' }}"
        data-close-on-select="{{ $closeOnSelect ? 'true' : 'false' }}"
        {{ $taggable ? 'data-tags="true"' : '' }}
        multiple="multiple"
        {{ $required ? 'required' : '' }}
        {{ $attributes }}
        data-kt-repeater="select2"
    >
        @foreach($options as $key => $label)
            <option
                value="{{ $key }}" {{ in_array($key, old($name, $value) ?? []) ? 'selected' : '' }}
            >
                {{ $label }}
            </option>
        @endforeach
    </select>
    <!--end::Select2 Multiselect-->

    <!--begin::Description-->
    @if ($description)
        <div class="text-muted fs-7">{{ $description }}</div>
    @endif
    <!--end::Description-->

    @error($name)
    <span class="invalid-feedback" role="alert">
        <strong>{{ $message }}</strong>
    </span>
    @enderror

</div>
