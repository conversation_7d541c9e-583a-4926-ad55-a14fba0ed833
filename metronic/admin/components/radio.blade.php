@if(empty($options))
    {{-- Single Radio Button --}}
    <div class="fv-row mb-5">
        <div class="form-check form-check-custom form-check-solid {{ $inline ? 'd-inline-block me-5' : '' }}">
            <input 
                class="form-check-input {{ $classes }} @error($name) is-invalid @enderror" 
                type="radio" 
                name="{{ $name }}" 
                id="{{ $name }}_{{ \Illuminate\Support\Str::slug($value) }}" 
                value="{{ $value }}"
                {{ (old($name, $checked) || (isset($model) && $model->{$name} == $value)) ? 'checked' : '' }}
                {{ $required ? 'required' : '' }}
                {{ $attributes }}
            >
            @if ($label)
                <label class="form-check-label {{ $required ? 'required' : '' }}" for="{{ $name }}_{{ \Illuminate\Support\Str::slug($value) }}">
                    {{ $label }}
                </label>
            @endif
        </div>
        
        @if ($description)
            <div class="text-muted fs-7 mt-1">{{ $description }}</div>
        @endif
        
        @error($name)
            <span class="invalid-feedback" role="alert">
                <strong>{{ $message }}</strong>
            </span>
        @enderror
    </div>
@else
    {{-- Radio Button Group --}}
    <div class="fv-row mb-7">
        @if ($label)
            <label class="form-label fw-semibold fs-6 mb-2 {{ $required ? 'required' : '' }}">{{ $label }}</label>
        @endif
        
        <div class="{{ $inline ? 'd-flex flex-wrap' : '' }}">
            @foreach($options as $optionValue => $optionLabel)
                <div class="form-check form-check-custom form-check-solid {{ $inline ? 'me-5 mb-2' : 'mb-2' }}">
                    <input 
                        class="form-check-input {{ $classes }} @error($name) is-invalid @enderror" 
                        type="radio" 
                        name="{{ $name }}" 
                        id="{{ $name }}_{{ \Illuminate\Support\Str::slug($optionValue) }}" 
                        value="{{ $optionValue }}"
                        {{ (old($name, $value) == $optionValue || (isset($model) && $model->{$name} == $optionValue)) ? 'checked' : '' }}
                        {{ $required ? 'required' : '' }}
                        {{ $attributes }}
                    >
                    <label class="form-check-label" for="{{ $name }}_{{ \Illuminate\Support\Str::slug($optionValue) }}">
                        {{ $optionLabel }}
                    </label>
                </div>
            @endforeach
        </div>
        
        @if ($description)
            <div class="text-muted fs-7 mt-1">{{ $description }}</div>
        @endif
        
        @error($name)
            <span class="invalid-feedback d-block" role="alert">
                <strong>{{ $message }}</strong>
            </span>
        @enderror
    </div>
@endif
