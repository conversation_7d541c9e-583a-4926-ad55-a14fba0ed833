
@push('styles')
@if (app()->getLocale() === 'ar')
    <link href="{{  asset(config('app.admin_theme_path'). '/plugins/custom/datatables/datatables.bundle.rtl.css') }}" rel="stylesheet" type="text/css" />
@else
    <link href="{{  asset(config('app.admin_theme_path'). '/plugins/custom/datatables/datatables.bundle.css') }}" rel="stylesheet" type="text/css" />
@endif
@endpush

<div class="card">
    <!--begin::Card header-->
    <div class="card-header border-0 pt-6 d-none">
        <!--begin::Card title-->

        <!--begin::Card title-->
        <!--begin::Card toolbar-->
        <div class="card-toolbar">
            <!--begin::Toolbar-->

            <!--end::Toolbar-->
            <!--begin::Group actions-->
            <div class="d-flex justify-content-end align-items-center d-none" data-kt-user-table-toolbar="selected">
                <div class="fw-bold me-5">
                    <span class="me-2" data-kt-user-table-select="selected_count"></span>Selected
                </div>
                <button type="button" class="btn btn-danger" data-kt-user-table-select="delete_selected">Delete Selected</button>
            </div>
            <!--end::Group actions-->
        </div>
        <!--end::Card toolbar-->
    </div>
    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body py-4 pt-8">
        <!--begin::Table-->
        {{ $dataTable->table() }}
        <!--end::Table-->
    </div>
    <!--end::Card body-->
</div>

@push('scripts')
    <script src="{{ asset(config('app.admin_theme_path').'/js/general-data-table.js') }}"></script>
    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush
