@extends('theme::admin.layouts.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Tags Component Example</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="#">
            @csrf
            
            <!-- Default Style -->
            <x-theme::tags 
                name="default_tags"
                label="Default input style"
                value="tag1, tag2, tag3"
            />
            
            <!-- Solid Background Style -->
            <x-theme::tags 
                name="solid_tags"
                label="Solid background style"
                value="tag1, tag2, tag3"
                :solid="true"
            />
            
            <!-- With Description -->
            <x-theme::tags 
                name="tags_with_description"
                label="Tags with Description"
                value="feature, bug, enhancement"
                description="Add tags to categorize this item"
            />
            
            <!-- Required Tags -->
            <x-theme::tags 
                name="required_tags"
                label="Required Tags"
                :required="true"
                placeholder="This field is required"
            />
            
            <!-- Custom Classes -->
            <x-theme::tags 
                name="custom_tags"
                label="Custom Classes"
                value="custom, tags"
                classes="mt-3"
            />
            
            <!-- Form Actions -->
            <div class="d-flex justify-content-end mt-5">
                <button type="submit" class="btn btn-primary">
                    Submit
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
