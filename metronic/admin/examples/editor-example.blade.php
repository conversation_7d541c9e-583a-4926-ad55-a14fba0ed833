@extends('theme::admin.layouts.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Editor Component Example</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="#">
            @csrf
            
            <div class="row mb-8">
                <!-- Basic Editor -->
                <x-theme::editor 
                    name="product_description"
                    label="Product Description"
                    description="Set a description to the product for better visibility."
                    :required="true"
                />
            </div>
            
            <div class="row mb-8">
                <!-- Editor with Basic Toolbar -->
                <x-theme::editor 
                    name="short_description"
                    label="Short Description"
                    toolbar="basic"
                    minHeight="100px"
                    description="A brief summary that appears in product listings."
                />
            </div>
            
            <div class="row mb-8">
                <!-- Editor with Initial Content -->
                <x-theme::editor 
                    name="terms_conditions"
                    label="Terms and Conditions"
                    value="<h2>Terms of Service</h2><p>These are the default terms and conditions for using our service.</p><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul>"
                    description="Legal terms that users must agree to."
                />
            </div>
            
            <div class="row mb-8">
                <!-- Read-only Editor -->
                <x-theme::editor 
                    name="policy"
                    label="Privacy Policy"
                    value="<h3>Privacy Policy</h3><p>This is our standard privacy policy that cannot be edited.</p>"
                    :readonly="true"
                    description="This content is read-only and cannot be edited."
                />
            </div>
            
            <div class="row mb-8">
                <!-- Editor with Model -->
                @php
                    $product = (object) ['specifications' => '<h4>Product Specifications</h4><p>This product has the following specifications:</p><ul><li>Feature 1</li><li>Feature 2</li><li>Feature 3</li></ul>'];
                @endphp
                
                <x-theme::editor 
                    name="specifications"
                    label="Product Specifications"
                    :model="$product"
                    description="Technical details and specifications of the product."
                />
            </div>
            
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
@endpush

@push('scripts')
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
@endpush
