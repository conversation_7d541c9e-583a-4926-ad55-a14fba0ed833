@extends('theme::admin.layouts.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Advanced Repeater Component Example</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="#">
            @csrf

            <!-- Advanced Repeater with Select2, DatePicker, and Translation Editor -->
            <x-theme::repeater
                name="kt_docs_repeater_advanced"
                label="Advanced Repeater Example"
                description="This repeater includes select2, datepicker, and translation editor components"
                add-button-text="Add Item"
                delete-button-text="Delete"
            >
                <div class="col-md-3">
                    <x-theme::select
                        name="[select_option]"
                        label="Select Options:"
                        placeholder="Select an option"
                        :options="[
                            '1' => 'Option 1',
                            '2' => 'Option 2',
                            '3' => 'Option 3'
                        ]"
                    />
                </div>

                <div class="col-md-3">
                    <x-theme::date-picker
                        name="[event_date]"
                        label="Datepicker:"
                        placeholder="Pick a date"
                    />
                </div>

                <div class="col-md-4">
                    <label class="form-label">Tags:</label>
                    <input class="form-control" name="[tags]" data-kt-tagify value="tag1, tag2, tag3"/>
                </div>
            </x-theme::repeater>

            <!-- Repeater with Translation Editor -->
            <div class="mt-10">
                <h4 class="mb-5">Repeater with Translation Editor</h4>

                <x-theme::repeater
                    name="kt_docs_repeater_translations"
                    label="Translation Items"
                    description="Add multiple translated items"
                    add-button-text="Add Translation"
                >
                    <div class="col-md-10">
                        <x-theme::translation-editor
                            name="[description]"
                            label="Description"
                            :languages="['en', 'ar']"
                            minHeight="150px"
                            toolbar="basic"
                        />
                    </div>
                </x-theme::repeater>
            </div>

            <div class="d-flex justify-content-end mt-5">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset(config('app.admin_theme_path').'/plugins/custom/formrepeater/formrepeater.bundle.js') }}"></script>
@endpush
