@extends('theme::admin.layouts.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Translation Editor Component Example</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="#">
            @csrf

            <!-- Basic Translation Editor -->
            <div class="mb-10">
                <h4 class="mb-5">Basic Translation Editor</h4>
                <x-theme::translation-editor
                    name="description"
                    label="Product Description"
                    description="Set a description to the product for better visibility."
                    :required="true"
                />
            </div>

            <!-- Translation Editor with Custom Toolbar -->
            <div class="mb-10">
                <h4 class="mb-5">Translation Editor with Basic Toolbar</h4>
                <x-theme::translation-editor
                    name="summary"
                    label="Product Summary"
                    toolbar="basic"
                    minHeight="150px"
                    description="A brief summary that appears in product listings."
                />
            </div>

            <!-- Translation Editor with Custom Placeholder -->
            <div class="mb-10">
                <h4 class="mb-5">Translation Editor with Custom Placeholder</h4>
                <x-theme::translation-editor
                    name="features"
                    label="Product Features"
                    placeholder="List the key features of your product here..."
                    description="Highlight the most important features of your product."
                />
            </div>

            <!-- Translation Editor with Custom Languages (Array) -->
            <div class="mb-10">
                <h4 class="mb-5">Translation Editor with Custom Languages (Array)</h4>
                <x-theme::translation-editor
                    name="localized_content"
                    label="Localized Content"
                    :languages="['en', 'ar', 'fr']"
                    description="Content in multiple languages."
                />
            </div>

            <!-- Translation Editor with Custom Languages (String) -->
            <div class="mb-10">
                <h4 class="mb-5">Translation Editor with Custom Languages (String)</h4>
                <x-theme::translation-editor
                    name="multilingual_content"
                    label="Multilingual Content"
                    languages="en,ar,fr"
                    description="Content in multiple languages as comma-separated string."
                />
            </div>

            <!-- Translation Editor with Model -->
            <div class="mb-10">
                <h4 class="mb-5">Translation Editor with Model (Simulated)</h4>
                @php
                    $product = new class {
                        public function getTranslation($field, $locale, $fallback = true) {
                            $translations = [
                                'specifications' => [
                                    'en' => '<h4>Technical Specifications</h4><p>Detailed technical specifications in English.</p><ul><li>Feature 1</li><li>Feature 2</li><li>Feature 3</li></ul>',
                                    'ar' => '<h4>المواصفات الفنية</h4><p>مواصفات فنية مفصلة باللغة العربية.</p><ul><li>ميزة 1</li><li>ميزة 2</li><li>ميزة 3</li></ul>'
                                ]
                            ];

                            return $translations[$field][$locale] ?? null;
                        }
                    };
                @endphp

                <x-theme::translation-editor
                    name="specifications"
                    label="Product Specifications"
                    :model="$product"
                    description="Technical details and specifications of the product."
                />
            </div>

            <!-- Read-only Translation Editor -->
            <div class="mb-10">
                <h4 class="mb-5">Read-only Translation Editor</h4>
                @php
                    $terms = new class {
                        public function getTranslation($field, $locale, $fallback = true) {
                            $translations = [
                                'terms_conditions' => [
                                    'en' => '<h3>Terms and Conditions</h3><p>These are the standard terms and conditions for our service.</p>',
                                    'ar' => '<h3>الشروط والأحكام</h3><p>هذه هي الشروط والأحكام القياسية لخدمتنا.</p>'
                                ]
                            ];

                            return $translations[$field][$locale] ?? null;
                        }
                    };
                @endphp

                <x-theme::translation-editor
                    name="terms_conditions"
                    label="Terms and Conditions"
                    :model="$terms"
                    :readonly="true"
                    description="Legal terms that users must agree to."
                />
            </div>

            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
@endpush

@push('scripts')
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
@endpush
