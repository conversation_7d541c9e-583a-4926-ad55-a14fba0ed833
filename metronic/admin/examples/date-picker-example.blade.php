@extends('theme::admin.layouts.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Date Picker Component Example</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="#">
            @csrf
            
            <div class="row">
                <!-- Basic Date Picker -->
                <x-theme::date-picker 
                    name="due_date"
                    label="Due Date"
                    placeholder="Select a date"
                    :required="true"
                />
                
                <!-- Date Picker with Custom Icon -->
                <x-theme::date-picker 
                    name="start_date"
                    label="Start Date"
                    placeholder="Select start date"
                    icon="ki-duotone ki-calendar"
                    iconBgClass="bg-primary"
                />
            </div>
            
            <div class="row mt-5">
                <!-- Date Time Picker -->
                <x-theme::date-picker 
                    name="appointment_time"
                    label="Appointment Time"
                    placeholder="Select date and time"
                    :enableTime="true"
                    format="Y-m-d H:i"
                    description="Please select both date and time for your appointment."
                />
                
                <!-- Date Picker with Min/Max Date -->
                <x-theme::date-picker 
                    name="event_date"
                    label="Event Date"
                    placeholder="Select event date"
                    :minDate="date('Y-m-d')"
                    :maxDate="date('Y-m-d', strtotime('+1 year'))"
                    description="Event must be scheduled within the next year."
                />
            </div>
            
            <div class="row mt-5">
                <!-- Date Picker with Model -->
                @php
                    $event = (object) ['scheduled_date' => date('Y-m-d', strtotime('+1 week'))];
                @endphp
                
                <x-theme::date-picker 
                    name="scheduled_date"
                    label="Scheduled Date"
                    placeholder="Select scheduled date"
                    :model="$event"
                    columnSize="col-md-12"
                />
            </div>
            
            <div class="d-flex justify-content-end mt-5">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </form>
    </div>
</div>
@endsection
