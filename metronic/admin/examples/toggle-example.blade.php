@extends('theme::admin.layouts.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Toggle Switch Component Example</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="#">
            @csrf
            
            <div class="row mb-10">
                <!-- Basic Toggle -->
                <div class="col-md-6">
                    <h4 class="mb-5">Basic Toggle</h4>
                    <x-theme::toggle 
                        name="is_active"
                        label="Active Status"
                        :checked="true"
                        description="Enable or disable this feature"
                    />
                </div>
                
                <!-- Custom Text Toggle -->
                <div class="col-md-6">
                    <h4 class="mb-5">Custom Text Toggle</h4>
                    <x-theme::toggle 
                        name="notifications"
                        label="Email Notifications"
                        onText="Enabled"
                        offText="Disabled"
                        description="Receive email notifications for new messages"
                    />
                </div>
            </div>
            
            <div class="row mb-10">
                <!-- Different Sizes -->
                <div class="col-md-12">
                    <h4 class="mb-5">Toggle Sizes</h4>
                    <div class="d-flex flex-column gap-5">
                        <x-theme::toggle 
                            name="size_small"
                            label="Small Toggle"
                            size="sm"
                        />
                        
                        <x-theme::toggle 
                            name="size_medium"
                            label="Medium Toggle (Default)"
                            size="md"
                        />
                        
                        <x-theme::toggle 
                            name="size_large"
                            label="Large Toggle"
                            size="lg"
                        />
                    </div>
                </div>
            </div>
            
            <div class="row mb-10">
                <!-- Different Colors -->
                <div class="col-md-12">
                    <h4 class="mb-5">Toggle Colors</h4>
                    <div class="d-flex flex-column gap-5">
                        <x-theme::toggle 
                            name="color_primary"
                            label="Primary Color (Default)"
                            onColor="primary"
                            :checked="true"
                        />
                        
                        <x-theme::toggle 
                            name="color_success"
                            label="Success Color"
                            onColor="success"
                            :checked="true"
                        />
                        
                        <x-theme::toggle 
                            name="color_info"
                            label="Info Color"
                            onColor="info"
                            :checked="true"
                        />
                        
                        <x-theme::toggle 
                            name="color_warning"
                            label="Warning Color"
                            onColor="warning"
                            :checked="true"
                        />
                        
                        <x-theme::toggle 
                            name="color_danger"
                            label="Danger Color"
                            onColor="danger"
                            :checked="true"
                        />
                    </div>
                </div>
            </div>
            
            <div class="row mb-10">
                <!-- Disabled Toggle -->
                <div class="col-md-6">
                    <h4 class="mb-5">Disabled Toggle</h4>
                    <x-theme::toggle 
                        name="disabled_toggle"
                        label="Disabled Toggle"
                        :disabled="true"
                        description="This toggle cannot be changed"
                    />
                </div>
                
                <!-- Required Toggle -->
                <div class="col-md-6">
                    <h4 class="mb-5">Required Toggle</h4>
                    <x-theme::toggle 
                        name="required_toggle"
                        label="Required Toggle"
                        :required="true"
                        description="This toggle must be checked"
                    />
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    Submit
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
