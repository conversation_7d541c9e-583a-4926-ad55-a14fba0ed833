{"private": true, "type": "module", "scripts": {"copy-theme": "cpx \"core/khaleds/laravel-skin/metronic/assets/**/*\" public/metronic", "watch-theme": "cpx \"core/khaleds/laravel-skin/metronic/assets/**/*\" public/metronic --watch", "dev": "vite", "dev-admin": "npm run watch-theme && vite", "build": "vue-tsc && vite build && vite build --ssr"}, "dependencies": {"@bhplugin/vue3-datatable": "^1.0.3", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "@headlessui/vue": "^1.7.3", "@heroicons/vue": "^2.2.0", "@suadelabs/vue3-multiselect": "^1.0.2", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "@vueuse/core": "^10.11.1", "@vueuse/head": "^2.0.0", "apexcharts": "^4.4.0", "dayjs": "^1.11.13", "easymde": "^2.18.0", "file-upload-with-preview": "^4.2.0", "highlight.js": "^11.3.1", "lucide-vue-next": "^0.474.0", "maska": "^3.0.0", "moment": "^2.30.1", "path": "^0.12.7", "pdfjs-dist": "^4.10.38", "pinia": "^2.0.22", "pinia-plugin-persistedstate": "^4.2.0", "quill": "^2.0.3", "remixicon": "^4.6.0", "sweetalert2": "^11.5.1", "swiper": "^11.1.9", "tippy.vue": "^3.2.1", "v-tables-3": "^0.4.7", "vue": "^3.2.37", "vue-clipboard3": "^2.0.0", "vue-countup-v3": "^1.0.14", "vue-draggable-next": "^2.1.1", "vue-easy-lightbox": "^1.9.0", "vue-flatpickr-component": "^11.0.1", "vue-height-collapsible": "^0.1.1", "vue-i18n": "^9.2.2", "vue-router": "^4.1.5", "vue-simple-range-slider": "^1.0.0", "vue3-apexcharts": "^1.4.1", "vue3-carousel": "^0.15.1", "vue3-easymde": "^1.0.0", "vue3-form-wizard": "^0.2.4", "vue3-json-excel": "^1.0.10-alpha", "vue3-number-spinner": "^0.0.9", "vue3-perfect-scrollbar": "^2.0.0", "vue3-popper": "^1.5.0", "vue3-quill": "^0.3.1", "vue3-tel-input": "^1.0.4"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@rollup/plugin-alias": "^5.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.14", "@vitejs/plugin-vue": "^5.0.0", "@vue/server-renderer": "^3.4.0", "autoprefixer": "^10.4.12", "axios": "^1.6.4", "cpx": "^1.5.0", "laravel-vite-plugin": "^1.0.0", "lucide-vue-next": "^0.474.0", "postcss": "^8.4.31", "tailwindcss": "^3.2.1", "typescript": "^5.0.2", "url": "^0.11.4", "vite": "^5.0.0", "vite-plugin-static-copy": "^3.0.0", "vue": "^3.4.0", "vue-tsc": "^2.2.0"}}