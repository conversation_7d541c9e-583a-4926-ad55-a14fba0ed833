<?php
namespace Tests\Unit;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\GeneralEvent\Entities\Category;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Enums\StatusEnum;
use Modules\GeneralEvent\Repositories\GeneralEventRepository;
use Modules\GeneralEvent\Services\UserCategoryPreferenceService;
use Modules\GeneralEvent\Services\GeneralEventService;
use Tests\TestCase;

class GeneralEventTest extends TestCase
{
    use RefreshDatabase;
    protected GeneralEventService $service;
    protected GeneralEventRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = new GeneralEventRepository(new GeneralEvent());
        $this->service = new GeneralEventService($this->repository);
    }

    public function test_it_can_paginate_without_filters()
    {
        // Arrange
        GeneralEvent::factory()->count(15)->create();

        // Act
        $result = $this->service->paginate();

        // Assert
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertEquals(10, $result->perPage()); // Default pagination
        $this->assertEquals(15, $result->total());
    }

    public function test_it_can_paginate_with_filters()
    {
        // Arrange
        GeneralEvent::factory()->count(5)->create(['status' => StatusEnum::PUBLISHED->value]);
        GeneralEvent::factory()->count(3)->create(['status' => StatusEnum::DRAFT->value]);

        // Act
        $result = $this->service->paginate(['status' => StatusEnum::PUBLISHED->value]);

        // Assert
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertEquals(5, $result->total());
        $this->assertEquals(StatusEnum::PUBLISHED->value, $result->first()->status->value);
    }

    public function test_it_can_find_event()
    {
        // Arrange
        $event = GeneralEvent::factory()->create();

        // Act
        $result = $this->service->findOrFail(id: $event->id);

        // Assert
        $this->assertInstanceOf(GeneralEvent::class, $result);
        $this->assertEquals($event->id, $result->id);
    }

    public function test_it_can_publish_draft_event()
    {
        // Arrange
        $event = GeneralEvent::factory()->create([
            'status' => StatusEnum::DRAFT->value,
            'publish_date' => null
        ]);

        // Act
        $result = $this->service->publish($event);
        $event->refresh();

        // Assert
        $this->assertTrue($result);
        $this->assertEquals(StatusEnum::PUBLISHED->value, $event->status->value);
        $this->assertNotNull($event->publish_date);
        $this->assertTrue(now()->isSameDay($event->publish_date));
    }

    public function test_it_can_sync_new_preferences()
    {
        // Arrange
        $user = User::factory()->create();
        $categories = Category::factory()->count(3)->create();
        $categoryIds = $categories->pluck('id')->toArray();
        // Act
        $result = app(UserCategoryPreferenceService::class)->sync($user, $categoryIds);

        // Assert
        $this->assertArrayHasKey('attached', $result);
        $this->assertCount(3, $result['attached']);
        $this->assertCount(3, $user->categoryPreferences);

        // Verify timestamps are set
        $user->categoryPreferences->each(function ($preference) {
            $this->assertNotNull($preference->pivot->created_at);
            $this->assertNotNull($preference->pivot->updated_at);
        });
    }
}
