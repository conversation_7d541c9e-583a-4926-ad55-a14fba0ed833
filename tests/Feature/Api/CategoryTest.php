<?php
namespace Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\GeneralEvent\Entities\Category;
use Tests\TestCase;

class CategoryTest extends TestCase
{
    use RefreshDatabase;

    public function setUp(): void
    {
        parent::setUp();
        //$this->user = User::factory()->create();
    }

    public function test_can_list_events()
    {
        // Arrange
        Category::factory()->count(3)->create();

        // Act
        $response = $this->getJson('/api/categories');

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(3, 'data');
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                '*' => [  // '*' indicates an array of objects
                    'id',
                    'name',
                    'description',
                    'active'
                ]
            ]
        ]);
        $response->assertJson([
                'status' => true,
                'message' => ApiResponse::RETRIEVE
            ]);
    }

    public function test_can_show_event()
    {
        // Arrange
        $category = Category::factory()->create();

        // Act
        $response = $this->getJson('/api/categories/' . $category->id);

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'status' => true,
            'message' => ApiResponse::RETRIEVE
        ]);
        $responseData = $response->json('data');
        $this->assertEquals($category->id, $responseData['id']);
    }
}
