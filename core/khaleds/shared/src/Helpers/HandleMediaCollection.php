<?php

namespace Khaleds\Shared\Helpers;

trait HandleMediaCollection
{
    /**
     * Register multiple media collections dynamically.
     *
     * @param array $collections
     */
    public function registerMediaCollection(array $collections): void
    {
        foreach ($collections as $collectionName) {
            $this->addMediaCollection($collectionName)->singleFile();
        }
    }

    /**
     * Get all media images for a given collection.
     *
     * @param string $collection
     * @return array
     */
    public function getMediaImages(string $collection): array
    {
        $mediaImages = $this->getMedia($collection);
        return $mediaImages->count() ? $mediaImages->toArray() : [];
    }

    /**
     * Get a single media image URL for a given collection.
     *
     * @param string $collection
     * @return string|null
     */
    public function getMediaImage(string $collection): ?string
    {
        return $this->getMedia($collection)->first()?->getUrl();
    }
}
