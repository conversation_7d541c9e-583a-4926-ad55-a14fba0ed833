<?php

namespace Khaleds\Shared\Helpers\Datatables;

class ActionButton
{

    public string $classes = '';
    public string $url = '';
    public string $attributes = '';
    public string $html = '';
    public bool $can = true;

    public function __construct(public string $label)
    {
    }


    public static function make(string $label): self
    {
        return new static($label);
    }


    public function url(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    public function attributes(string $attributes): self
    {
        $this->attributes = $attributes;
        return $this;
    }

    public function classes(string $classes): self
    {
        $this->classes = $classes;
        return $this;
    }

    public function html(string $html): self
    {
        $this->html = $html;
        return $this;
    }

    public function when(bool $condition): self
    {
        $this->can = $condition;
        return $this;
    }



}
