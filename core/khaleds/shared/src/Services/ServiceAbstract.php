<?php

namespace Khaleds\Shared\Services;

use Khaleds\Shared\Infrastructure\Repositories\Interfaces\RepositoryInterface;
use Illuminate\Database\Eloquent\Model;

abstract class ServiceAbstract implements ServiceInterface
{

    //todo generate select array with filter and relations with check performance tool
    //TODO: maybe you should add in each service allowed filter search for security in array or validation function
    public function __construct(protected RepositoryInterface $repository)
    {
    }


    // return paginated collection maybe to be separated function
    public function paginate(array $filter = [], array $with = [], array $select = ['*']){

        return $this->repository->getAllBy($filter, $with, $select);
    }


    public function findOrFail(int $id ,array $filter = [],array $with=[]){

        return $this->repository->findOrFail($id , $filter,['*'],$with);

    }

    public function create(array $data){

        // check for exception
        return $this->repository->create($data);
    }


    public function update(array $data,Model $model){

        return $this->repository->update($model,$data);
    }

    public function delete(Model|string $model){

        $this->repository->delete($model);
    }

    //delete from table where condition
    public function deleteWhereConditions(array $filter = []){

        $this->repository->deleteWhereConditions($filter);
    }

    // todo to find bulk
    public function deleteBulk(array $ids,array $filter){

        foreach ($ids as $id) {

            $model = $this->repository->findOrFail($id,$filter ,['id']);

            $this->repository->delete($model);

        }

    }



}

// TODO: make interface for crud to be stickied with default name convention
