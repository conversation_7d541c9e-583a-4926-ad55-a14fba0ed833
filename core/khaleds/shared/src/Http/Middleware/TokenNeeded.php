<?php

namespace <PERSON>haleds\Shared\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class TokenNeeded
{

    /**
     * Handle an incoming request by get token and make auth user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, \Closure $next)
    {
        $token = $request->bearerToken();
        if ($token) {
            $model = PersonalAccessToken::findToken($token);
            if ($model) {
                if ($model->tokenable) {
                    Auth::login($model->tokenable);
                }
            }
        }

        return $next($request);
    }
}
