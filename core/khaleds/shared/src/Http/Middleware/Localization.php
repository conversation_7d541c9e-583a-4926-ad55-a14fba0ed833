<?php

namespace Khaleds\Shared\Http\Middleware;

use Illuminate\Http\Request;

class Localization
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, \Closure $next)
    {

        $locale = $request->header('Accept-Language', 'en');

        // You can also validate against allowed languages
        $allowedLocales = ['en', 'ar']; //Supported languages

        if (!in_array($locale, $allowedLocales)) {
            $locale = 'en'; // Default to English if invalid language
        }

        // Set the application locale
        app()->setLocale($locale);
        return $next($request);
    }
}
