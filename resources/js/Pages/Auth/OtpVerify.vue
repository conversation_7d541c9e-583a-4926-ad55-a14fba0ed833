<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import PortalGuestLayout from '@/Layouts/PortalGuestLayout.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputElement from "@/Components/InputElement.vue";
import { usePage , router , Link, useForm } from '@inertiajs/vue3';

import { useAuthStore } from '@/Stores/auth';
import GeneralBtnElement from "@/Components/GeneralBtnElement.vue";
const authStore = useAuthStore();

console.log('Email saved:', authStore.email);

defineProps<{
    status?: string;
}>();

const page = usePage();

const form = useForm({
    otp1: '',
    otp2: '',
    otp3: '',
    otp4: '',
    otp5: '',
    otp6: '',
    otp_code: '',
    email:authStore.email
});

const errors = useForm({});

const clearError = (field) => {
    errors[field] = '';
};

const submit = () => {

    form.otp_code = form.otp1 + form.otp2 + form.otp3 + form.otp4 + form.otp5 + form.otp6;
    console.log(form.otp_code)
    form.post(route('otp.store'), {
        onError: (errors) => {
            console.log(errors); // Check if `otp_code` error appears
        }
    });
};


// Timer state
const timeLeft = ref(59); // Start countdown from 59 seconds
let timer: ReturnType<typeof setInterval> | null = null;

// Start countdown when component is mounted
onMounted(() => {
    startCountdown();
});

// Cleanup when component is unmounted
onUnmounted(() => {
    if (timer) clearInterval(timer);
});

// Countdown logic
const startCountdown = () => {
    timer = setInterval(() => {
        if (timeLeft.value > 0) {
            timeLeft.value--;
        } else {
            clearInterval(timer);
        }
    }, 1000);
};

// Resend OTP logic
const resendOTP = () => {

    const formReset = useForm({
        email:authStore.email
    });

    timeLeft.value = 59; // Reset timer
    startCountdown();
    formReset.post(route('password.email'));
};


// **Automatically move to the next input when typing**
const handleInput = (index: number, event: Event) => {
    const target = event.target as HTMLInputElement;
    target.value = target.value.replace(/\D/g, ''); // Allow only digits

    if (target.value.length === 1) {
        const nextInput = document.getElementById(`otp${index + 1}`) as HTMLInputElement;
        if (nextInput) nextInput.focus();
    }
};

// **Move focus back on Backspace if empty**
const handleBackspace = (index: number, event: KeyboardEvent) => {
    if (event.key === "Backspace" && index > 1) {
        const currentInput = document.getElementById(`otp${index}`) as HTMLInputElement;
        if (currentInput.value === '') {
            const prevInput = document.getElementById(`otp${index - 1}`) as HTMLInputElement;
            if (prevInput) prevInput.focus();
        }
    }
};
</script>

<template>
    <PortalGuestLayout>

        <template #title> {{ $t('auth.enter_email') }}</template>
        <template #subtitle> {{ $t('auth.confirmation_code_will_send') }}</template>
        <template #image><img :src="'/assets/images/otp.png'" alt="Logo" class="w-full h-full object-cover" /></template>

        <div v-if="status" class="mb-4 font-medium text-sm text-green-600 dark:text-green-400">
            {{ status }}
        </div>

        <form @submit.prevent="submit">

            <div class="grid grid-cols-6 lg:grid-cols-6 gap-6 lg:gap-8">

                <InputElement
                    id="otp1"
                    name="otp1"
                    class="!w-[40px] h-[40px] sm:!w-[65px] sm:!h-[65px] !rounded-[24px] bg-gray-background text-center"
                    v-model="form.otp1"
                    maxlength="1"
                    @input="handleInput(1, $event)"
                    @keydown.backspace="handleBackspace(1, $event)"
                    required
                    autofocus
                />
                <InputElement
                    id="otp2"
                    name="otp2"
                    class="!w-[40px] h-[40px] sm:!w-[65px] sm:!h-[65px] !rounded-[24px] bg-gray-background text-center"
                    v-model="form.otp2"
                    maxlength="1"
                    @input="handleInput(2, $event)"
                    @keydown.backspace="handleBackspace(2, $event)"
                    required
                />
                <InputElement
                    id="otp3"
                    name="otp3"
                    class="!w-[40px] h-[40px] sm:!w-[65px] sm:!h-[65px] !rounded-[24px] bg-gray-background text-center"
                    v-model="form.otp3"
                    maxlength="1"
                    @input="handleInput(3, $event)"
                    @keydown.backspace="handleBackspace(3, $event)"
                    required
                />
                <InputElement
                    id="otp4"
                    name="otp4"
                    class="!w-[40px] h-[40px] sm:!w-[65px] sm:!h-[65px] !rounded-[24px] bg-gray-background text-center"
                    v-model="form.otp4"
                    maxlength="1"
                    @input="handleInput(4, $event)"
                    @keydown.backspace="handleBackspace(4, $event)"
                    required
                />
                <InputElement
                    id="otp5"
                    name="otp5"
                    class="!w-[40px] h-[40px] sm:!w-[65px] sm:!h-[65px] !rounded-[24px] bg-gray-background text-center"
                    v-model="form.otp5"
                    maxlength="1"
                    @input="handleInput(5, $event)"
                    @keydown.backspace="handleBackspace(5, $event)"
                    required
                />
                <InputElement
                    id="otp6"
                    name="otp6"
                    class="!w-[40px] h-[40px] sm:!w-[65px] sm:!h-[65px] !rounded-[24px] bg-gray-background text-center"
                    v-model="form.otp6"
                    maxlength="1"
                    @input="handleInput(6, $event)"
                    @keydown.backspace="handleBackspace(6, $event)"
                    required
                />



            </div>

            <InputError class="mt-2" :message="form.errors.otp_code" />

            <div class="mt-6 text-xs  text-center">
                <span class="time ">
                    {{ $t('auth.send_code_again_after') }},
                    <span class="time text-primary ">{{ String(Math.floor(timeLeft / 60)).padStart(2, '0') }}:{{ String(timeLeft % 60).padStart(2, '0') }} {{ $t('auth.sec') }}</span>
                </span>
            </div>

            <div class="text-center text-xs mt-2">
                <span class="text-gray-outline" v-if="timeLeft > 0">
                    {{ $t('auth.code_not_received') }}, {{ $t('auth.send_code_again') }}
                </span>
                <div v-else>
                    {{ $t('auth.code_not_received') }}
                    <button  @click.prevent="resendOTP" class="text-primary">
                        {{ $t('auth.send_code_again') }}
                    </button>
                </div>
            </div>

            <div class="mt-6 footer flex flex-col justify-center  w-full rounded">

                <div class="grid">

                    <GeneralBtnElement class="btn btn-primary border-primary" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                        {{ $t('auth.send') }}
                    </GeneralBtnElement>

                </div>

                <p class="mt-4 text-sm font-bold text-center text-gray-secondary">
                    {{ $t('auth.back_to') }} <Link :href="route('login')" class="text-primary"> {{ $t('auth.login') }} </Link>
                </p>

            </div>


        </form>
    </PortalGuestLayout>
</template>
