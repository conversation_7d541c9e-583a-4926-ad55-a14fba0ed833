<template>
    <PortalGuestLayout>
        <template #title>{{ $t('auth.dira_cast') }}</template>
        <template #subtitle>{{ $t('auth.attendee_registration') }}</template>
        <template #image><img :src="'/assets/images/logo_auth.png'" alt="Logo" class="h-full max-w-none" /></template>

        <div class="space-y-6">
            <!-- Registration Form -->
            <form @submit.prevent="submitForm" class="space-y-4" v-if="!registrationSuccess">

                <!-- First Name and Last Name -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div>
                        <InputElement
                            v-model="form.first_name"
                            name="first_name"
                            :placeholder="$t('auth.first_name')"
                            @input="clearError('first_name')"
                            type="text"
                            autocomplete="given-name"
                            icon="ri-user-line"
                        />
                        <InputError :message="errors?.first_name" />
                    </div>
                    <div>
                        <InputElement
                            v-model="form.last_name"
                            name="last_name"
                            :placeholder="$t('auth.last_name')"
                            @input="clearError('last_name')"
                            type="text"
                            autocomplete="family-name"
                            icon="ri-user-line"
                        />
                        <InputError :message="errors?.last_name" />
                    </div>
                </div>

                <!-- Email -->
                <div>
                    <InputElement
                        v-model="form.email"
                        name="email"
                        :placeholder="$t('auth.email')"
                        @input="clearError('email')"
                        type="email"
                        autocomplete="email"
                        icon="ri-mail-line"
                    />
                    <InputError :message="errors?.email" />
                </div>

                <!-- Password -->
                <div>
                    <InputElement
                        v-model="form.password"
                        name="password"
                        :placeholder="$t('auth.password')"
                        @input="clearError('password')"
                        type="password"
                        autocomplete="new-password"
                        icon="ri-lock-line"
                    />
                    <InputError :message="errors?.password" />
                </div>

                <!-- Confirm Password -->
                <div>
                    <InputElement
                        v-model="form.password_confirmation"
                        name="password_confirmation"
                        :placeholder="$t('auth.confirm_password')"
                        @input="clearError('password_confirmation')"
                        type="password"
                        autocomplete="new-password"
                        icon="ri-lock-line"
                    />
                    <InputError :message="errors?.password_confirmation" />
                </div>

                <!-- Submit Button -->
                <div class="pt-4">
                    <GeneralBtnElement
                        type="submit"
                        :disabled="form.processing"
                        class="w-full btn btn-primary border-primary"
                    >
                        <span v-if="form.processing">{{ $t('auth.registering') }}...</span>
                        <span v-else>{{ $t('auth.register_as_attendee') }}</span>
                    </GeneralBtnElement>
                </div>

                <!-- Login Link -->
                <div class="text-center pt-4">
                    <p class="text-sm text-gray-600">
                        {{ $t('auth.already_have_account') }}
                        <Link :href="route('login')" class="font-bold text-primary hover:text-primary">
                            {{ $t('auth.login') }}
                        </Link>
                    </p>
                </div>

                <!-- Other Registration Options -->
                <div class="text-center pt-4 border-t">
                    <p class="text-sm text-gray-600 mb-2">
                        {{ $t('auth.register_as_different_type') }}
                    </p>
                    <Link :href="route('register')" class="font-bold text-orange hover:text-primary-500">
                        {{ $t('auth.register_as_organizer_or_speaker') }}
                    </Link>
                </div>

            </form>

            <!-- Thank You Message -->
            <div v-if="registrationSuccess" class="flex flex-col gap-5 items-center text-center">
                <ImageElement img-src="/assets/images/form_success.svg" class="w-[64px]" />
                <div class="text-[18px]">
                    {{ $t('auth.attendee_request_sent') }}
                </div>
                <div class="text-[14px]">
                    {{ $t('auth.attendee_request_sent_desc') }}
                </div>
                <GeneralBtnElement as="link" href="/login" class="px-28 btn-primary border-primary">
                    {{ $t('auth.login') }}
                </GeneralBtnElement>
            </div>
        </div>
    </PortalGuestLayout>
</template>

<script setup lang="ts">
import PortalGuestLayout from '@/Layouts/PortalGuestLayout.vue';
import { useForm, Link } from '@inertiajs/vue3';
import { reactive, ref } from 'vue';
import InputElement from '@/Components/InputElement.vue';
import InputError from '@/Components/InputError.vue';
import GeneralBtnElement from '@/Components/GeneralBtnElement.vue';
import ImageElement from '@/Components/ImageElement.vue';

const form = useForm({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const errors = reactive({});
const registrationSuccess = ref(false);

const clearError = (field: string) => {
    if (errors[field]) {
        errors[field] = '';
    }
};

const submitForm = () => {
    form.post('/register/attendee', {
        onSuccess: () => {
            // Show thank you message
            registrationSuccess.value = true;
        },
        onError: (validationErrors) => {
            Object.assign(errors, validationErrors);
        }
    });
};
</script>

<style scoped>
/* Add any specific styles for attendee registration if needed */
</style>
