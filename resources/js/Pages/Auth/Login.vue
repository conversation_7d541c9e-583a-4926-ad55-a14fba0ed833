<script setup lang="ts">
import Checkbox from '@/Components/Checkbox.vue';
import PortalGuestLayout from '@/Layouts/PortalGuestLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputElement from '@/Components/InputElement.vue';
import { Link, useForm } from '@inertiajs/vue3';
import GeneralBtnElement from "@/Components/GeneralBtnElement.vue";

defineProps<{
    canResetPassword?: boolean;
    status?: string;
}>();

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const clearError = (field) => {
    form.errors[field] = '';
};

const submit = () => {
    form.post(route('login'), {
        onFinish: () => {
            form.reset('password');
        },
    });
};
</script>

<template>
    <PortalGuestLayout >

        <template #title>{{ $t('auth.dira_cast') }}</template>
        <template #subtitle>{{ $t('auth.welcome_back') }}</template>
        <template #image><img :src="'/assets/images/logo_auth.png'" alt="Logo" class="h-full max-w-none" /></template>

        <div v-if="status" class="mb-4 font-medium text-sm text-green-600">
            {{ status }}
        </div>

        <form @submit.prevent="submit">

            <div class="grid gap-4">

                <div class="grid grid-cols-1 gap-5">

                    <div class="grid">
                        <InputElement
                            v-model="form.email"
                            name="email"
                            :placeholder="$t('auth.email')"
                            @input="clearError('email')"
                            type="email"
                            autocomplete="username"
                            icon="ri-mail-line"
                        />
                        <InputError :message="form.errors.email" />
                    </div>

                    <div class="grid gap-2">

                        <div class="grid">
                            <InputElement
                                v-model="form.password"
                                name="password"
                                :placeholder="$t('auth.password')"
                                @input="clearError('password')"
                                type="password"
                                autocomplete="current-password"
                                icon="ri-lock-line"
                            />
                            <InputError :message="form.errors.password" />
                        </div>
                        <Link
                            v-if="canResetPassword"
                            :href="route('password.request')"
                            class="underline text-xs text-gray-secondary dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                        >
                            {{ $t('auth.forget_password') }}
                        </Link>

                    </div>

                </div>

                <div class="grid gap-1 items-center">
                    <label class="flex items-center gap-1 text-sm text-gray-secondary cursor-pointer">
                        <Checkbox name="remember" v-model:checked="form.remember" />
                        <span class="ms-2 text-sm text-gray-600 dark:text-gray-400" >{{ $t('auth.remember_me') }}</span>
                    </label>
                </div>

                <GeneralBtnElement class="btn btn-primary border-primary" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    {{ $t('auth.login') }}
                </GeneralBtnElement>

            </div>

        </form>

<!--        <div class="flex gap-2 justify-center w-full flex-wrap">-->
<!--            <GeneralBtnElement as="link" href="/" icon="/assets/images/apple.svg">-->
<!--                {{ $t('auth.login_apple') }}-->
<!--            </GeneralBtnElement>-->
<!--            <GeneralBtnElement as="link" href="/" icon="/assets/images/google.svg">-->
<!--                {{ $t('auth.login_google') }}-->
<!--            </GeneralBtnElement>-->
<!--        </div>-->

        <footer class="flex flex-col justify-center items-center w-full rounded">
<!--            <p class="text-sm font-bold text-center">-->
<!--                {{ $t('auth.new_user') }}، <Link :href="route('register')" class="text-primary"> {{ $t('auth.create_account_attende') }} </Link>-->
<!--            </p>-->
<!--            <p class="text-sm font-bold text-gray-secondary">{{ $t('auth.or') }}</p>-->
            <Link :href="route('register')" class="text-sm font-bold text-orange text-center"> {{ $t('auth.create_account_speaker_organizer') }}</Link>
        </footer>

    </PortalGuestLayout>
</template>
