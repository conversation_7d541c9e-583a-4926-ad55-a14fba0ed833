<script setup lang="ts">
import EventCard from "@/Components/EventCard.vue";
import Status from '@/Components/Status.vue';
import EventDetailsCard from '@/Components/EventDetailsCard.vue';
import Tabs from '@/Components/Tabs.vue'
import TabContent from '@/Components/TabContent.vue'
import ReviewCard from '@/Components/ReviewCard.vue'
import ReviewSlider from '@/Components/ReviewSlider.vue'
import { Carousel, Slide } from 'vue3-carousel';
import BreadcrumbElement from '@/Components/BreadcrumbElement.vue'
import { Head } from "@inertiajs/vue3";
import { computed, ref, defineProps } from 'vue';
import type { Event, Tab, TabContents, Review, TabId } from '@/types/event';
import PortalLayout from '@/Layouts/PortalLayout.vue';
import { useI18n } from 'vue-i18n';

interface Props {
    event: Event;
}

const props = defineProps<Props>();
const { t } = useI18n();

const handleViewDetails = (): void => {
    console.log('View details clicked')
}

const handleFeatureClick = (feature: string): void => {
    console.log('Feature clicked:', feature)
}

const tabs = computed<Tab[]>(() => {
    const tabIds = ['about' ,'similar', 'presenter', 'attendance'];
    return tabIds.map(id => ({
        id,
        label: t(`Events.features.tabs.${id}.label`),
        icon: t(`Events.features.tabs.${id}.icon`)
    }));
});

const contents = computed<TabContents>(() => ({
    similar: {
        title: t('Events.features.tab_contents.similar.title'),
        description: t('Events.features.tab_contents.similar.description')
    },
    presenter: {
        title: t('Events.features.tab_contents.presenter.title'),
        description: t('Events.features.tab_contents.presenter.description')
    },
    attendance: {
        title: t('Events.features.tab_contents.attendance.title'),
        description: t('Events.features.tab_contents.attendance.description')
    },
    about: {
        title: t('Events.features.tab_contents.about.title'),
        description: t('Events.features.tab_contents.about.description')
    }
}));

const activeTab = ref<TabId>('about')
const activeContent = computed(() => contents.value[activeTab.value])

const handleTabChange = (tabId: TabId): void => {
    activeTab.value = tabId
}

const reviews: Review[] = [
    {
        id: 1,
        date: '22 فبراير ، 2023',
        name: 'عبد العزيز محمد',
        rating: 5,
        comment: 'هناك حقيقة مثبتة منذ زمن طويل وهي أن المحتوى المقروء لصفحة ما سيلهي القارئ عن التركيز على الشكل الخارجي للنص أو شكل توضع الفقرات في الصفحة التي يقرأها.'
    },
    {
        id: 2,
        date: '22 فبراير ، 2023',
        name: 'عبد العزيز محمد',
        rating: 4,
        comment: 'هناك حقيقة مثبتة منذ زمن طويل وهي أن المحتوى المقروء لصفحة ما سيلهي القارئ عن التركيز على الشكل الخارجي للنص أو شكل توضع الفقرات في الصفحة التي يقرأها.'
    }
]
</script>

<template>
    <Head :title="event.title" />

    <PortalLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">{{ event.title }}</h2>
        </template>

        <BreadcrumbElement
        :title="$t('Events.events')"
        :items="[$t('home'), $t('Events.events')]"
        />

        <div class="">
            <div class="">
                <div class="sm:rounded-lg">
                    <div class="text-gray-900 dark:text-gray-100">
                        <!-- Event Card Container -->
                        <div class="flex justify-center">
                            <EventCard
                                :date="event.date"
                                :location="event.location"
                                :title="event.title"
                                :speaker="event.speaker"
                                :speakerImage="event.speakerImage || '/assets/images/Artboard.png'"
                                :eventImage="event.eventImage || '/assets/images/Artboard.png'"
                                @view-details="handleViewDetails"
                            />
                        </div>

                        <Status
                            :status="event.status"
                            :category="event.category"
                            :variant="event.status.toLowerCase()"
                        />

                        <EventDetailsCard
                            :rating="event.rating"
                            :certificateImage="event.certificateImage"
                            :duration="event.duration"
                            :attendanceType="event.attendanceType"
                            :certificateType="event.certificateType"
                            @feature-click="handleFeatureClick"
                        />

                        <div class="mt-4 gap-6 rounded-2xl bg-white dark:bg-black flex flex-col p-4 shadow-soft border-spacing-16 mx-1">
                            <h2 class="text-gray-800 dark:text-gray-200 font-cairo text-xl font-semibold leading-[30px]">
                                {{ $t('Events.details') }}
                            </h2>
                            <div>
                                <Tabs
                                    :tabs="tabs"
                                    :default-tab="activeTab"
                                    @update:tab="handleTabChange"
                                />

                                <TabContent
                                    v-if="activeContent"
                                    :title="activeContent.title"
                                    :description="activeContent.description"
                                />

                                <div class="w-full my-6">
                                    <ReviewSlider
                                        :total-slides="reviews.length"
                                        :slides-to-show="2"
                                    >
                                        <Slide v-for="(review, index) in reviews" :key="review.id">
                                            <ReviewCard v-bind="review" />
                                        </Slide>
                                    </ReviewSlider>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PortalLayout>
</template>
