export enum EventStatus  {
  ALL = 'all',
  COMING = 'coming',
  NOW = 'now',
  PAST = 'past',
  CANCELED = 'canceled',
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ENDED = 'ended',
}

export namespace EventStatus {
  export function filters(): EventStatus[] {
      return [
          EventStatus.ALL,
          EventStatus.COMING,
          EventStatus.NOW,
          EventStatus.PAST,
          EventStatus.DRAFT,
          EventStatus.PUBLISHED,
          EventStatus.ENDED,
          EventStatus.CANCELED,
      ];
  }

  export function getBgColor(status: EventStatus): string {
    switch (status) {
        case EventStatus.COMING:
            return 'bg-status-coming';
        case EventStatus.NOW:
            return 'bg-primary';
        case EventStatus.PAST:
            return 'bg-status-past';
        case EventStatus.CANCELED:
            return 'bg-red-500';
        case EventStatus.DRAFT:
            return 'bg-gray-500';
        case EventStatus.ENDED:
            return 'bg-gray-500';
        case EventStatus.PUBLISHED:
            return 'bg-primary';
        default:
            return 'bg-primary';
    }
  }

  export function getTextColor(status: EventStatus): string {
    switch (status) {
        case EventStatus.COMING:
            return 'text-status-coming-text';
        case EventStatus.NOW:
            return 'text-white';
        case EventStatus.PAST:
            return 'text-black';
        case EventStatus.CANCELED:
            return 'text-white';
        case EventStatus.DRAFT:
            return 'text-white';
        case EventStatus.ENDED:
            return 'text-white';
        case EventStatus.PUBLISHED:
            return 'text-white';
        default:
            return 'text-white';
    }
  }
}
