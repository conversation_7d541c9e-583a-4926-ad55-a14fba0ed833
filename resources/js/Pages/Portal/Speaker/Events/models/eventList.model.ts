import { EventType } from '@/Pages/Portal/Speaker/Events/models/enum/eventType.model';
import { EventStatus } from '@/Pages/Portal/Speaker/Events/models/enum/eventsStatus.model';

type LocalizedString = {
  ae: string;
  en: string;
}
export type EventCard = {
    id: number;  // Add this line
    image: string;
  category: LocalizedString;
  title: LocalizedString;
  location: string;
  date: string;
  durations: number;
  type: EventType;
  Number_of_attendees: number;
  price: number;
  status: EventStatus;
}
