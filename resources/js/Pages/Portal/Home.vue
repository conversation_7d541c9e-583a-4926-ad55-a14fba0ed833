<script setup lang="ts">
import GuestLayout from '@/Layouts/GuestLayout.vue';
import { computed } from 'vue';
import { usePage } from '@inertiajs/vue3';
import LatestEvents from '@/Components/Portal/Attendee/LatestEvents.vue';
const page = usePage();

const Orders = page.props.totalOrders;
const Levels = page.props.totalLevels;
const Events = computed(() => page.props.eventStats);

</script>

<template>

    <GuestLayout>
        <div class="p-4 text-gray-900 dark:text-gray-100">
            <LatestEvents />
        </div>
    </GuestLayout>

</template>
