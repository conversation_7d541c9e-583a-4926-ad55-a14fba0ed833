<script setup lang="ts">
import PortalLayout from '@/Layouts/PortalLayout.vue';
import PortalGuestLayout from '@/Layouts/PortalGuestLayout.vue';
import { computed } from 'vue';
import { usePage } from '@inertiajs/vue3';
import ImageElement from "../../Components/ImageElement.vue";
import BreadcrumbElement from "@/Components/BreadcrumbElement.vue";
import StatCardWidget from "@/Components/StatCardWidget.vue";
const page = usePage();

const Orders = page.props.totalOrders;
const Levels = page.props.totalLevels;
const Events = computed(() => page.props.eventStats);

</script>

<template>

    <PortalLayout v-if="page.props.auth.user">

            <BreadcrumbElement
                :title="$t('home')"
                :items="['']"
            />

        <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 mb-6 text-white">

            <StatCardWidget
                :title="$t('portal.total_orders')"
                :number=Orders
                icon="ri-eye-line"
                :subtitle="$t('portal.order')"
                bgClass="bg-gradient-to-r from-cyan-500 to-cyan-400"
            />

            <StatCardWidget
                :title="$t('portal.total_levels')"
                :number=Levels
                icon="ri-eye-line"
                :subtitle="$t('portal.level')"
                bgClass="bg-gradient-to-r from-violet-500 to-violet-400"
            />

            <StatCardWidget
                v-for="([status, count], index) in Object.entries(Events)"
                :key="index"
                :title="`${$t('portal.events')} ${$t('Events.Status.' + status)} `"
                :number="count"
                icon="ri-eye-line"
                :subtitle="$t('portal.event')"
                bgClass="bg-gradient-to-r from-blue-500 to-blue-400"
            />

        </div>

    </PortalLayout>
    <PortalGuestLayout v-else>

        <template #title>{{ $t('auth.dira_cast') }}</template>
        <template #subtitle>{{ $t('auth.welcome_back') }}</template>
        <template #image>
            <ImageElement imgSrc="/assets/images/logo_auth.png" alt="logo" imgClass="h-full max-w-none" />
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h2 class="h2">{{ $t('auth.welcome_back') }}</h2>
                </div>
            </div>
        </div>
    </PortalGuestLayout>
</template>
