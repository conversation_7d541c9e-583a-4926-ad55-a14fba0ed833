<script setup lang="ts">

import PortalLayout from "@/Layouts/PortalLayout.vue";
import BreadcrumbElement from "@/Components/BreadcrumbElement.vue";
import Vue3Datatable from '@bhplugin/vue3-datatable';
import {ref, computed, watch} from 'vue';
import { router } from "@inertiajs/vue3";
import {useI18n} from "vue-i18n";
import { useAppStore } from '@/Stores/index';
import GeneralBtnElement from "@/Components/GeneralBtnElement.vue";
import Swal from 'sweetalert2';
import dayjs from 'dayjs';
import 'dayjs/locale/ar';

const store = useAppStore();
const props = defineProps({
    reviews: {
        type: Object,
        default: () => ({ data: [] })
    },
    initialUrlParams: {
        type: Object,
        default: () => ({})
    }
});

const searchKey = ref('');

const { locale:localeLang } = useI18n();
let lang;
if(localeLang.value=='ae'){lang='ar'}else {lang='en'}
const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;
});

const formatDate = (date) => {
    dayjs.locale(locale.value === 'ar' ? 'ar' : 'en');
    return dayjs(date).format('DD MMMM, YYYY');
};

const cols = computed(() =>  [
    { field: 'review_id', title: t('reviews.id') },
    { field: 'review_event', title: t('reviews.event') },
    { field: 'review_user', title: t('reviews.user') },
    { field: 'review_comment', title: t('reviews.comment') },
    { field: 'review_rate', title: t('reviews.rate') },
    { field: 'review_created', title: t('reviews.created') },
    { field: 'actions', title: t('Common.actions'), sort: false },
]) || [];

const rows = computed(() => {
    const mappedRows = props.reviews.data.map(review => ({
        review_id: review.id,
        review_event: review?.rateable?.name[locale.value],
        review_user: review?.user?.first_name + ' ' + review?.user?.last_name,
        review_comment: review?.comment,
        review_rate: review?.rate,
        review_created: formatDate(review.created_at)
    }));
    return mappedRows;
});

const currentPage = ref(props.reviews?.current_page ?? 1);
const perPage = ref(props.reviews?.per_page ?? 10);
const totalRows = computed(() => props.reviews.total ?? 0);

const changeServer = (pagination) => {

    currentPage.value = pagination.current_page;

    if (pagination.pageSize !== perPage.value) {
        perPage.value = pagination.pagesize;
    }

    router.get(`/reviews`, {
        page: pagination.current_page,
        limit: perPage.value,
    }, {
        preserveState: true,
        replace: true
    });
};

const paginationInfoText = computed(() => {
    const start = (currentPage.value - 1) * perPage.value + 1;
    const end = Math.min(start + perPage.value - 1, totalRows.value);
    return t('datatable.pagination_info', [start, end, totalRows.value]);
});

const deleteReview = (id: number) => {
    Swal.fire({
        title: t("Common.delete_confirm_title"),
        text: t("reviews.delete_confirm_message"),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: t("Common.delete"),
        cancelButtonText: t("Common.cancel"),
    }).then((result) => {
        if (result.isConfirmed) {
            router.get(`/reviews/delete/${id}`, {}, {
                preserveScroll: true,
                onSuccess: () => {
                    Swal.fire({
                        icon: 'success',
                        title: t("reviews.review_deleted"),
                        timer: 1000,
                        showConfirmButton: false,
                    })
                },
            })
        }
    })
}

</script>

<template>
    <PortalLayout>

        <div class="w-full">
            <div class="flex flex-col gap-6">

                <BreadcrumbElement
                    :title="$t('reviews.reviews')"
                    :items="[$t('home'), $t('reviews.reviews')]"
                />

                <div>
                    <div class="panel pb-0 mt-6">

                        <div class="datatable">
                            <vue3-datatable
                                :rows="rows"
                                :columns="cols"
                                :totalRows="totalRows"
                                :isServerMode="true"
                                :page="currentPage"
                                :pageSize="perPage"
                                @change="changeServer"
                                :paginationInfo="paginationInfoText"
                                :sortable="true"
                                skin="whitespace-nowrap bh-table-hover"
                                firstArrow='<i class="block ri-arrow-left-double-line text-lg rtl:rotate-180"></i>'
                                lastArrow='<i class="block ri-arrow-right-double-line text-lg rtl:rotate-180"></i>'
                                previousArrow='<i class="block ri-arrow-left-s-line text-lg rtl:rotate-180"></i>'
                                nextArrow='<i class="block ri-arrow-right-s-line text-lg rtl:rotate-180"></i>'
                            >

                                <template #review_comment="data">
                                    <p class="max-w-xs truncate">  {{ data.value.review_comment }} </p>
                                </template>

                                <template #review_rate="data">
                                    <div class="flex items-center justify-center text-warning">
                                        <div v-for="i in data.value.review_rate " >
                                            <i class="ri-star-fill text-lg"></i>
                                        </div>
                                    </div>
                                </template>

                                <template #actions=" row ">
                                    <div class="flex gap-1">
                                        <div class="text-center">
                                            <GeneralBtnElement @click="deleteReview(row.value.review_id)" class="border-none shadow-none">
                                                <i class="ri-close-circle-line text-lg"></i>
                                            </GeneralBtnElement>
                                        </div>
                                    </div>
                                </template>

                            </vue3-datatable>
                        </div>

                    </div>
                </div>

            </div>
        </div>

    </PortalLayout>
</template>
