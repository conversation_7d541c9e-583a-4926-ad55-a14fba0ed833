<template>
    <PortalLayout>
        <div class="w-full">
            <div class="flex flex-col gap-6">


                <BreadcrumbElement
                    :title="$t('calendar.calendar')"
                    :items="[$t('home'), $t('calendar.calendar')]"
                />

                <div>
                    <div class="panel">
                        <div class="mb-5">
                            <div class="mb-4 flex items-center sm:flex-row flex-col sm:justify-between justify-center">
                                <div class="sm:mb-0 mb-4">
                                    <div class="text-lg font-semibold ltr:sm:text-left rtl:sm:text-right text-center">
                                        {{ $t('calendar.calendar') }}
                                    </div>
                                </div>
                                <GeneralBtnElement as="link" href="/events/create" icon="ri-calendar-event-line" class="btn btn-primary border-primary">
                                    <i class="ri-calendar-event-line text-base me-2"></i>
                                    {{ $t('Events.add') }}
                                </GeneralBtnElement>
                            </div>
                            <div class="calendar-wrapper">
                                <FullCalendar ref="calendar" :options="calendarOptions">
                                    <template v-slot:eventContent="arg">
                                            <div class="fc-event-main-frame flex items-center px-1 py-0.5 text-white">
                                                <Link :href="`/events/${arg.event.id}`" class="text-white">
                                                    <div class="fc-event-time font-semibold px-0.5">
                                                        {{ arg.timeText }}
                                                    </div>
                                                    <div class="fc-event-title-container">
                                                            <div class="fc-event-title fc-sticky !font-medium px-0.5">
    <!--                                                            <Link :href="`/events/${arg.event.id}`" class="text-white">-->
                                                                    {{ arg.event.title }}
    <!--                                                            </Link>-->
                                                            </div>
                                                        </div>
                                                </Link>
                                            </div>
                                    </template>
                                </FullCalendar>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </PortalLayout>
</template>

<script lang="ts" setup>
import PortalLayout from "@/Layouts/PortalLayout.vue";
import BreadcrumbElement from "@/Components/BreadcrumbElement.vue";
import { computed, ref , watch } from 'vue';
import FullCalendar from '@fullcalendar/vue3';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';

import {useI18n} from "vue-i18n";


import {usePage} from "@inertiajs/vue3";
import GeneralBtnElement from "@/Components/GeneralBtnElement.vue";
import Link from "@/Components/NavLink.vue";
const page = usePage();
const allEvents = page.props.sessions;


const { locale:localeLang } = useI18n();
let lang;
if(localeLang.value=='ae'){lang='ar'}else {lang='en'}
const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;
    calendarOptions.value.locale = locale.value;
    calendarOptions.value.direction = locale.value === 'ar' ? 'rtl' : 'ltr';
});



const calendar: any = ref(null);
const now = new Date();

const events = computed(() =>
    allEvents.map(event => ({
        id: event.id,
        title: event.name ? event.name[locale.value] : "Null",
        start: event.start_date.replace(' ', 'T'),
        end: event.end_date.replace(' ', 'T'),
        className: event.className || 'primary',
        description: event.description ? event.description[locale.value] : '',
        sessions: event.sessions?.map(session => ({
            id: session.id,
            title: session.name ? session.name[locale.value] : '',
        })) || []
    }))
);

const calendarOptions = computed(() => {
    return {
        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
        initialView: 'dayGridMonth',
        locale: locale.value,
        direction: locale.value === 'ar' ? 'rtl' : 'ltr',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay',
        },
        buttonText: {
            today: t('calendar.today'),  // Use translation function
            month: t('calendar.month'),
            week: t('calendar.week'),
            day: t('calendar.day'),
        },
        editable: true,
        dayMaxEvents: true,
        selectable: true,
        droppable: false,
        events: events.value,
    };
});


const editEvent = (data: any = null) => {
    console.log('event')
};



</script>
