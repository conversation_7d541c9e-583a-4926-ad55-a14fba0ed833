<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { router } from '@inertiajs/vue3';
import debounce from 'lodash/debounce';
import { EventStatus } from '@/Pages/Portal/Speaker/Events/models/enum/eventsStatus.model';
import { EventType } from '@/Pages/Portal/Speaker/Events/models/enum/eventType.model';
import ListEventCard from '@/Components/ListEventCard.vue';
import BreadcrumbElement from '@/Components/BreadcrumbElement.vue';
import PortalLayout from '@/Layouts/PortalLayout.vue';
import { Link } from '@inertiajs/vue3';
import { DateFormatter } from "@/utils/dateFormatter";
import type { Event, EventsResponse, EventFilters } from '@/types/events';
import PaginationElement from '@/Components/PaginationElement.vue';

interface Props {
    events: {
        data: Event[];
        current_page: number;
        last_page: number;
        total: number;
        per_page: number;
    };
    filters?: EventFilters;
}

const props = defineProps<Props>();
const selectedType = ref<EventStatus>(props.filters?.status || EventStatus.ALL);
const searchQuery = ref(props.filters?.search || '');
const isLoading = ref(false);
const currentPage = ref(props.events.current_page);

// Create a computed property to access the events data
const eventsList = computed(() => {
    return props.events?.data || [];
});

// Create a debounced search function
const debouncedSearch = debounce(() => {
    updateFilters();
}, 900);

// Watch for changes in search query
watch(searchQuery, (newValue) => {
    debouncedSearch();
});

// Watch for changes in status filter
watch(selectedType, (newValue) => {
    updateFilters();
});

// Watch for changes in pagination
watch(currentPage, (newPage) => {
    updateFilters(newPage);
});

// Update Filter function for search with Name and EventStatus
function updateFilters(page?: number) {
    isLoading.value = true;

    const filters: Record<string, any> = {};

    if (searchQuery.value) {
        filters.search = searchQuery.value;
    }

    if (selectedType.value !== EventStatus.ALL) {
        filters.status = selectedType.value;
    }

    router.get(
        route('generalEvent.index'),
        {
            filter: filters,
            page: page || undefined
        },
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
            onStart: () => {
                isLoading.value = false;
            },
            onFinish: () => {
                isLoading.value = false;
            },
        }
    );
}
</script>

<template>
    <PortalLayout>
        <div class="w-full">
            <div class="flex flex-col gap-6">
                <BreadcrumbElement
                    :title="$t('Events.events')"
                    :items="[$t('home'), $t('Events.events')]"
                />

                <!-- Status Filter Buttons -->
                <div class="flex items-center justify-between gap-2">
                    <div class="flex items-center flex-wrap gap-6">
                        <button
                            v-for="type in EventStatus.filters()"
                            :key="type"
                            class="px-6 py-[6px] rounded-3xl transition-colors duration-200"
                            :class="{
                                'bg-primary text-white': selectedType === type,
                                'text-primary bg-primary-default-light dark:bg-transparent dark:shadow-soft-inset dark:shadow-primary': selectedType !== type
                            }"
                            :disabled="isLoading"
                            @click="selectedType = type"
                        >
                            {{ $t(`Events.Status.${type}`) }}
                        </button>
                    </div>

                </div>

                <!-- Search Bar and Add Button -->
                <div class="flex items-center justify-between gap-2 flex-wrap">
                    <div class="w-[300px] xl:w-[460px] lg:w-[350px]">
                        <div class="relative">
                            <input
                                v-model="searchQuery"
                                type="text"
                                class="form-input rounded-3xl ltr:pl-9 rtl:pr-9 ltr:pr-4 rtl:pl-4 peer bg-transparent placeholder:tracking-widest dark:bg-transparent"
                                :placeholder="$t('Events.search')"
                                :disabled="isLoading"
                            />
                            <!-- Search Icon -->
                            <div class="w-8 h-8 p-2 rounded-3xl   dark:bg-transparent dark:shadow-soft-inset dark:shadow-primary absolute bottom-1 left-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M15.7077 14.2968L12.6109 11.1987C14.928 8.10222 14.2962 3.71356 11.1997 1.39641C8.1032 -0.920736 3.71454 -0.288925 1.39739 2.80759C-0.919759 5.90411 -0.287949 10.2928 2.80857 12.6099C5.29595 14.4712 8.71231 14.4712 11.1997 12.6099L14.2978 15.708C14.6871 16.0973 15.3183 16.0973 15.7076 15.708C16.097 15.3187 16.097 14.6874 15.7076 14.2982L15.7077 14.2968ZM7.02996 12.012C4.27792 12.012 2.04698 9.78102 2.04698 7.02898C2.04698 4.27695 4.27792 2.046 7.02996 2.046C9.782 2.046 12.0129 4.27695 12.0129 7.02898C12.01 9.77977 9.78078 12.009 7.02996 12.012Z" fill="#007172"/>
                                </svg>
                            </div>
                            <!-- Loading Indicator -->
                            <div
                                v-if="isLoading"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2"
                            >
                                <div class="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                            </div>
                        </div>
                    </div>
                    <button class="py-2 px-8 bg-primary text-white rounded-3xl w-auto sm:w-[175px]">
                        <Link :href="route('generalEvent.create')">
                            {{ $t('Events.add') }}
                        </Link>
                    </button>
                </div>

                <!-- Events Grid -->
                <div class="grid grid-cols-1 xl:grid-cols-2 md:grid-cols-2 gap-6">
                    <ListEventCard
                        v-for="event in eventsList"
                        :key="event.id"
                        :id="event.id"
                        :image="event.media?.[0]?.original_url || '/assets/images/lightbox6.jpeg'"
                        :category="event.category?.name"
                        :title="event.name"
                        :location="event.address?.title || ''"
                        :date="DateFormatter.format(event.start_date)"
                        :durations="event.event_hours"
                        :type="event.type"
                        :Number_of_attendees="event.max_attendees"
                        :price="0"
                        :status="event.status"
                    />
                </div>

                <!-- No Results Message -->
                <div
                    v-if="!isLoading && eventsList.length === 0"
                    class="text-center py-8 text-gray-500"
                >
                    {{ $t('Events.noResults') }}
                </div>

                <!-- Pagination -->
                <PaginationElement
                    v-if="eventsList.length > 0"
                    v-model:currentPage="currentPage"
                    :totalPages="props.events.last_page"
                    :total="props.events.total"
                    :perPage="props.events.per_page"
                />
            </div>
        </div>
    </PortalLayout>
</template>
