<script setup lang="ts">

import PortalLayout from "@/Layouts/PortalLayout.vue";
import BreadcrumbElement from "@/Components/BreadcrumbElement.vue";
import Vue3Datatable from '@bhplugin/vue3-datatable';
import {ref, computed, watch} from 'vue';
import {Link, router} from "@inertiajs/vue3";
import {useI18n} from "vue-i18n";
import { useAppStore } from '@/Stores/index';
import InputElement from "@/Components/InputElement.vue";
import GeneralBtnElement from "@/Components/GeneralBtnElement.vue";
import Swal from 'sweetalert2';

const store = useAppStore();
const props = defineProps({
    levels: {
        type: Object,
        default: () => ({ data: [] })
    },
    initialUrlParams: {
        type: Object,
        default: () => ({})
    }
});

console.log(props.levels.data)

const searchKey = ref('');

const { locale:localeLang } = useI18n();
let lang;
if(localeLang.value=='ae'){lang='ar'}else {lang='en'}
const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;
});

const search1 = ref('');

const cols = computed(() =>  [
    { field: 'level_id', title: t('levels.level_number') },
    { field: 'level_name', title: t('levels.level_name') },
    { field: 'actions', title: t('Common.actions'), sort: false },
]) || [];

const rows = computed(() => {
    const mappedRows = props.levels.data.map(level => ({
        level_id: level.id,
        level_name: level.name ? level.name[locale.value] : "Null", // Ensure it exists
    }));
    console.log(mappedRows);  // Check the structure of the mapped rows
    return mappedRows;
});

const currentPage = ref(props.levels?.current_page ?? 1);
const perPage = ref(props.levels?.per_page ?? 10);
const totalRows = computed(() => props.levels.total ?? 0);

const orderBy = ref("id"); // Default sort column
const orderDirection = ref("asc"); // Default sort direction

const changeServer = (pagination) => {

    currentPage.value = pagination.current_page;

    if (pagination.pageSize !== perPage.value) {
        perPage.value = pagination.pagesize;
    }

    // Adjust query parameters to match your new structure
    const filters = {
        'filter[name]': search1.value,  // Replacing search_key with filter[name]
    };

    router.get(`/events/levels`, {
        page: pagination.current_page,
        limit: perPage.value,
        search_key: search1.value,
        ...filters,
    }, {
        preserveState: true,
        replace: true
    });
};

const paginationInfoText = computed(() => {
    const start = (currentPage.value - 1) * perPage.value + 1;
    const end = Math.min(start + perPage.value - 1, totalRows.value);
    return t('datatable.pagination_info', [start, end, totalRows.value]);
});

const deleteLevel = (id: number) => {
    Swal.fire({
        title: t("Common.delete_confirm_title"),
        text: t("levels.delete_confirm_message"),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: t("Common.delete"),
        cancelButtonText: t("Common.cancel"),
    }).then((result) => {
        if (result.isConfirmed) {
            router.get(`/events/levels/delete/${id}`, {}, {
                preserveScroll: true,
                onSuccess: () => {
                    Swal.fire({
                        icon: 'success',
                        title: t("levels.level_deleted"),
                        timer: 1000,
                        showConfirmButton: false,
                    })
                },
            })
        }
    })
}

</script>

<template>
    <PortalLayout>
        <div class="w-full">
            <div class="flex flex-col gap-6">


                <BreadcrumbElement
                    :title="$t('levels.levels')"
                    :items="[$t('home'), $t('levels.levels')]"
                />

                <div>
                    <div class="panel pb-0 mt-6">

                        <!-- Search Bar and Add Button -->
                        <div class="flex items-center justify-between gap-2 flex-wrap mb-6">
                            <div class="w-[300px] xl:w-[460px] lg:w-[350px]">
                                <div class="relative">
                                    <InputElement
                                        v-model="search1"
                                        name="website"
                                        :placeholder="$t('levels.search_placeholder')"
                                        type="text"
                                        icon="ri-search-line"
                                        error=""
                                    />
                                </div>
                            </div>
                            <button class="py-2 px-8 bg-primary text-white rounded-3xl w-auto sm:w-[175px]">
                                <Link :href="route('levels.create')">
                                    {{ $t('levels.add_level') }}
                                </Link>
                            </button>
                        </div>

                        <div class="datatable">
                            <vue3-datatable
                                :rows="rows"
                                :columns="cols"
                                :totalRows="totalRows"
                                :isServerMode="true"
                                :page="currentPage"
                                :pageSize="perPage"
                                @change="changeServer"
                                :paginationInfo="paginationInfoText"
                                :sortable="true"
                                :search="search1"
                                skin="whitespace-nowrap bh-table-hover"
                                firstArrow='<i class="block ri-arrow-left-double-line text-lg rtl:rotate-180"></i>'
                                lastArrow='<i class="block ri-arrow-right-double-line text-lg rtl:rotate-180"></i>'
                                previousArrow='<i class="block ri-arrow-left-s-line text-lg rtl:rotate-180"></i>'
                                nextArrow='<i class="block ri-arrow-right-s-line text-lg rtl:rotate-180"></i>'
                            >

                            <template #actions=" row ">
                                <div class="flex gap-1">
                                    <div class="text-center">
                                        <GeneralBtnElement @click="deleteLevel(row.value.level_id)" class=" border-none">
                                            <i class="ri-close-circle-line text-lg"></i>
                                        </GeneralBtnElement>
                                    </div>
                                </div>
                            </template>

                            </vue3-datatable>
                        </div>

                    </div>
                </div>


            </div>
        </div>
    </PortalLayout>
</template>
