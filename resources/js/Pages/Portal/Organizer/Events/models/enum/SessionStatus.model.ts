export enum SessionStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  ENDED = 'ended',
  CANCELED = 'canceled',
}

export namespace SessionStatus {
  export function filters(): SessionStatus[] {
    return [
      SessionStatus.PENDING,
      SessionStatus.ACTIVE,
      SessionStatus.ENDED,
      SessionStatus.CANCELED,
    ];
  }

  export function getBgColor(status: SessionStatus): string {
    switch (status) {
      case SessionStatus.PENDING:
        return 'bg-orange-light';
      case SessionStatus.ACTIVE:
        return 'bg-success-light';
      case SessionStatus.ENDED:
        return 'bg-gray-500';
      case SessionStatus.CANCELED:
        return 'bg-danger-light';
      default:
        return 'bg-primary-light';
    }
  }

  export function getTextColor(status: SessionStatus): string {
    switch (status) {
      case SessionStatus.PENDING:
        return 'text-amber';
      case SessionStatus.ACTIVE:
        return 'text-success';
      case SessionStatus.ENDED:
        return 'text-white';
      case SessionStatus.CANCELED:
        return 'text-danger';
      default:
        return 'text-primary';
    }
  }
}
