<template>
    <PortalLayout>
        <BreadcrumbElement
            :title="$t('Events.events')"
            :items="[$t('home'), $t('Events.events')]"
        />
        <div class="flex flex-col gap-6">
            <!-- About Event Panel -->
            <PanelElement :title="$t('Events.Create.about_event')">
                <div class="flex flex-col gap-4">
                    <!-- Name English -->
                    <InputElement
                        v-model="form.name.en"
                        :label="$t('Events.Create.event_name') + ' (English)'"
                        type="text"
                        :placeholder="$t('Events.Create.event_name_placeholder')"
                        :required="true"

                    />
                    <InputError :message="form.errors['name.en']" class="mt-2" />

                    <!-- Name Arabic -->
                    <InputElement
                        v-model="form.name.ar"
                        :label="$t('Events.Create.event_name') + ' (Arabic)'"
                        type="text"
                        :placeholder="$t('Events.Create.event_name_placeholder')"
                        :required="true"
                    />
                    <InputError :message="form.errors['name.ar']" class="mt-2" />



                    <!-- Category Selection -->
                    <div class="flex flex-col gap-2">
                        <InputLabelElement
                            :label="$t('Events.Create.category')"
                        />
                        <multiselect
                            v-model="selectedCategory"
                            class="custom-multiselect"
                            :select-label="$t('Common.press_enter_to_select')"
                            :deselect-label="$t('Common.press_enter_to_remove')"
                            :selected-label="$t('Common.selected')"
                            :options="formattedCategories"
                            track-by="id"
                            :searchable="true"
                            :placeholder="$t('Events.Create.category_placeholder')"
                            :custom-label="getCustomLabel"
                            @select="handleCategorySelect"
                        >

                        </multiselect>
                        <InputError :message="form.errors['category_id']" class="mt-2" />

                    </div>

                    <!-- Tags Selection -->
                    <!-- Tags Selection -->
                    <div class="flex flex-col gap-2">
                        <InputLabelElement :required="false" :label="$t('Events.Create.tags')"/>
                        <multiselect
                            v-model="selectedTags"
                            class="custom-multiselect"
                            :select-label="$t('Common.press_enter_to_select')"
                            :deselect-label="$t('Common.press_enter_to_remove')"
                            :selected-label="$t('Common.selected')"
                            :options="optionsTags"
                            :searchable="true"
                            :multiple="true"
                            :taggable="true"
                            :disabled="isTagSelectDisabled"
                            @input="validateTagInput"
                            @tag="addNewTag"
                            :placeholder="isTagSelectDisabled
                                ? $t('Events.Create.select_category_first')
                                : $t('Events.Create.event_add_tag_placeholder')"
                            label="name"
                            track-by="id"
                            :custom-label="getTagLabel"
                        >
                            <template #singleLabel="{ option }">
                                {{ getTagLabel(option) }}
                            </template>
                            <template #option="{ option }">
                                {{ getTagLabel(option) }}
                            </template>
                        </multiselect>
                        <p v-if="isTagSelectDisabled" class="text-sm text-gray-500">
                            {{ $t('Events.Create.please_select_category_first') }}
                        </p>
                        <InputError :message="form.errors['tags']" class="mt-2" />
                    </div>

                    <!-- Cover Image -->
                    <FileUploadElement
                        v-model="form.cover_image"
                        :preview-url="existingCoverImage"
                        :label="$t('Events.Create.cover_image')"
                    />
                    <InputError :message="form.errors.cover_image" class="mt-2" />
                </div>
            </PanelElement>

            <!-- Certificate Hours Panel -->
            <PanelElement :title="$t('Events.Create.certificate_hours')">
                <div class="flex flex-col gap-4">
                    <!-- Certificate Attendance -->
                    <div class="flex flex-col gap-2">
                        <InputLabelElement
                            :label="$t('Events.Create.attendance_certificate')"
                        />
                        <div class="flex gap-6">
                            <RadioInputElement
                                :option="$t('Events.Create.yes')"
                                v-model="form.has_certificate"
                                name="attendance"
                                :value="true"
                            />
                            <RadioInputElement
                                :option="$t('Events.Create.no')"
                                :value="false"
                                name="attendance"
                                v-model="form.has_certificate"
                            />

                        </div>
                    </div>

                    <div class="border-b dark:border-blue-darkness"></div>

                    <!-- Event Hours -->
                    <div>
                        <InputElement
                            v-model="form.event_hours"
                            :label="$t('Events.Create.event_hours')"
                            type="number"
                            icongroup="/assets/images/Timer.svg"
                            :placeholder="$t('Events.Create.hours_placeholder')"
                            :required="true"
                        />
                        <InputError :message="form.errors['event_hours']" class="mt-2" />

                    </div>
                </div>
            </PanelElement>

            <!-- Event Duration Panel -->
            <PanelElement :title="$t('Events.Create.event_duration')">
                <div class="flex flex-col gap-4">
                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- Start Date -->
                        <div class="w-full">
                            <DateInputElement
                                v-model="form.start_date"
                                :placeholder="$t('Events.Create.date_placeholder')"
                                icongroup="/assets/images/Calendar.svg"
                                :enableTime="true"
                                dateFormat="Y-m-d H:i"

                            >
                                <InputLabelElement
                                    :label="$t('Events.Create.start_date')"
                                />
                            </DateInputElement>
                            <InputError :message="form.errors.start_date" class="mt-2" />
                        </div>

                        <!-- End Date -->
                        <div class="w-full">
                            <DateInputElement
                                v-model="form.end_date"
                                :placeholder="$t('Events.Create.date_placeholder')"
                                icongroup="/assets/images/Calendar.svg"
                                :enableTime="true"
                                dateFormat="Y-m-d H:i"
                            >
                                <InputLabelElement
                                    :label="$t('Events.Create.end_date')"
                                />
                            </DateInputElement>
                            <div>
                                <InputError :message="form.errors.end_date" class="mt-2" />
                            </div>

                        </div>
                    </div>

                    <div class="border-b dark:border-blue-darkness"></div>

                    <!-- Publish Date -->
                    <div>
                        <DateInputElement
                            v-model="form.publish_date"
                            :placeholder="$t('Events.Create.date_placeholder')"
                            icongroup="/assets/images/Calendar.svg"
                            :enableTime="true"
                            dateFormat="Y-m-d H:i"
                        >
                            <InputLabelElement
                                :required="false"
                                :label="$t('Events.Create.publish_date')"
                            />
                        </DateInputElement>
                        <div>
                            <InputError :message="form.errors.publish_date" class="mt-2" />
                        </div>

                    </div>
                </div>
            </PanelElement>

            <!-- Attendance Panel -->
            <PanelElement :title="$t('Events.Create.attendance')">
                <div class="flex flex-col gap-4">
                    <!-- Attendance Method -->
                    <div class="flex flex-col gap-2">
                        <InputLabelElement
                            :label="$t('Events.Create.attendance_method')"
                        />
                        <div class="flex gap-6">
                            <RadioInputElement
                                :option="$t('Events.Create.offline')"
                                :value="EventType.OFFLINE"
                                name="type"
                                v-model="form.type"
                            />
                            <RadioInputElement
                                :option="$t('Events.Create.online')"
                                :value="EventType.ONLINE"
                                name="type"
                                v-model="form.type"
                            />
                            <RadioInputElement
                                :option="$t('Events.Create.hybrid')"
                                :value="EventType.HYBRID"
                                name="type"
                                v-model="form.type"
                            />
                        </div>
                        <InputError :message="form.errors.type" class="mt-2" />

                    </div>

                    <div class="border-b dark:border-blue-darkness"></div>

                    <!-- Location Details -->
                    <div class="">
                        <InputElement
                            v-model="form.title"
                            class="w-full"
                            :label="$t('Events.Create.event_location')"
                            type="text"
                            :placeholder="$t('Events.Create.location_placeholder')"
                            :required="form.type === EventType.ONLINE ? false : true"
                        />

                        <InputError :message="form.errors.title" class="mt-2" />
                        <div class="flex gap-4 w-full">
                            <input
                                v-model="form.lat"
                                type="hidden"
                            />
                            <input
                                v-model="form.long"
                                type="hidden"
                            />
                        </div>
                    </div>

                    <div class="" v-show="form.type !== EventType.ONLINE">
                        <InputElement
                            v-model="form.venue_name"
                            class="w-full"
                            :label="$t('Events.Create.venue_name')"
                            type="text"
                            :placeholder="$t('Events.Create.venue_name_placeholder')"
                            :required="form.type === EventType.ONLINE ? false : true"
                        />

                        <InputError :message="form.errors.venue_name" class="mt-2" />
                    </div>

                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="w-full">
                            <InputElement
                                v-model="form.max_attendees"
                                :label="$t('Events.Create.max_attendees')"
                                type="number"
                                :placeholder="$t('Events.Create.max_attendees_placeholder')"
                                :required="false"
                            />
                            <InputError :message="form.errors.max_attendees" class="mt-2" />
                        </div>

                    </div>
                </div>
            </PanelElement>

            <!-- Description Panel -->
            <PanelElement :title="$t('Events.Create.description')">
                <div class="flex flex-col gap-2">
                    <InputLabelElement
                        :label="$t('Events.Create.about_event_description')"
                    />
                    <!-- Description English -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('Events.Create.en_description') }} <span class="text-red-500">*</span></label>
                        <quill-editor
                            v-model="form.description.en"
                            @change="(event) => form.description.en = event.html"
                            @update:content="$emit('clearError', 'about_company')"
                            @ready="onEditorReadyEn($event)"
                            style="max-height: 200px"
                        />
                        <InputError :message="form.errors['description.en']" class="mt-2" />
                    </div>
                    <!-- Description Arabic -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">   {{ $t('Events.Create.ar_description') }} <span class="text-red-500">*</span></label>
                        <quill-editor
                            v-model="form.description.ar"
                            @change="(event) => form.description.ar = event.html"
                            @update:content="$emit('clearError', 'about_company')"
                            @ready="onEditorReadyAr($event)"
                            style="max-height: 200px"
                        />
                        <InputError :message="form.errors['description.en']" class="mt-2" />

                    </div>
                </div>
            </PanelElement>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <input
                    v-model="form.district"
                    type="hidden"
                />
                <input
                    v-model="form.city"
                    type="hidden"
                />
                <input
                    v-model="form.region"
                    type="hidden"
                />
                <input
                    v-model="form.street"
                    type="hidden"
                />
                <input
                    v-model="form.building_number"
                    type="hidden"
                />
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end gap-2 flex-wrap">
                <GeneralBtnElement @click="saveDraft" class="bg-primary text-white border-none">
                    {{ $t('Events.Create.update_event') }}
                </GeneralBtnElement>
            </div>
        </div>
    </PortalLayout>
</template>

<script setup lang="ts">
import { ref, computed, nextTick , watch , onMounted } from 'vue';
import { useForm, router } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import moment from 'moment';
import { EventType } from '@/Pages/Portal/Speaker/Events/models/enum/eventType.model';
import { EventStatus } from '@/Pages/Portal/Speaker/Events/models/enum/eventsStatus.model';

// Component imports
import PortalLayout from '@/Layouts/PortalLayout.vue';
import InputElement from '@/Components/InputElement.vue';
import InputError from '@/Components/InputError.vue';

import PanelElement from '@/Components/PanelElement.vue';
import FileUploadElement from '@/Components/FileUploadElement.vue';
import InputLabelElement from '@/Components/InputLabelElement.vue';
import RadioInputElement from '@/Components/RadioInputElement.vue';
import DateInputElement from '@/Components/DateInputElement.vue';
import BreadcrumbElement from '@/Components/BreadcrumbElement.vue';
import GeneralBtnElement from "@/Components/GeneralBtnElement.vue";
import Multiselect from '@suadelabs/vue3-multiselect';
import { quillEditor } from 'vue3-quill';
import Swal from 'sweetalert2';

// Style imports
import '@suadelabs/vue3-multiselect/dist/vue3-multiselect.css';

const { locale } = useI18n();
// const currentLocale = computed(() => locale.value || 'en');
const currentLocale = computed(() => {
    return locale.value === "ae" ? "ar" : locale.value || "en";
});

const selectedTags = ref([]);
const optionsTags = ref([]);
const isTagSelectDisabled = ref(true);

const validateTagInput = (value) => {
    return value.replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '').slice(0, 20);
};

// Function to add a new tag
const addNewTag = async (newTag) => {
    // 1. Validation function (reusable)
    const isValidTag = (tag) => {
        const regex = /^[a-zA-Z0-9\u0600-\u06FF\s]{1,20}$/; // Allows English/Arabic letters, numbers, spaces
        return regex.test(tag);
    };

    // 2. Validate the initial tag (from multiselect input)
    if (!isValidTag(newTag)) {
        Swal.fire({
            icon: 'error',
            title: t('Events.Create.invalid_tag'),
            text: t('Events.Create.invalid_tag_error'),
            confirmButtonText: t("Common.cancel"),
        });
        return;
    }

    // 3. Show Swal prompt for localized name (with validation)
    const { value: inputValue } = await Swal.fire({
        title: t("Events.Create.enter_local_name"),
        input: "text",
        inputLabel: t("Events.Create.provide_tag_local_name", { name: newTag }),
        inputPlaceholder: t("Events.Create.local_name_placeholder"),
        inputValidator: (value) => {
            if (!isValidTag(value)) {
                return t('Events.Create.invalid_tag_error');
            }
        },
        showCancelButton: true,
        confirmButtonText: t("Common.add"),
        cancelButtonText: t("Common.cancel"),
    });

    if (!inputValue) return; // Exit if cancelled

    // 4. Create tag object
    const isEnglish = currentLocale.value === "en";
    const newTagObject = {
        name: {
            en: isEnglish ? newTag : inputValue,
            ar: isEnglish ? inputValue : newTag,
        }
    };

    // 5. Check for duplicates (case-insensitive)
    const exists = optionsTags.value.some(tag =>
        tag.name.en?.toLowerCase() === newTagObject.name.en?.toLowerCase() ||
        tag.name.ar?.toLowerCase() === newTagObject.name.ar?.toLowerCase()
    );

    if (!exists) {
        optionsTags.value.push(newTagObject);
    }

    // 6. Add to selected tags if not already present
    const isAlreadySelected = selectedTags.value.some(tag =>
        tag.name.en?.toLowerCase() === newTagObject.name.en?.toLowerCase() ||
        tag.name.ar?.toLowerCase() === newTagObject.name.ar?.toLowerCase()
    );

    if (!isAlreadySelected) {
        selectedTags.value.push(newTagObject);
    }
};
// Types
interface Category {
    id: number;
    name: string | {
        en: string;
        ar: string;
    };
}

interface FormattedCategory {
    id: number;
    name: {
        en: string;
        ar: string;
    };
}

// Props
const props = defineProps<{
    event: Event;
    organizer_id: number;
    categories: Category[]
}>();
console.log('PROPS',props)


// Selected category ref
const selectedCategory = ref<FormattedCategory | null>(null);

onMounted(() => {
    const exists = formattedCategories.value.some(
        (category) => category.id === props.event.category.id
    );
    if (!exists) {
        categories.value.push(props.event.category);
    }
    selectedCategory.value = props.event.category;

    // Convert existing image URL to File ONLY if it exists
    if (existingCoverImage.value) {
        urlToFile(existingCoverImage.value, existingCoverImage.value.split('/').pop() || 'cover_image.jpg')
            .then(file => {
                form.cover_image = file;
            })
            .catch(error => {
                console.error("Error converting URL to File:", error);
                // Handle the error appropriately (e.g., show a message to the user)
            });
    }

    // Pre-select tags if the event has tags
    if (props.event.tags && props.event.tags.length > 0) {
        // Map the event tags to the format expected by the multiselect component
        selectedTags.value = props.event.tags.map(tag => {
            return {
                id: tag.id,
                name: typeof tag.name === 'string' ? tag.name : tag.name[currentLocale.value] || tag.name.en
            };
        });

        // Enable tag selection
        isTagSelectDisabled.value = false;
    }

    // Load tags for the selected category
    if (selectedCategory.value) {
        handleCategorySelect(selectedCategory.value);
    }

});

// Type options
const typeOptions = ref([
    EventType.OFFLINE,
    EventType.ONLINE,
    EventType.HYBRID
]);

// Format categories for multiselect
const formattedCategories = computed(() => {
    return props.categories.map(category => {
        let parsedName;
        try {
            parsedName = typeof category.name === 'string'
                ? JSON.parse(category.name)
                : category.name;
        } catch (e) {
            // If parsing fails, create a default structure
            parsedName = {
                en: typeof category.name === 'string' ? category.name : '',
                ar: typeof category.name === 'string' ? category.name : ''
            };
        }

        return {
            id: category.id,
            name: parsedName
        };
    });
});

console.log('cats:',formattedCategories)

// Custom label function for category display
const getCustomLabel = (option: FormattedCategory) => {
    if (!option || !option.name) return '';
    return option.name[currentLocale.value] || option.name.en || '';
};

const getTagLabel = (tag: any) => {
    if (!tag) return '';
    // Handle existing tags (from category) and new tags (created by user)
    if (tag.name && typeof tag.name === 'object') {
        return tag.name[currentLocale.value] || tag.name.en || '';
    }
    return tag.name || '';
};

// Handle category selection
// Handle category selection, modified to load tags
const handleCategorySelect = (selected: FormattedCategory | null) => {
    selectedCategory.value = selected;
    form.category_id = selected ? selected.id : null;

    if (selected) {
        // Find the selected category in the original data to get its tags
        const categoryWithTags = props.categories.find(cat => cat.id === selected.id);

        if (categoryWithTags && categoryWithTags.matched_tags && categoryWithTags.matched_tags.length) {
            // Format tags for the multiselect component
            optionsTags.value = categoryWithTags.matched_tags.map(tag => {
                return {
                    id: tag.id,
                    name: typeof tag.name === 'string' ? tag.name : tag.name[currentLocale.value] || tag.name.en
                };
            });
            // Enable tag selection
            isTagSelectDisabled.value = false;
        } else {
            // If no tags found, keep an empty array but enable adding new ones
            optionsTags.value = [];
            isTagSelectDisabled.value = false;
        }
    } else {
        // If no category is selected, disable tag selection
        optionsTags.value = [];
        isTagSelectDisabled.value = true;
    }
};

// Watch for changes in selectedTags and update the form
watch(selectedTags, (newTags) => {
    // Convert selected tags into the required format
    form.tags = newTags.map(tag => {
        if (tag.id && typeof tag.id === 'number') {
            return {
                id: tag.id,
                name: { en: tag.name, ar: tag.name }
            };
        }
        return {
            name: typeof tag.name === 'string' ?
                { en: tag.name, ar: tag.name } : // For existing tags from category
                tag.name // For newly created tags
        };
    });

    console.log('Updated form.tags:', form.tags);
}, { deep: true });

watch(
    currentLocale,
    (newLocale) => {
        if (selectedCategory.value && !isTagSelectDisabled.value) {
            const categoryWithTags = props.categories.find(cat => cat.id === selectedCategory.value.id);
            if (categoryWithTags && categoryWithTags.matched_tags && categoryWithTags.matched_tags.length) {
                const selectedTagIds = selectedTags.value.map(tag => tag.id);
                optionsTags.value = categoryWithTags.matched_tags.map(tag => {
                    return {
                        id: tag.id,
                        name: typeof tag.name === 'string'
                            ? tag.name
                            : tag.name[newLocale] || tag.name.en
                    };
                });
                selectedTags.value = optionsTags.value.filter(tag =>
                    selectedTagIds.includes(tag.id)
                );
            }
        }
    },
    { immediate: true }
);

// Date formatting helper
const formatDate = (date: string | Date): string => {
    if (!date) return '';
    return moment(date).format('YYYY-MM-DD HH:mm:ss');
};

// Form initialization
const form = useForm({
    name: {
        en: props.event?.name?.en || '',
        ar: props.event?.name?.ar || ''
    },
    description: {
        en: props.event?.description?.en || '', // Pull from props or stay empty
        ar: props.event?.description?.ar || ''
    },
    type: props.event?.type || '',
    category_id: props.event?.category_id || '',
    status: props.event?.status || EventStatus.DRAFT,
    link: props.event?.link || '',
    start_date: props.event?.start_date || '',
    end_date: props.event?.end_date || '',
    publish_date: props.event?.publish_date || '',
    has_certificate: props.event?.has_certificate || false,
    event_hours: props.event?.event_hours || '',
    max_attendees: props.event?.max_attendees || '',
    remaining_seats: props.event?.remaining_seats || '',
    cover_image: null as File | null,
    title: props.event?.address.title || '',
    venue_name: props.event?.address.venue_name || '',
    lat: props.event?.address.lat || '',
    long: props.event?.address.long || '',
    district: props.event?.address.district || '',
    city: props.event?.address.city || '',
    region: props.event?.address.region || '',
    street: props.event?.address.street || '',
    building_number: props.event?.address.building_number || '',
    tags:''
});

const onEditorReadyEn = (e) => {
    e.container.querySelector('.ql-blank').innerHTML = props.event?.description?.en
}
const onEditorReadyAr = (e) => {
    e.container.querySelector('.ql-blank').innerHTML = props.event?.description?.ar
}

// Existing cover image URL (for preview)
const existingCoverImage = ref(props.event?.media[0]?.original_url || null);

watch(
    () => [form.cover_image, existingCoverImage.value],
    ([newCoverImage]) => {
        // Only clear existingCoverImage if a new File is selected or it's cleared directly
        if (newCoverImage instanceof File || newCoverImage === null) {
            existingCoverImage.value = null
        }
    }
);

// Convert URL to File object
const urlToFile = async (url: string, filename: string): Promise<File> => {
    const response = await fetch(url);
    const blob = await response.blob();
    return new File([blob], filename, { type: blob.type });
};

// Form submission
const submit = async () => {
    form.clearErrors();

    // Convert existing image to File if needed
    if (!form.cover_image && existingCoverImage.value) {
        const filename = existingCoverImage.value.split('/').pop() || 'cover_image.jpg';
        form.cover_image = await urlToFile(existingCoverImage.value, filename);
    }

    form.post(route('generalEvent.update', { id: props.event.id }), {
        preserveScroll: true,
        onError: (errors) => {
            console.error('Validation errors:', errors);
            nextTick(() => {
                const firstError = document.querySelector('.text-red-500, .p-error');
                if (firstError) {
                    window.scrollTo({
                        top: firstError.getBoundingClientRect().top + window.pageYOffset - 100,
                        behavior: 'smooth'
                    });
                }
            });
        },
        onSuccess: () => {
            console.log('Event updated successfully!');
            router.get(route('generalEvent.index'));
        }
    });
};

const saveDraft = () => {
    form.status = EventStatus.DRAFT;
    submit();
};

</script>

<style scoped>
.custom-multiselect {
    @apply bg-white border border-gray-300 rounded-md;
}

:deep(.multiselect__tags) {
    @apply border-0 rounded-md min-h-[42px];
}

:deep(.multiselect__single) {
    @apply my-1;
}

:deep(.multiselect__content-wrapper) {
    @apply border border-gray-300 rounded-md mt-1;
}

:deep(.multiselect__option--highlight) {
    @apply bg-primary bg-opacity-10;
}

:deep(.multiselect__option--selected) {
    @apply bg-primary text-white font-bold;
}

:deep(.multiselect__placeholder) {
    @apply text-gray-500;
}

:deep(.custom-multiselect input[type="text"]) {
    text-transform: lowercase; /* Optional: enforce case */
}
</style>
