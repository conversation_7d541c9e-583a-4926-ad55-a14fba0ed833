<template>
  <PortalLayout>
    <div class="flex flex-col gap-6">
      <BreadcrumbElement
        :title="$t('Events.events')"
        :items="[$t('home'), $t('Events.events')]"
      />
      <EventHeaderWidget
        :title="page.props.eventData.name[currentLang]"
        :date="formattedDate"
      />
      <div class="vue-form-wizard circle">
        <form @submit.prevent="handleSubmit">
          <FormWizard
            hide-buttons
            ref="formWizard"
            :editable="true"
            :tab-clickable="true"
          >
          <div class="border-b-thinner mb-5 border-gray-outline dark:border-gray-800"></div>
          <TabContent :title="$t('Events.Sessions.Create.Steps.One.label')">
            <Transition name="fade-slide">
              <div v-show="currentStep === 0" class="step-content">
                <!-- Step 1: Main Info -->
                <StepOne v-model:form-data="form" />
              </div>
            </Transition>
          </TabContent>
          <TabContent :title="$t('Events.Sessions.Create.Steps.Two.label')">
            <Transition name="fade-slide">
              <div v-show="currentStep === 1" class="step-content">
                <!-- Step 2: Main Info -->
                <StepTwo v-model:form-data="form" />
              </div>
            </Transition>
          </TabContent>
          <TabContent :title="$t('Events.Sessions.Create.Steps.Three.label')">
            <Transition name="fade-slide">
              <div v-show="currentStep === 2" class="step-content">
                <!-- Step 3: Main Info -->
                <div class="flex flex-col gap-6">
                  <div class="flex flex-col gap-4 items-center justify-center p-4 rounded-2xl shadow-soft dark:border dark:border-blue-darkness">
                    <div class="bg-green-custom rounded-full w-16 h-16 flex justify-center items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="29" height="29" viewBox="0 0 29 29" fill="none">
                        <path d="M8.38 27.2751L1.12666 20.1903C0.969773 20.0378 0.845059 19.8554 0.759898 19.6539C0.674737 19.4524 0.630859 19.2358 0.630859 19.017C0.630859 18.7982 0.674737 18.5816 0.759898 18.3801C0.845059 18.1786 0.969773 17.9962 1.12666 17.8437L3.53199 15.497C3.85455 15.1845 4.28606 15.0097 4.7352 15.0097C5.18433 15.0097 5.61584 15.1845 5.9384 15.497L10.2563 19.7178L22.3661 1.30713C22.616 0.935623 23.0005 0.675834 23.4384 0.582677C23.8764 0.48952 24.3334 0.570295 24.7128 0.807928L27.5757 2.60633C27.7627 2.72131 27.9247 2.87267 28.0521 3.05144C28.1795 3.2302 28.2697 3.43273 28.3174 3.64701C28.365 3.8613 28.3692 4.08297 28.3295 4.29887C28.2899 4.51478 28.2073 4.72054 28.0867 4.90393L13.7475 26.7109C13.4601 27.1377 13.0827 27.4964 12.6418 27.7616C12.2009 28.0268 11.7072 28.1921 11.1954 28.2459C10.6837 28.2997 10.1664 28.2407 9.68 28.0729C9.19356 27.9052 8.74981 27.6329 8.38 27.2751Z" fill="white"/>
                      </svg>
                    </div>
                    <p class="font-semibold">
                      {{ $t('Events.Sessions.Create.Steps.Three.session_add_msg') }}
                    </p>
                  </div>
                  <div class="flex gap-4 justify-end flex-wrap">
                      <Link
                          :href="route('events.show', page.props.event_id)"
                          class="py-2 px-8 border-thinner border-primary text-primary rounded-3xl inline-block text-center"
                      >
                          {{ $t('Events.Sessions.Create.Steps.Three.go_to_event') }}
                      </Link>

                      <Link
                          :href="route('events.sessions.create', page.props.event_id)"
                          class="py-2 px-8 bg-primary text-white rounded-3xl inline-block text-center"
                      >
                          {{ $t('Events.Sessions.Create.Steps.Three.add_new_session') }}
                      </Link>
                  </div>
                </div>
              </div>
            </Transition>
          </TabContent>

          <div class="wizard-footer flex mt-4">
            <button
                class="previous"
                v-if="currentStep > 0 && currentStep < stepLength"  type="button"
                @click="prevStep"
            >
                <i class="ri-arrow-left-line rotate-rtl"></i> {{ $t('auth.back') }}
            </button>

            <button
                v-if="currentStep < stepLength - 1"
                type="button" @click="nextStep" class="next">
                {{ $t('auth.next') }} <i class="ri-arrow-right-line rotate-rtl"></i>
            </button>

            <button v-if="currentStep === stepLength - 1"
                    type="submit" class="finish">
                {{ $t('auth.send') }} <i class="ri-arrow-right-line rotate-rtl"></i>
            </button>
          </div>
          </FormWizard>
        </form>
      </div>
    </div>
  </PortalLayout>
</template>

<script setup lang="ts">
import { ref, watch, computed} from 'vue';
import PortalLayout from '@/Layouts/PortalLayout.vue';
import BreadcrumbElement from '@/Components/BreadcrumbElement.vue';
import EventHeaderWidget from '@/Components/EventHeaderWidget.vue';
import { FormWizard, TabContent } from 'vue3-form-wizard';
import 'vue3-form-wizard/dist/style.css';
import StepOne from '@/Components/Portal/Organizer/EventSessions/StepOne.vue';
import StepTwo from '@/Components/Portal/Organizer/EventSessions/StepTwo.vue';
import { useForm,usePage,router } from '@inertiajs/vue3';
import dayjs from 'dayjs';
import { Link } from '@inertiajs/vue3';
import 'dayjs/locale/ar';
import { useI18n } from 'vue-i18n';

const { locale } = useI18n();

const page = usePage();
let lang;
if(locale.value=='ae'){
  lang='ar'
}

const currentLang = ref(lang);


const formattedDate = computed(() => {
  if (!page.props.eventData) return '';
  dayjs.locale(currentLang.value === 'ar' ? 'ar' : 'en');
  const date = dayjs(page.props.eventData.start_date).format('DD MMMM, YYYY');
  const time = dayjs(page.props.eventData.start_time).format('hh:mm');
  const period = Number(dayjs(page.props.eventData.start_time).format('H')) >= 12 ?
    (currentLang.value === 'ar' ? 'م' : 'PM') :
    (currentLang.value === 'ar' ? 'ص' : 'AM');

  return `${date} - ${time} ${period}`;
});


const form = useForm({
    event_id: page.props.event_id,
    name: {
      ar: '',
      en: ''
    },
    type_id: '',
    session_date: '',
    start_time: '',
    end_time: '',
    type:'',
    link:'',
    hall_name: '',
    description: {
      ar: '',
      en: ''
    },
    speakers: []
});

const currentStep = ref(0);
const stepLength = ref(2);
const formWizard = ref(null);

const nextStep = async () => {
  if (currentStep.value === 0) {
    await handleDataValedate()
  }else if (currentStep.value >= stepLength.value) {
    return;
  } else {
    currentStep.value++;
    formWizard.value?.nextTab();
  }
};

const prevStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
        formWizard.value?.prevTab();
    }
};

let formattedData ;

const handleDataValedate = async() => {
  formattedData = {
    ...form,
    type_id: form.type_id?.id,
    type: form.type?.value,
    speakers: form.speakers.map(speaker => speaker.id)
  }
  formattedData.post(route('eventSession.validated'), {

    onSuccess: () => {
      // Show success notification or redirect
      currentStep.value++;
      formWizard.value?.nextTab();
      console.log('Session created successfully');
    },
    onError: (errors) => {
      console.error('Form submission failed:', errors);
    }
  });
};

const handleSubmit = async() => {
  formattedData.post(route('eventSession.store'), {
  onSuccess: () => {
    // Show success notification or redirect
    currentStep.value++;
    formWizard.value?.nextTab();
    console.log('Session created successfully');
  },
  onError: (errors) => {
    console.error('Form submission failed:', errors);
  }
});

};

watch(locale, (newLocale) => {
  currentLang.value = newLocale === 'ae' ? 'ar' : newLocale;
});
</script>


<style scoped>
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: opacity 1s ease, transform 1s ease;
  opacity: 1;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

:deep(.vue-form-wizard .wizard-tab-content) {
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: min-height;
  position: relative;
  min-height: 300px; /* Set minimum height to prevent too quick transitions */
}

.step-content {
  width: 100%;
  position: relative;
  transition: opacity 1s ease, transform 1s ease;
}
</style>
