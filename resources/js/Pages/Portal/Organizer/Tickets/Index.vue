<script setup lang="ts">

import PortalLayout from "@/Layouts/PortalLayout.vue";
import BreadcrumbElement from "@/Components/BreadcrumbElement.vue";
import Vue3Datatable from '@bhplugin/vue3-datatable';
import {ref, computed, watch, onMounted, nextTick} from 'vue';
import { router } from "@inertiajs/vue3";
import {useI18n} from "vue-i18n";
import { useAppStore } from '@/Stores/index';
import FilterDataTableElement from "@/Components/FilterDataTableElement.vue";

const store = useAppStore();

const props = defineProps({
    tickets: {
        type: Object,
        default: () => ({ data: [] })
    },
    initialUrlParams: {
        type: Object,
        default: () => ({})
    }
});

const searchKey = ref('');
const filters = ref({});

// Handle filter changes
const handleFilterChange = (filterData) => {
    searchKey.value = filterData.searchTerm;
    filters.value = filterData.filters;

    router.get("/tickets", {
        page: 1,
        search_key: searchKey.value,
        filters: filters.value,
    }, {
        preserveState: true,
        replace: true
    });
};

const colsFilter = computed(() => [
    { field: 'status', title: t('tickets.status') },
    { field: 'to', title: t('tickets.to') },
    { field: 'ticket_name', title: t('tickets.ticket_name') },
    { field: 'attendee_name', title: t('tickets.attendee_name') },
    { field: 'min_price', title: t('tickets.min_price') },
    { field: 'max_price', title: t('tickets.max_price') },
]);



const { locale:localeLang } = useI18n();
let lang;
if(localeLang.value=='ae'){lang='ar'}else {lang='en'}
const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;
});

const search1 = ref('');

const cols = computed(() =>  [
    { field: 'ticket_id', title: t('tickets.ticket_number') },
    { field: 'price', title: t('tickets.ticket_price') },
    { field: 'ticket_name', title: t('tickets.ticket_name') },
    { field: 'level_name', title: t('tickets.ticket_level') },
    { field: 'ticket_status', title: t('tickets.ticket_status') },
    { field: 'ordered_at', title: t('tickets.ticket_date')},
    { field: 'attendee_name', title: t('tickets.ticket_owner') },
    { field: 'event_name', title: t('tickets.event_name') },
]) || [];

// Transform API Data to Match `rows` Format
const rows = computed(() => {
    return props.tickets.data.map(ticket => ({
        ticket_id: ticket.ticket_id,
        price: ticket.price,
        ticket_name: ticket.ticket_name ? JSON.parse(ticket.ticket_name)[locale.value] : "Null", // Ensure it exists
        level_name: ticket.level_name ? JSON.parse(ticket.level_name)[locale.value] : "Null",
        ticket_status: ticket.ticket_status,
        ordered_at: ticket.ordered_at,
        attendee_name: ticket.attendee_name,
        event_name: ticket.event_name ? JSON.parse(ticket.event_name)[locale.value] : "Null",
        eventAttend: ticket.attended ? "True" : "False"
    }));
});

const currentPage = ref(props.tickets?.current_page ?? 1);
const perPage = ref(props.tickets?.per_page ?? 10);
const totalRows = computed(() => props.tickets.total ?? 0);

const orderBy = ref("id"); // Default sort column
const orderDirection = ref("asc"); // Default sort direction

const changeServer = (pagination) => {

    currentPage.value = pagination.current_page;

    orderBy.value = pagination.sort_column || "id";
    orderDirection.value = pagination.sort_direction || "asc";

    console.log("Sorting By:", orderBy.value);
    console.log("Sorting Direction:", orderDirection.value);

    if (pagination.pageSize !== perPage.value) {
        perPage.value = pagination.pagesize;
    }

    router.get(`/tickets`, {
        page: pagination.current_page,
        limit: perPage.value,
        search_key: search1.value,
        order_by: orderBy.value,
        order_direction: orderDirection.value,
    }, {
        preserveState: true,
        replace: true
    });
};

const paginationInfoText = computed(() => {
    const start = (currentPage.value - 1) * perPage.value + 1;
    const end = Math.min(start + perPage.value - 1, totalRows.value);
    return t('datatable.pagination_info', [start, end, totalRows.value]);
});
</script>

<template>
    <PortalLayout>
        <div class="w-full">
            <div class="flex flex-col gap-6">

                <BreadcrumbElement
                    :title="$t('tickets.tickets')"
                    :items="[$t('home'), $t('tickets.tickets')]"
                />

                <div>
                    <div class="panel pb-0 mt-6">

                        <FilterDataTableElement
                            :initial-url-params="initialUrlParams"
                            :filter-columns="colsFilter"
                            @filter-changed="handleFilterChange"
                        />

                        <div class="datatable">
                            <vue3-datatable
                                :rows="rows"
                                :columns="cols"
                                :totalRows="totalRows"
                                :isServerMode="true"
                                :page="currentPage"
                                :pageSize="perPage"
                                @change="changeServer"
                                :paginationInfo="paginationInfoText"
                                :sortable="true"
                                :search="search1"
                                skin="whitespace-nowrap bh-table-hover"
                                firstArrow='<i class="block ri-arrow-left-double-line text-lg rtl:rotate-180"></i>'
                                lastArrow='<i class="block ri-arrow-right-double-line text-lg rtl:rotate-180"></i>'
                                previousArrow='<i class="block ri-arrow-left-s-line text-lg rtl:rotate-180"></i>'
                                nextArrow='<i class="block ri-arrow-right-s-line text-lg rtl:rotate-180"></i>'
                            >
                            </vue3-datatable>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </PortalLayout>
</template>
