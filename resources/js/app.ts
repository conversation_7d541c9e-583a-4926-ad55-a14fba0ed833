import './bootstrap';
import '../css/app.css';

import { createApp, h, DefineComponent } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';

// Pinia store
import { createPinia } from "pinia";
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

// Perfect scrollbar
import { PerfectScrollbarPlugin } from "vue3-perfect-scrollbar";

// Vue-meta
import { createHead } from "@vueuse/head";

// Default settings
import appSetting from "@/app-setting";

// Vue-i18n
import i18n from "@/i18n";


// Create a global t function
const t = i18n.global.t
// Make it available globally
window.t = t

// Add type declaration for TypeScript
declare global {
    interface Window {
        t: typeof i18n.global.t;
    }
}

// Tippy tooltips
import { TippyPlugin } from "tippy.vue";

// Input mask
import { vMaska } from "maska/vue";

// Markdown editor
import VueEasymde from "vue3-easymde";
import "easymde/dist/easymde.min.css";

// Popper
import Popper from "vue3-popper";

// JSON to Excel
import vue3JsonExcel from "vue3-json-excel";

import Vue3TelInput from 'vue3-tel-input'
import 'vue3-tel-input/dist/vue3-tel-input.css'

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob<DefineComponent>('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) });

        // Use plugins
        const pinia = createPinia();
        pinia.use(piniaPluginPersistedstate);
        const head = createHead();

        app.use(plugin)
            .use(ZiggyVue)
            .use(pinia)
            .use(PerfectScrollbarPlugin)
            .use(head)
            .use(i18n)
            .use(TippyPlugin)
            .use(VueEasymde)
            .use(vue3JsonExcel)
            .use(Vue3TelInput);

        // Add directives
        app.directive("maska", vMaska);

        // Register components
        app.component("Popper", Popper);

        // Initialize app settings
        appSetting.init();

        app.mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});
