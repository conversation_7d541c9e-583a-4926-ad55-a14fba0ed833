{"dashboard": "Dashboard", "sales": "Sales", "analytics": "Analytics", "apps": "Apps", "components": {"choose_file": "Choose <PERSON>", "browse": "Browse"}, "elements": "Elements", "font_icons": "Font Icons", "widgets": "Widgets", "tables": "Tables", "datatables": "Data Tables", "forms": "Forms", "users": "Users", "pages": "Pages", "authentication": "Authentication", "drag_and_drop": "Drag and Drop", "maps": "Maps", "charts": "Charts", "starter_kit": "Starter <PERSON>", "documentation": "Documentation", "ui_kit": "UI Kit", "more": "More", "finance": "Finance", "crypto": "Crypto", "chat": "Cha<PERSON>", "mailbox": "Mailbox", "todo_list": "Todo List", "notes": "Notes", "scrumboard": "Scrumboard", "contacts": "Contacts", "invoice": "Invoice", "list": "List", "preview": "Preview", "add": "Add", "edit": "Edit", "calendar": {"calendar": "Calendar", "month": "Month", "day": "Day", "week": "Week", "today": "Today", "event_details": "Event Details"}, "tabs": "Tabs", "accordions": "Accordions", "modals": "Modals", "cards": "Cards", "carousel": "Carousel", "countdown": "Countdown", "counter": "Counter", "sweet_alerts": "<PERSON> Alerts", "timeline": "Timeline", "notifications": "Notifications", "media_object": "Media Object", "list_group": "List Group", "pricing_tables": "Pricing Tables", "lightbox": "Lightbox", "alerts": "<PERSON><PERSON><PERSON>", "avatar": "Avatar", "badges": "Badges", "breadcrumbs": "Breadcrumbs", "buttons": "Buttons", "button_groups": "Button Groups", "color_library": "Color Library", "dropdown": "Dropdown", "infobox": "Infobox", "jumbotron": "Jumbotron", "loader": "Loader", "pagination": "Pagination", "popovers": "Popovers", "progress_bar": "Progress Bar", "search": "Search", "tooltips": "Tooltips", "treeview": "Treeview", "typography": "Typography", "basic": "Basic", "order_sorting": "Order Sorting", "multi_column": "Multi Column", "multiple_tables": "Multiple Tables", "alt_pagination": "Alt. Pagination", "range_search": "Range Search", "export": "Export", "input_group": "Input Group", "layouts": "Layouts", "validation": "Validation", "input_mask": "Input Mask", "select2": "Select2", "touchspin": "Touchspin", "checkbox_and_radio": "Checkbox & Radio", "switches": "Switches", "wizards": "Wizards", "file_upload": "File Upload", "quill_editor": "Quill Editor", "markdown_editor": "Markdown Editor", "date_and_range_picker": "Date & Range Picker", "clipboard": "Clipboard", "user_and_pages": "User And Pages", "profile": {"profile": "Profile", "basic_info": "Basic Information", "download_cv": "View CV", "download_portfolio": "View Portfolio", "rating": "Rating", "no_website": "No Website", "no_files": "No files available."}, "account_settings": "Account <PERSON><PERSON>", "knowledge_base": "Knowledge Base", "contact_form": "Contact Form", "faq": "Faq", "coming_soon": "Coming Soon", "error": "Error", "maintenence": "Maintenence", "login_boxed": "Login Boxed", "register_boxed": "Register Boxed", "unlock_boxed": "Unlock Boxed", "recover_id_boxed": "Recover Id Boxed", "login_cover": "<PERSON>gin <PERSON>", "register_cover": "Register Cover", "unlock_cover": "Unlock Cover", "recover_id_cover": "Recover Id Cover", "supports": "Supports", "login": "<PERSON><PERSON>", "lockscreen": "Lockscreen", "password_recovery": "Password Recovery", "register": "Register", "404": "404", "500": "500", "503": "503", "user_interface": "User Interface", "tables_and_forms": "Tables And Forms", "columns_filter": "Columns Filter", "column_chooser": "<PERSON><PERSON><PERSON>", "advanced": "Advanced", "checkbox": "Checkbox", "skin": "Skin", "sticky_header": "<PERSON><PERSON>", "clone_header": "<PERSON><PERSON>", "coming_soon_boxed": "Coming Soon Boxed", "coming_soon_cover": "Coming Soon Cover", "contact_us_boxed": "Contact Us Boxed", "contact_us_cover": "Contact Us Cover", "optional": "Optional", "copyright": "© 2024 All rights reserved. Powered by ", "terms_conditions": "Terms & conditions", "privacy_policy": "Privacy & policy", "currency": {"sar": "SAR"}, "Common": {"add": "Add", "cancel": "Cancel", "delete": "Delete", "press_enter_to_select": "press enter to select", "press_enter_to_remove": "press enter remove", "selected": "selected", "delete_confirm_title": "Are you sure ?", "actions": "Actions", "confirm": "Confirm"}, "attendanceType": {"online": "Online", "in_person": "In Person", "hybrid": "Hybrid", "offline": "Offline"}, "certificateType": {"attendance": "Attendance Certificate", "completion": "Completion Certificate", "participation": "Participation Certificate"}, "duration": "Duration", "hours": "hours", "auth": {"forget_password": "Forgot your password?", "login": "login", "sign_out": "Sign Out", "login_apple": "Login With Apple", "login_google": "Login With Google", "create_account_attende": "Create account as attende ?", "create_account_speaker_organizer": "Create an account as a speaker or an organizer?", "or": "or", "new_user": "New user", "remember_me": "Remember me", "email": "email", "password": "password", "dira_cast": "<PERSON><PERSON>", "welcome_back": "Welcome back to the activities as you like", "enter_email": "Enter your email", "confirmation_code_will_send": "A confirmation code will be sent to", "send": "Send", "back_to": "Back To", "confirm": "Confirm", "send_code_again_after": "Send the code again after", "sec": "Sec", "code_not_received": "The code did not arrive", "send_code_again": "Send code again ?", "type_confirm_code": "Enter the confirmation code", "email_sent_to": "An email has been sent to", "enter_confirmation_code": "Enter the confirmation code", "create_new_password": "Create a new password", "confirm_password": "Confirm Password", "portfolio": "Portfolio", "establishment_doc": "Exhibition establishment document", "company_logo": "Company Logo", "main_info": "Main Info", "about_you": "About You", "about_you_info": "Tell us breif about You", "attachments": "Attachments", "thank_you": "Thank You", "display_name": "Desired Display Name", "cv": "CV", "profile_pic": "Profile Pic", "experience_certificates": "Experience Certificates", "first_name": "First Name", "last_name": "Last Name", "create_account": "Create Account", "organizer": "Organizer", "speaker": "Speaker", "company_name": "Company Name", "website": "Website", "commercial_number": "Commercial registration number", "tax_number": "Tax Number", "about_company": "Tell us breif about your company", "next": "Next", "back": "Back", "back_home": "Back Home", "request_sent": "Your request has been successfully created", "request_sent_desc": "Our team will review and verify the accuracy of the data and contact you via email", "phone": "phone", "phone_placeholder": "Enter Phone Number", "Password_requirements": "Password Requirements", "one_uppercase_letter": "One Uppercase letter", "one_number": "One Number", "one_special_character": "One Special character", "at_least_characters": "At least 8 characters", "attendee_registration": "Attendee Registration", "register_as_attendee": "Register as Attendee", "registering": "Registering", "already_have_account": "Already have an account?", "register_as_different_type": "Want to register as a different type?", "register_as_organizer_or_speaker": "Register as Organizer or Speaker", "attendee_request_sent": "Your attendee registration has been successfully created", "attendee_request_sent_desc": "Welcome to Dira Cast! An activation email has been sent to you. Please activate your email to complete your registration"}, "sava_as_draft": "Save as Draft", "home": "Home", "houre": "<PERSON><PERSON>", "navigation": {"home": "Home", "events": "Events", "event_organizers": "Event Organizers", "contact_us": "Contact Us"}, "sidebar": {"tickets": "Tickets", "ratings": "Ratings", "calendar": "Calendar", "settings": "Settings"}, "riyal": "Riyal", "Events": {"event_name": "Event Name", "event_description": "Event Description", "no_events": "No Events Available", "no_events_check_back": "Check back later for exciting new events!", "latest_events": "Latest Events", "discover_new_events": "Discover our newest and most exciting events happening soon", "start": "Start", "end": "End", "events": "Events", "noResults": "No Result", "Status": {"all": "All", "coming": "Coming", "now": "Now", "past": "Past", "cancelled": "Cancelled", "canceled": "Canceled", "published": "Published", "ended": "Ended", "draft": "DRAFT"}, "Type": {"online": "Online", "offline": "Offline", "hybrid": "Hybrid"}, "search": "Search for Events", "add": "Add Event", "edit": "Edit Event", "delete": "Delete Event", "delete_confirmation": "Are you sure you want to remove event", "cancel_event": "Cancel Event", "cancel_confirmation": "Are you sure you want to cancel event", "publish_failed": "Publish Failed", "numberOfAttendees": "Number of attendees", "view_details": "View Details", "speaker_alt": "Speaker <PERSON>", "event_alt": "Event Image", "date_label": "Date", "location_label": "Location", "details": "Event Details", "event_evaluation": "Event Evaluation", "publish_event": "Publish Event", "published_successfully": "Event Published Successful", "deleted_successfully": "Event Deleted Successful", "delete_failed": "Delete failed", "cancel_failed": "Cancel failed", "cancelled_successfully": "Event Cancelled Successful", "tags": "Tags", "Create": {"about_event_description": "Tell us about yourself", "about_event": "About the Event", "event_name": "Event Name", "event_name_placeholder": "Display Name", "event_rank": "Event Rank", "event_rank_placeholder": "Choose Event Rank", "organizer_name": "Event Organizer", "organizer_placeholder": "Organization Name", "cover_image": "Event Cover Image", "certificate_hours": "Certificate and Hours", "attendance_certificate": "Attendance Certificate", "yes": "Yes", "no": "No", "event_hours": "Event Hours", "hours_placeholder": "Choose display time period", "event_duration": "Event Duration", "start_date": "Start Date", "end_date": "End Date", "publish_date": "Platform Publish Date", "date_placeholder": "Choose time period", "attendance": "Event Attendance", "attendance_method": "Attendance Method", "in_person": "In Person", "online": "Online", "offline": "Offline", "hybrid": "Hybrid", "event_location": "Event Location", "location_placeholder": "Display Name", "map_location": "Map Location", "map_placeholder": "Enter Google Maps Location", "max_attendees": "Maximum Attendees", "max_attendees_placeholder": "<PERSON><PERSON> allowed attendance number", "remaining_seats": "Remaining Seats for Limited Places", "remaining_seats_placeholder": "<PERSON>ose remaining attendance number to show limited places", "create_event": "Create Event", "update_event": "Update Event", "description": "Description Your Event", "save_as_draft": "Save as Draft", "category": "Category", "category_placeholder": "Select a category", "en_description": "Enter event description in English", "ar_description": "أدخل وصف الحدث باللغة العربية", "tags": "Tags", "event_add_tag_placeholder": "Select Or Add New Tag ...", "please_select_category_first": "Please select category first", "select_category_first": "select category first", "provide_tag_local_name": "Provide the Arabic name", "enter_local_name": "Enter Arabic name", "local_name_placeholder": "Arabic name ....", "venue_name": "Venue Name", "venue_name_placeholder": "Enter Venue Name", "invalid_tag": "Invalid Tag", "invalid_tag_error": "Tags can only contain letters, numbers, and spaces (max 20 characters)."}, "features": {"tabs": {"price": {"label": "Price"}, "address": {"label": "Location"}, "organizer": {"label": "Organizer"}, "about": {"label": "About"}}}, "Ticketing": {"remaining_tickets": "Remaining Tickets", "remaining_tickets_placeholder": "From the total tickets for the event", "ticket": "Ticket", "total_number_of_tickets_for_level": "Total number of tickets for the level", "from_total_number_of_tickets_for_level": "From Total number of tickets for the level", "from": "From", "to": "To", "Create": {"Steps": {"One": {"label": "Ticket Levels", "description": "Specify ticket sales levels", "add_new_level": "Add New Level", "Form": {"level_name": "Level Name", "level_name_placeholder": "Level Name", "start_date": "Start Date", "end_date": "End Date", "date_placeholder": "Select Date"}}, "Two": {"label": "Tickets and Features", "description": "Add available tickets and their features", "add_new_package": "Add a new package", "Form": {"ticket_name_ar": "Ticket Name (arabic)", "ticket_name_en": "Ticket Name (english)", "ticket_name_placeholder": "Ticket Name", "number_of_tickets": "Number of tickets available", "number_of_tickets_placeholder": "Ex: 100 ticket", "basic_ticket_price": "Basic ticket price", "basic_ticket_price_placeholder": "Enter ticket price", "ticket_description_ar": "Ticket Description (arabic)", "ticket_description_en": "Ticket Description (english)", "ticket_description_placeholder": "Brief description of the package"}}, "Three": {"label": "Prices and Discounts", "description": "Specify prices and discounts for each package", "Form": {"ticket_price": "Ticket price", "ticket_price_placeholder": "Enter ticket price", "number_of_tickets": "Number of tickets available", "number_of_tickets_placeholder": "Ex: 100 ticket"}}, "Four": {"label": "Review", "go_to_dashboard": "Go to Dashboard", "go_to_event": "Go to event"}, "Five": {"label": "Thank You"}}}, "search": "Search"}, "pagination": {"number_of_results": "Number of results", "of": "of"}, "Sessions": {"session_from": "From", "session_to": "To", "session_details": "Session Details", "session_speakers": "Session Speakers", "today": "Today", "hall": "Hall", "specialty": "Specialty", "search_sessions": "Search Sessions or Categories", "delete": "Delete Session", "cancel": "Cancel Session", "confirm_delete": "Are you sure you want to delete this session?", "confirm_cancel": "Are you sure you want to cancel this session?", "deleted_successfully": "Session deleted successfully", "canceled_successfully": "Session canceled successfully", "updated_successfully": "Session updated successfully", "delete_failed": "Session Delete failed", "cancel_failed": "Session Cancel failed", "Status": {"pending": "Pending", "active": "Active", "ended": "Ended", "canceled": "Canceled"}, "Create": {"Steps": {"One": {"label": "Program", "description": "Manage event sessions", "PanelLabel": {"session_details": "Session details", "session_time": "Session time", "session_attend": "Attend the session", "session_speakers": "Session speakers"}, "Form": {"ar_session_name": "Session name in Arabic", "en_session_name": "Session name in English", "session_type": "Session type", "session_type_placeholder": "Choose session type", "session_date": "Session date", "session_date_placeholder": "Select Session date", "session_start": "Session start", "session_end": "Session end", "date_placeholder": "Select Date", "type_of_attendance": "Type of attendance", "type_of_attendance_placeholder": "Choose type of attendance", "hall_name": "Hall name", "hall_name_placeholder": "Enter Hall name", "attendance_link": "Attendance link", "session_description_ar": "Tell us about the session in a few sentences  (Arabic)", "session_description_en": "Tell us about the session in a few sentences (English)", "session_speakers": "Session speakers", "session_speakers_placeholder": "Enter speaker email", "speaker_error_msg": "There is no speaker on the platform with this email", "speaker_already_add_error_msg": "This speaker is already added"}, "Button": {"quick_add": "Add Speaker"}, "Modal": {"quick_add_speaker": "Quick Add Speaker", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "phone": "Phone Number"}}, "Two": {"label": "Review", "from_hour": "from", "to_hour": "to"}, "Three": {"label": "Thank You", "session_add_msg": "The session has been added successfully", "add_new_session": "Add a new session", "go_to_event": "Go to event"}}}}, "pricing_plan": "Pricing Plan", "event_program": "Event Program", "attendance": {"start_date": "Start Date", "end_date": "End Date", "eventDate": "Event Date", "location": "Location"}, "price_plans": {"title": "Price Plans", "available_features": "Available Features", "available_tickets": "Available Tickets", "no_tickets_available": "No Tickets available For That Event", "ticket_available": "Ticket Available", "discount": "Discount"}, "agenda": {"title": "Event Agenda", "description": "Here's the detailed schedule and program for this event"}}, "tickets": {"tickets": "Tickets", "ticket_number": "Ticket Number", "ticket_price": "Ticket Price", "ticket_name": "Ticket Name", "ticket_level": "Ticket Level", "ticket_status": "Ticket Status", "ticket_date": "Ticket Date", "ticket_owner": "Ticket Owner", "event_name": "Event Name", "event_attend": "Event Attend", "search_placeholder": "Search for event or category", "attendee_name": "Ticket Owner", "to": "Ticket Date", "status": "Ticket Status", "enter_search_value": "Enter search value", "filters": "Filters", "min_price": "<PERSON>", "max_price": "Max Price"}, "actions": "Actions", "view": "View", "delete": "Delete", "reviews": {"reviews": "Reviews", "search_placeholder": "Search for reviews", "id": "ID", "user": "User", "email": "Email", "subject": "Subject", "comment": "Comment", "rate": "Rate", "created": "Created", "actions": "Actions", "event": "Event", "delete_confirm_message": "This will delete Comment !", "review_deleted": "review has been deleted."}, "levels": {"levels": "levels", "search_placeholder": "Search for levels", "level_number": "Level Number", "level_name": "Level Name", "add_level": "Add Level", "level_name_en": "Level Name (EN)", "level_name_ar": "Level Name (AR)", "create_level": "Create Level", "delete_confirm_message": "This will delete the level!", "level_deleted": "Level has been deleted.", "is_active": "Is Active"}, "datatable": {"pagination_info": "Showing {0} to {1} of {2} entries"}, "validations": {"password_rule": "password : at least 8 characters with lowercase & uppercase letters."}, "portal": {"total_orders": "Total Orders", "total_levels": "Total Levels", "total_events": "Total Events", "level": "Level", "order": "Order", "event": "Event", "events": "Events"}}