export interface EventCardProps {
    date: string;
    location: string;
    title: string;
    speaker: string;
    speakerImage: string;
    eventImage: string;
}

export interface Event extends EventCardProps {
    id: number;
    name: {
        ar: string;
        en: string;
    };
    description: {
        ar: string;
        en: string;
    };
    start_date: string;
    end_date: string;
    type: string;
    status: string;
    event_hours: number;
    has_certificate: boolean;
    max_attendees: number;
    address?: {
        title: string;
    };
    organizer?: {
        name: string;
        profile_photo_url: string;
    };
    media?: Array<{
        original_url: string;
    }>;
    category?: {
        name: {
            ar: string;
            en: string;
        };
    };
    organizer?: Organizer;
    speakers?: Speakers;
}

export interface Tab {
    id: string;
    label: string;
    icon: string;
}

export interface TabContent {
    title: string;
    description: string;
}

export interface TabContents {
    [key: string]: TabContent;
}

export interface Review {
    id: number;
    date: string;
    name: string;
    rating: number;
    comment: string;
}

export type TabId = 'similar' | 'presenter' | 'attendance' | 'about' | 'price' | 'address' | 'organizer';

export type EventStatus = 'active' | 'completed' | 'cancelled' | 'pending';
export type AttendanceType = 'online' | 'in-person' | 'hybrid';
export type CertificateType = 'attendance' | 'completion' | 'achievement';

export interface StatusBadgeProps {
    status: EventStatus;
    category: string;
    variant: string;
}

export interface EventFeatureProps {
    rating: number;
    certificateImage: string;
    duration: string;
    attendanceType: AttendanceType;
    certificateType: CertificateType;
}

export interface TabProps {
    tabs: Tab[];
    defaultTab: TabId;
}

export interface TabContentProps {
    title: string;
    description: string;
}

export interface ReviewCardProps {
    id: number;
    created_at: string;
    user: object;
    rate: number;
    comment: string;
}

export interface ReviewSliderProps {
    title: string;
    totalSlides: number;
    slidesToShow: number;
    children?: any;
}
interface Organizer {
    name: string;
    profile_photo_url?: string;
    rating?: number;
    description?: string;
}

interface  Speakers{
    name: string;
    profile_photo_url?: string;
}
