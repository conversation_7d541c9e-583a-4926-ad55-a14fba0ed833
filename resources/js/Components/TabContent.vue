<template>
    <div class="h-24 w-full rounded-2xl flex-col justify-start items-end gap-4 inline-flex my-6">
        <div class="self-stretch h-8 flex-col justify-center items-end flex overflow-hidden">
            <div class="self-stretch text-start text-gray-text dark:text-blue-muted text-base font-semibold font-cairo">
                {{ title }}
            </div>
        </div>
        <div class="self-stretch text-gray-secondary dark:text-gray-500 text-sm font-normal font-cairo text-start mt-4">
            {{ description }}
        </div>
    </div>
</template>

<script setup lang="ts">
import type { TabContentProps } from '@/types/event';

defineProps<TabContentProps>();
</script>
