<template>
    <div class="event-features min-h-[98px] flex flex-wrap sm:flex-nowrap justify-end items-center gap-3 sm:gap-6 w-full">
        <div
            v-for="(feature, index) in features"
            :key="index"
            class="feature-card w-[calc(50%-0.375rem)] sm:w-full py-3 sm:py-4 bg-gray-background dark:bg-black rounded-lg flex-col justify-center items-center gap-2 inline-flex
               hover:bg-teal-100 dark:hover:bg-primary transition-colors duration-300 cursor-pointer group"
            @click="$emit('feature-click', feature)"
        >
            <div class="w-8 h-8 relative overflow-hidden">
                <component
                    :is="feature.icon"
                    class="w-6 h-6 text-teal-600 group-hover:dark:text-teal-100 transform transition-transform duration-300 hover:scale-110"
                />
            </div>
            <div class="text-center text-gray-800 text-sm font-semibold font-cairo dark:text-blue-muted group-hover:dark:text-white">
                {{ feature.label }}
            </div>
        </div>
        <div class="certificate-image w-full sm:w-[250px] h-[97.33px] relative">
            <div class="absolute inset-0"></div>
            <img
                src="/assets/images/ballon.png"
                alt="Certificate"
                class="w-[60px] h-full absolute left-[1.52px] top-0 object-contain"
                @error="handleImageError"
            />
            <div
                v-if="imageError"
                class="absolute inset-0 flex items-center justify-center bg-gray-100"
            >
                <ImageOff class="w-6 h-6 text-gray-400" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Star, Users, Award, Clock, ImageOff } from 'lucide-vue-next';
import type { Component } from 'vue';
import { useI18n } from "vue-i18n";

const { t } = useI18n();

interface EventFeature {
    icon: Component;
    label: string;
    value: string | number;
}

interface Props {
    rating?: number;
    duration?: string;
    attendanceType?: string;
    certificateType?: string;
}

const props = withDefaults(defineProps<Props>(), {
    rating: 3.5,
    duration: '30',
    attendanceType: 'in_person',
    certificateType: 'attendance'
});

const emit = defineEmits<{
    (e: 'feature-click', feature: EventFeature): void;
}>();

const imageError = ref<boolean>(false);

const features = computed<EventFeature[]>(() => [
    {
        icon: Star,
        label: t('rating', { rating: props.rating }),
        value: props.rating
    },
    {
        icon: Users,
        label: t(`attendanceType.${props.attendanceType}`),
        value: props.attendanceType
    },
    {
        icon: Award,
        label: t(`certificateType.${props.certificateType}`),
        value: props.certificateType
    },
    {
        icon: Clock,
        label: t('duration', { hours: props.duration }),
        value: props.duration
    }
]);

const handleImageError = (): void => {
    imageError.value = true;
};
</script>

<style scoped>
.font-cairo {
    font-family: 'Cairo', sans-serif;
}

:deep([dir="rtl"]) .event-features {
    direction: rtl;
}

:deep([dir="rtl"]) .left-\[1\.52px\] {
    left: auto;
    right: 1.52px;
}
</style>
