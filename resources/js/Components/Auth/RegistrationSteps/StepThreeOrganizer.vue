<template>

    <div class="grid gap-4">

        <div class="grid">
            <FileUploadElement
                :label="$t('auth.company_logo')"
                name="company_logo"
                uploadId="companyLogo"
                v-model="modelValue.company_logo"
                accept="image/*"
                maxFileSize="5242880"
                @update:modelValue="$emit('clearError', 'company_logo')"
            />
            <InputError :message="errors?.company_logo" />
        </div>

        <div class="grid">
            <FileUploadElement
                :label="$t('auth.establishment_doc')"
                name="establishment_doc"
                uploadId="establishmentDoc"
                v-model="modelValue.establishment_doc"
                accept=".pdf"
                maxFileSize="10485760"
                @update:modelValue="$emit('clearError', 'establishment_doc')"
            />
            <InputError :message="errors?.establishment_doc" />
        </div>

        <div class="grid">
            <FileUploadElement
                :label="$t('auth.portfolio')"
                name="portfolio"
                uploadId="portfolio"
                v-model="modelValue.portfolio"
                accept=".pdf"
                maxFileSize="10485760"
                @update:modelValue="$emit('clearError', 'portfolio')"
            />
            <InputError :message="errors?.portfolio" />
        </div>

    </div>

</template>
<script setup lang="ts">
import FileUploadElement from "@/Components/FileUploadElement.vue";
import InputError from "@/Components/InputError.vue";

import { defineProps } from "vue";

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    },
    errors: {
        type: Object,
        default: () => ({})
    }
});

defineEmits(['update:modelValue', 'clearError']);

</script>
