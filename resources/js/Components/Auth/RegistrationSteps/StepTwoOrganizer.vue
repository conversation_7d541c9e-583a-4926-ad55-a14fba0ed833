<template>

    <div class="grid gap-4">

        <div class="grid">
            <InputElement
                v-model="modelValue.company_name"
                name="company_name"
                :placeholder="$t('auth.company_name')"
                @input="$emit('clearError', 'company_name')"
                type="text"
                autocomplete="off"
                icon="ri-community-line"
            />
            <InputError :message="errors?.company_name" />
        </div>

        <div class="grid">

            <InputElement
                v-model="modelValue.website"
                name="website"
                :placeholder="$t('auth.website')"
                @input="$emit('clearError', 'website')"
                type="text"
                autocomplete="off"
                icon="ri-global-line"
            />
            <InputError :message="errors?.website" />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">

            <div>
                <InputElement
                    v-model="modelValue.commercial_number"
                    name="commercial_number"
                    :placeholder="$t('auth.commercial_number')"
                    @input="$emit('clearError', 'commercial_number')"
                    type="text"
                    autocomplete="off"
                    icon="ri-draft-line"
                />
                <InputError :message="errors?.commercial_number" />
            </div>
            <div>
                <InputElement
                    v-model="modelValue.tax_number"
                    name="tax_number"
                    :placeholder="$t('auth.tax_number')"
                    @input="$emit('clearError', 'tax_number')"
                    type="text"
                    autocomplete="off"
                    icon="ri-file-list-3-line"
                />
                <InputError :message="errors?.tax_number" />
            </div>

        </div>

        <div class="grid">
            <PanelElement :title="$t('auth.about_company')">
                <quill-editor
                    v-model="modelValue.about_company"
                    @change="(event) => modelValue.about_company = event.html"
                    @update:content="$emit('clearError', 'about_company')"
                    name="about_company"
                    style="max-height: 200px"
                />
                <InputError :message="errors?.about_company" />
            </PanelElement>
        </div>

    </div>

</template>
<script setup lang="ts">

import InputError from "@/Components/InputError.vue";
import PanelElement from "@/Components/PanelElement.vue";
import InputElement from "@/Components/InputElement.vue";
import { quillEditor } from 'vue3-quill';

import { defineProps } from "vue";

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    },
    errors: {
        type: Object,
        default: () => ({})
    }
});

defineEmits(['update:modelValue', 'clearError']);
</script>
