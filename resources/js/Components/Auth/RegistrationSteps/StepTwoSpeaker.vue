<template>

    <div class="grid gap-4">

        <div class="grid">
            <InputElement
                v-model="modelValue.display_name"
                name="display_name"
                :placeholder="$t('auth.display_name')"
                @input="$emit('clearError', 'display_name')"
                type="text"
                autocomplete="off"
                icon="ri-community-line"
            />
            <InputError :message="errors?.display_name" />
        </div>

        <div class="grid">

            <InputElement
                v-model="modelValue.website"
                name="website"
                :placeholder="$t('auth.website')"
                @input="$emit('clearError', 'website')"
                type="text"
                autocomplete="off"
                icon="ri-global-line"
            />
            <InputError :message="errors?.website" />
        </div>

        <div class="grid">
            <PanelElement :title="$t('auth.about_you_info')">
                <quill-editor
                    v-model="modelValue.about_you"
                    @change="(event) => modelValue.about_you = event.html"
                    @update:content="$emit('clearError', 'about_you')"
                    name="about_you"
                    style="max-height: 200px"
                />
                <InputError :message="errors?.about_you" />
            </PanelElement>
        </div>

    </div>

</template>
<script setup lang="ts">

import InputError from "@/Components/InputError.vue";
import PanelElement from "@/Components/PanelElement.vue";
import InputElement from "@/Components/InputElement.vue";
import { quillEditor } from 'vue3-quill';

import { defineProps } from "vue";

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    },
    errors: {
        type: Object,
        default: () => ({})
    }
});

defineEmits(['update:modelValue', 'clearError']);

</script>
