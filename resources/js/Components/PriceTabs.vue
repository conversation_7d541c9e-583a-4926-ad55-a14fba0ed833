<script setup lang="ts">
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue';
import PriceCard from "@/Components/PriceCard.vue";
import { computed,ref,watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { locale } = useI18n();
let lang;
if(locale.value=='ae'){
  lang='ar'
}else if (locale.value) {
  lang = locale.value;
}

const currentLang = ref(lang);

watch(locale, (newLocale) => {
    currentLang.value = newLocale === 'ae' ? 'ar' : newLocale;
});

interface Feature {
    text: string;
    available: boolean;
}

interface PricingPlan {
    disscount: number | null;
    title: string;
    price: string;
    originalPrice: string;
    ticketsCount: number;
    description: string;
    features: Feature[];
}

interface Tab {
    name: string;
    plans: PricingPlan[];
}

const props = defineProps<{
  eventData: any[]
}>();

const tabs = computed(() => {
  return props.eventData.map(level => ({
    name: level.level.name,
    plans: level.ticket_levels.map(ticket => ({
      disscount: ticket.discount_percentage,
      title: ticket.ticket.name,
      price: ticket.discount_price,
      originalPrice: `${ticket.main_price} ${t('currency.sar')}`,
      ticketsCount: ticket.count,
      description: ticket.ticket.description || '',
      features: ticket.sessions.map(session => ({
        text: session.name,
        available: session.is_available
      }))
    }))
  }));
});
</script>

<template>
  <TabGroup as="div" class="mb-5" v-model="selectedIndex">
    <TabList class="flex flex-wrap gap-0 justify-start border-b border-gray-200 dark:border-gray-700">
      <Tab v-for="(tab,index) in tabs" :key="index" as="template" v-slot="{ selected }">
        <button
          class="px-8 py-4 text-sm transition-colors duration-200 relative"
          :class="{
            'text-primary border-b-2 border-primary bg-[#F5F9F9]': selected,
            'text-gray-500 hover:text-gray-700': !selected
          }"
        >
          {{ tab.name[currentLang] }}
        </button>
      </Tab>
    </TabList>
    <TabPanels class="flex-1 text-sm">
      <TabPanel v-for="tab in tabs" :key="tab.name">
        <div class="mt-6 mb-8">
          <div class="rounded-small p-4">
            <div class="flex items-center justify-between gap-4">
              <div class="flex items-center gap-2">
                <span class="text-gray-text dark:text-gray-400">{{ tab.name[currentLang] }}</span>
                <button class="p-1">
                  <!-- Info icon SVG remains unchanged -->
                </button>
              </div>
              <div class="flex items-center gap-4">
                <div class="py-2 px-4 rounded-small bg-primary-default-light">
                  <div class="text-primary font-medium text-lg mb-1 text-center">
                    {{ tab.plans.reduce((sum, plan) => sum + plan.ticketsCount, 0) }} تذكرة
                  </div>
                  <div class="text-gray-secondary text-sm">
                      {{ $t('Events.Ticketing.total_number_of_tickets_for_level') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <PriceCard :plans="tab.plans" />
      </TabPanel>
    </TabPanels>
  </TabGroup>
</template>
