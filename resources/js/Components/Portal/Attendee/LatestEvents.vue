<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Link } from '@inertiajs/vue3';
import { Carousel, Slide, Pagination, Navigation } from 'vue3-carousel';
import 'vue3-carousel/dist/carousel.css';
import { useI18n } from 'vue-i18n';

interface Event {
    id: number;
    name: any; // JSON field
    description: any; // JSON field
    type: string;
    status: string;
    start_date: string;
    end_date: string;
    media?: any[];
}

interface Props {
    events: Event[];
}

const props = defineProps<Props>();

// Use i18n for reactive locale management
const { locale: localeLang } = useI18n();

let lang;
if(localeLang.value == 'ae'){
    lang = 'ar'
} else {
    lang = 'en'
}

const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;
});

// Get current locale for RTL support (now reactive)
const isRTL = computed(() => {
    return locale.value === 'ar';
});

// Carousel settings
const carouselSettings = computed(() => ({
    itemsToShow: 4,
    snapAlign: 'start',
    wrapAround: true,
    autoplay: false,
    pauseAutoplayOnHover: true,
    dir: isRTL.value ? 'rtl' : 'ltr',
    breakpoints: {
        // Mobile
        320: {
            itemsToShow: 1,
            snapAlign: 'center',
        },
        // Tablet
        768: {
            itemsToShow: 2,
            snapAlign: 'start',
        },
        // Small Desktop
        1024: {
            itemsToShow: 3,
            snapAlign: 'start',
        },
        // Large Desktop
        1280: {
            itemsToShow: 4,
            snapAlign: 'start',
        },
    },
}));

// Helper functions (now reactive to locale changes)
const getEventName = (event: Event) => {
    if (typeof event.name === 'string') return event.name;
    if (typeof event.name === 'object' && event.name) {
        // Prefer current locale, fallback to other language
        if (isRTL.value) {
            return event.name.ar || event.name.en || t('Events.event_name');
        } else {
            return event.name.en || event.name.ar || t('Events.event_name');
        }
    }
    return t('Events.event_name');
};

const getEventDescription = (event: Event) => {
    if (typeof event.description === 'string') return event.description;
    if (typeof event.description === 'object' && event.description) {
        // Prefer current locale, fallback to other language
        if (isRTL.value) {
            return event.description.ar || event.description.en || t('Events.event_description');
        } else {
            return event.description.en || event.description.ar || t('Events.event_description');
        }
    }
    return t('Events.event_description');
};

const getEventImage = (event: Event) => {
    if (event.media && event.media.length > 0) {
        return event.media[0].original_url || '/assets/images/default-event.jpeg';
    }
    return '/assets/images/default-event.jpeg';
};

function handleImageError(event) {
    const target = event.target as HTMLImageElement;
    target.src = '/assets/images/default-event.jpeg';
}

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    // Use current locale for date formatting
    const locale = isRTL.value ? 'ar-US' : 'en-US';
    return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'published':
            return 'bg-green-100 text-green-800';
        case 'draft':
            return 'bg-yellow-100 text-yellow-800';
        case 'cancelled':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <div class="latest-events-section py-8">
        <!-- Section Header -->
        <div class="container mx-auto px-4 mb-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    {{ $t('Events.latest_events') }}
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    {{ $t('Events.discover_new_events') }}
                </p>
            </div>
        </div>

        <!-- Events Carousel -->
        <div class="container mx-auto px-4" v-if="events && events.length > 0">
            <Carousel v-bind="carouselSettings" class="events-carousel">
                <Slide v-for="event in events" :key="event.id" class="px-3">
                    <div class="event-card bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden  w-full h-full">
                        <!-- Event Image -->
                        <div class="relative h-48 overflow-hidden">
                            <img
                                :src="getEventImage(event)"
                                :alt="getEventName(event)"
                                class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                                @error="handleImageError"
                            />
                            <!-- Status Badge -->
                            <div class="absolute top-3 right-3">
                                <span :class="getStatusColor(event.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                                    {{ $t(`Events.Status.${event.status}`) }}
                                </span>
                            </div>
                            <!-- Type Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                    {{ $t(`Events.Type.${event.type}`) }}
                                </span>
                            </div>
                        </div>
                        <!-- Event Content -->
                        <div class="p-6 event-content">
                            <!-- Event Title -->
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3 line-clamp-2">
                                {{ getEventName(event) }}
                            </h3>

                            <!-- Event Description -->
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                                {{ getEventDescription(event) }}
                            </p>

                            <!-- Event Dates -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="ri-calendar-2-fill text-base me-2"></i>
                                    <span>{{ $t('Events.start') }}: {{ formatDate(event.start_date) }}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="ri-calendar-2-fill text-base me-2"></i>
                                    <span>{{$t('Events.end') }}: {{ formatDate(event.end_date) }}</span>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="mt-auto">
                                <Link
                                    :href="`/events/${event.id}`"
                                    class="block w-full bg-primary hover:bg-primary text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 text-center"
                                >
                                    {{ $t('Events.view_details') }}
                                </Link>
                            </div>
                        </div>
                    </div>
                </Slide>

                <!-- Add pagination -->
                <template #addons>
                    <Navigation />
                    <Pagination />
                </template>
            </Carousel>
        </div>

        <!-- Empty State -->
        <div v-else class="container mx-auto px-4 text-center py-12">
            <div class="max-w-md mx-auto">
                <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ $t('Events.no_events') }}</h3>
                <p class="text-gray-500 dark:text-gray-400">{{ $t('Events.no_events_check_back') }}</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.event-content{
    @apply flex flex-col flex-1;
}

:deep(.carousel__track){
    padding: 10px 0;
}

.events-carousel {
    @apply relative;
}

/* Custom carousel navigation styles */
:deep(.carousel__prev),
:deep(.carousel__next) {
    @apply bg-white dark:bg-gray-800 text-gray-800 dark:text-white shadow-lg border border-gray-200 dark:border-gray-700;
    @apply hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200;
    @apply w-12 h-12 rounded-full -mx-[20px];
}

:deep(.carousel__prev:hover),
:deep(.carousel__next:hover) {
    @apply shadow-xl;
}

:deep(.carousel__pagination) {
    @apply relative;
}

/* Custom pagination styles */
:deep(.carousel__pagination) {
    @apply mt-6;
}

:deep(.carousel__pagination-button) {
    @apply w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600;
    @apply hover:bg-primary dark:hover:bg-primary transition-colors duration-200;
}

:deep(.carousel__pagination-button--active) {
    @apply bg-primary dark:bg-primary;
}

/* Line clamp utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Card hover effects */
.event-card {
    @apply flex flex-col transform transition-all duration-300;
}

.event-card:hover {
    @apply -translate-y-1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    :deep(.carousel__prev),
    :deep(.carousel__next) {
        @apply w-10 h-10 hidden;
    }
}
</style>
