<template>
    <div class="p-4">
        <!-- Event Date Title -->
        <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            {{ $t('Events.attendance.eventDate') }}
        </h3>

        <!-- Date Information -->
        <div class="flex flex-col gap-2 mb-4">
            <!-- Start Date -->
            <div class="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="bg-primary-default-light dark:bg-primary-dark-light rounded-full p-1 box-content" width="17" height="17" viewBox="0 0 17 17" fill="none">
                    <path d="M11.2471 7.19068C11.506 7.19068 11.7158 6.98081 11.7158 6.72193C11.7158 6.46305 11.506 6.25318 11.2471 6.25318C10.9882 6.25318 10.7783 6.46305 10.7783 6.72193C10.7783 6.98081 10.9882 7.19068 11.2471 7.19068Z" fill="#007172"/>
                    <path d="M12.3252 2.73756H11.7158V2.26881C11.7158 2.00992 11.506 1.80006 11.2471 1.80006C10.9882 1.80006 10.7783 2.00992 10.7783 2.26881V2.73756H8.64551V2.26881C8.64551 2.00992 8.43565 1.80006 8.17676 1.80006C7.91787 1.80006 7.70801 2.00992 7.70801 2.26881V2.73756H5.59863V2.26881C5.59863 2.00992 5.38877 1.80006 5.12988 1.80006C4.87099 1.80006 4.66113 2.00992 4.66113 2.26881V2.73756H4.0752C3.04132 2.73756 2.2002 3.57868 2.2002 4.61256V11.9251C2.2002 12.9589 3.04132 13.8001 4.0752 13.8001H7.66113C7.92002 13.8001 8.12988 13.5902 8.12988 13.3313C8.12988 13.0724 7.92002 12.8626 7.66113 12.8626H4.0752C3.55826 12.8626 3.1377 12.442 3.1377 11.9251V4.61256C3.1377 4.09562 3.55826 3.67506 4.0752 3.67506H4.66113V4.14381C4.66113 4.4027 4.87099 4.61256 5.12988 4.61256C5.38877 4.61256 5.59863 4.4027 5.59863 4.14381V3.67506H7.70801V4.14381C7.70801 4.4027 7.91787 4.61256 8.17676 4.61256C8.43565 4.61256 8.64551 4.4027 8.64551 4.14381V3.67506H10.7783V4.14381C10.7783 4.4027 10.9882 4.61256 11.2471 4.61256C11.506 4.61256 11.7158 4.4027 11.7158 4.14381V3.67506H12.3252C12.8421 3.67506 13.2627 4.09562 13.2627 4.61256V7.28443C13.2627 7.54332 13.4726 7.75318 13.7314 7.75318C13.9903 7.75318 14.2002 7.54332 14.2002 7.28443V4.61256C14.2002 3.57868 13.3591 2.73756 12.3252 2.73756Z" fill="#007172"/>
                    <path d="M11.3643 8.12818C9.80051 8.12818 8.52832 9.40037 8.52832 10.9641C8.52832 12.5279 9.80051 13.8001 11.3643 13.8001C12.928 13.8001 14.2002 12.5279 14.2002 10.9641C14.2002 9.40037 12.928 8.12818 11.3643 8.12818ZM11.3643 12.8626C10.3175 12.8626 9.46582 12.0109 9.46582 10.9641C9.46582 9.91731 10.3175 9.06568 11.3643 9.06568C12.411 9.06568 13.2627 9.91731 13.2627 10.9641C13.2627 12.0109 12.411 12.8626 11.3643 12.8626Z" fill="#007172"/>
                    <path d="M12.0439 10.4954H11.833V10.0032C11.833 9.74429 11.6231 9.53443 11.3643 9.53443C11.1054 9.53443 10.8955 9.74429 10.8955 10.0032V10.9641C10.8955 11.223 11.1054 11.4329 11.3643 11.4329H12.0439C12.3028 11.4329 12.5127 11.223 12.5127 10.9641C12.5127 10.7052 12.3028 10.4954 12.0439 10.4954Z" fill="#007172"/>
                    <path d="M9.20801 7.19068C9.46689 7.19068 9.67676 6.98081 9.67676 6.72193C9.67676 6.46305 9.46689 6.25318 9.20801 6.25318C8.94912 6.25318 8.73926 6.46305 8.73926 6.72193C8.73926 6.98081 8.94912 7.19068 9.20801 7.19068Z" fill="#007172"/>
                    <path d="M7.16895 9.22974C7.42783 9.22974 7.6377 9.01988 7.6377 8.76099C7.6377 8.50211 7.42783 8.29224 7.16895 8.29224C6.91006 8.29224 6.7002 8.50211 6.7002 8.76099C6.7002 9.01988 6.91006 9.22974 7.16895 9.22974Z" fill="#007172"/>
                    <path d="M5.12988 7.19068C5.38877 7.19068 5.59863 6.98081 5.59863 6.72193C5.59863 6.46305 5.38877 6.25318 5.12988 6.25318C4.871 6.25318 4.66113 6.46305 4.66113 6.72193C4.66113 6.98081 4.871 7.19068 5.12988 7.19068Z" fill="#007172"/>
                    <path d="M5.12988 9.22974C5.38877 9.22974 5.59863 9.01988 5.59863 8.76099C5.59863 8.50211 5.38877 8.29224 5.12988 8.29224C4.871 8.29224 4.66113 8.50211 4.66113 8.76099C4.66113 9.01988 4.871 9.22974 5.12988 9.22974Z" fill="#007172"/>
                    <path d="M5.12988 11.2688C5.38877 11.2688 5.59863 11.0589 5.59863 10.8001C5.59863 10.5412 5.38877 10.3313 5.12988 10.3313C4.871 10.3313 4.66113 10.5412 4.66113 10.8001C4.66113 11.0589 4.871 11.2688 5.12988 11.2688Z" fill="#007172"/>
                    <path d="M7.16895 11.2688C7.42783 11.2688 7.6377 11.0589 7.6377 10.8001C7.6377 10.5412 7.42783 10.3313 7.16895 10.3313C6.91006 10.3313 6.7002 10.5412 6.7002 10.8001C6.7002 11.0589 6.91006 11.2688 7.16895 11.2688Z" fill="#007172"/>
                    <path d="M7.16895 7.19068C7.42783 7.19068 7.6377 6.98081 7.6377 6.72193C7.6377 6.46305 7.42783 6.25318 7.16895 6.25318C6.91006 6.25318 6.7002 6.46305 6.7002 6.72193C6.7002 6.98081 6.91006 7.19068 7.16895 7.19068Z" fill="#007172"/>
                </svg>
                <span class="font-semibold">{{ $t('Events.attendance.start_date') }}:</span>
                <span>{{ formatDate(event.start_date) }}</span>
            </div>

            <!-- End Date -->
            <div class="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="bg-primary-default-light dark:bg-primary-dark-light rounded-full p-1 box-content" width="17" height="17" viewBox="0 0 17 17" fill="none">
                    <path d="M11.2471 7.19068C11.506 7.19068 11.7158 6.98081 11.7158 6.72193C11.7158 6.46305 11.506 6.25318 11.2471 6.25318C10.9882 6.25318 10.7783 6.46305 10.7783 6.72193C10.7783 6.98081 10.9882 7.19068 11.2471 7.19068Z" fill="#007172"/>
                    <path d="M12.3252 2.73756H11.7158V2.26881C11.7158 2.00992 11.506 1.80006 11.2471 1.80006C10.9882 1.80006 10.7783 2.00992 10.7783 2.26881V2.73756H8.64551V2.26881C8.64551 2.00992 8.43565 1.80006 8.17676 1.80006C7.91787 1.80006 7.70801 2.00992 7.70801 2.26881V2.73756H5.59863V2.26881C5.59863 2.00992 5.38877 1.80006 5.12988 1.80006C4.87099 1.80006 4.66113 2.00992 4.66113 2.26881V2.73756H4.0752C3.04132 2.73756 2.2002 3.57868 2.2002 4.61256V11.9251C2.2002 12.9589 3.04132 13.8001 4.0752 13.8001H7.66113C7.92002 13.8001 8.12988 13.5902 8.12988 13.3313C8.12988 13.0724 7.92002 12.8626 7.66113 12.8626H4.0752C3.55826 12.8626 3.1377 12.442 3.1377 11.9251V4.61256C3.1377 4.09562 3.55826 3.67506 4.0752 3.67506H4.66113V4.14381C4.66113 4.4027 4.87099 4.61256 5.12988 4.61256C5.38877 4.61256 5.59863 4.4027 5.59863 4.14381V3.67506H7.70801V4.14381C7.70801 4.4027 7.91787 4.61256 8.17676 4.61256C8.43565 4.61256 8.64551 4.4027 8.64551 4.14381V3.67506H10.7783V4.14381C10.7783 4.4027 10.9882 4.61256 11.2471 4.61256C11.506 4.61256 11.7158 4.4027 11.7158 4.14381V3.67506H12.3252C12.8421 3.67506 13.2627 4.09562 13.2627 4.61256V7.28443C13.2627 7.54332 13.4726 7.75318 13.7314 7.75318C13.9903 7.75318 14.2002 7.54332 14.2002 7.28443V4.61256C14.2002 3.57868 13.3591 2.73756 12.3252 2.73756Z" fill="#007172"/>
                    <path d="M11.3643 8.12818C9.80051 8.12818 8.52832 9.40037 8.52832 10.9641C8.52832 12.5279 9.80051 13.8001 11.3643 13.8001C12.928 13.8001 14.2002 12.5279 14.2002 10.9641C14.2002 9.40037 12.928 8.12818 11.3643 8.12818ZM11.3643 12.8626C10.3175 12.8626 9.46582 12.0109 9.46582 10.9641C9.46582 9.91731 10.3175 9.06568 11.3643 9.06568C12.411 9.06568 13.2627 9.91731 13.2627 10.9641C13.2627 12.0109 12.411 12.8626 11.3643 12.8626Z" fill="#007172"/>
                    <path d="M12.0439 10.4954H11.833V10.0032C11.833 9.74429 11.6231 9.53443 11.3643 9.53443C11.1054 9.53443 10.8955 9.74429 10.8955 10.0032V10.9641C10.8955 11.223 11.1054 11.4329 11.3643 11.4329H12.0439C12.3028 11.4329 12.5127 11.223 12.5127 10.9641C12.5127 10.7052 12.3028 10.4954 12.0439 10.4954Z" fill="#007172"/>
                    <path d="M9.20801 7.19068C9.46689 7.19068 9.67676 6.98081 9.67676 6.72193C9.67676 6.46305 9.46689 6.25318 9.20801 6.25318C8.94912 6.25318 8.73926 6.46305 8.73926 6.72193C8.73926 6.98081 8.94912 7.19068 9.20801 7.19068Z" fill="#007172"/>
                    <path d="M7.16895 9.22974C7.42783 9.22974 7.6377 9.01988 7.6377 8.76099C7.6377 8.50211 7.42783 8.29224 7.16895 8.29224C6.91006 8.29224 6.7002 8.50211 6.7002 8.76099C6.7002 9.01988 6.91006 9.22974 7.16895 9.22974Z" fill="#007172"/>
                    <path d="M5.12988 7.19068C5.38877 7.19068 5.59863 6.98081 5.59863 6.72193C5.59863 6.46305 5.38877 6.25318 5.12988 6.25318C4.871 6.25318 4.66113 6.46305 4.66113 6.72193C4.66113 6.98081 4.871 7.19068 5.12988 7.19068Z" fill="#007172"/>
                    <path d="M5.12988 9.22974C5.38877 9.22974 5.59863 9.01988 5.59863 8.76099C5.59863 8.50211 5.38877 8.29224 5.12988 8.29224C4.871 8.29224 4.66113 8.50211 4.66113 8.76099C4.66113 9.01988 4.871 9.22974 5.12988 9.22974Z" fill="#007172"/>
                    <path d="M5.12988 11.2688C5.38877 11.2688 5.59863 11.0589 5.59863 10.8001C5.59863 10.5412 5.38877 10.3313 5.12988 10.3313C4.871 10.3313 4.66113 10.5412 4.66113 10.8001C4.66113 11.0589 4.871 11.2688 5.12988 11.2688Z" fill="#007172"/>
                    <path d="M7.16895 11.2688C7.42783 11.2688 7.6377 11.0589 7.6377 10.8001C7.6377 10.5412 7.42783 10.3313 7.16895 10.3313C6.91006 10.3313 6.7002 10.5412 6.7002 10.8001C6.7002 11.0589 6.91006 11.2688 7.16895 11.2688Z" fill="#007172"/>
                    <path d="M7.16895 7.19068C7.42783 7.19068 7.6377 6.98081 7.6377 6.72193C7.6377 6.46305 7.42783 6.25318 7.16895 6.25318C6.91006 6.25318 6.7002 6.46305 6.7002 6.72193C6.7002 6.98081 6.91006 7.19068 7.16895 7.19068Z" fill="#007172"/>
                </svg>
                <span class="font-semibold">{{ $t('Events.attendance.end_date') }}:</span>
                <span>{{ formatDate(event.end_date) }}</span>
            </div>
        </div>

<hr class="my-[24px]">
        <!-- Event Date Title -->
        <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            {{ $t('Events.attendance.location') }}
        </h3>
        <!-- Location Info -->
        <div class="flex items-center gap-2 mb-4 text-gray-600 dark:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="bg-primary-default-light dark:bg-primary-dark-light rounded-full p-1 box-content" width="17" height="17" viewBox="0 0 17 17" fill="none">
                <path d="M11.7681 3.07314C10.8228 2.12385 9.56355 1.60104 8.22218 1.60104H8.21639C5.51526 1.54575 3.14554 3.92454 3.20115 6.62468C3.27186 8.53131 5.10607 10.9527 6.53444 12.813C6.95392 13.3141 7.56513 13.6001 8.22175 13.6001C8.9014 13.6001 9.53243 13.2938 9.95301 12.7599C11.0126 11.4431 13.3007 8.276 13.2316 6.62464C13.2317 5.2823 12.7119 4.02103 11.7681 3.07314ZM9.21661 12.1798C8.73221 12.8188 7.71324 12.8199 7.22866 12.1809C5.29185 9.71468 4.13854 7.66083 4.13854 6.62466C4.13854 4.37154 5.96786 2.5385 8.21639 2.5385H8.22218C10.4675 2.5385 12.2942 4.37156 12.2942 6.62466C12.2942 7.6721 11.1725 9.69687 9.21661 12.1798ZM8.24273 8.6083C7.14431 8.6083 6.25068 7.71466 6.25068 6.61625C6.3601 3.97352 10.1258 3.9743 10.2348 6.61627C10.2348 7.71469 9.34115 8.6083 8.24273 8.6083ZM8.24273 5.56163C7.66121 5.56163 7.18811 6.03473 7.18811 6.61625C7.24605 8.01535 9.23962 8.01492 9.29734 6.61625C9.29734 6.03473 8.82427 5.56163 8.24273 5.56163Z" fill="#007172"/>
            </svg>
            <span>{{ event.address?.title || $t('Events.attendance.no_location') }}</span>
        </div>
        <!-- Map Container -->
        <div class="w-full h-[300px] rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 relative">
            <!-- Actual map image -->
            <img
                src="/assets/images/map.png"
                alt="Location Map"
                class="w-full h-full object-cover"
            />

            <!-- Map Marker -->
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import type { Event } from '@/types';

defineProps<{
    event: Event;
}>();
// Function to format date
const formatDate = (dateString: string) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
};

const { t } = useI18n();

</script>

<style scoped>
/* Add any additional styling here */
</style>
