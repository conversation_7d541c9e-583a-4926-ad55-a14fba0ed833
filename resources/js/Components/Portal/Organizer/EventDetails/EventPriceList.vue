<template>
    <div>
        <!-- Price Plans Section Title -->
        <div class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
            {{ $t('Events.price_plans.title') }}
        </div>

        <!-- Pricing Cards -->
        <EventPriceTabs v-if="props.tickets.length" :event-data="props.tickets"/>
        <div v-else>
            {{ $t('Events.price_plans.no_tickets_available') }}
        </div>

    </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import {computed, ref, watch} from 'vue';
import EventPriceTabs from '@/Components/EventPriceTabs.vue';



interface Feature {
    text: {
        ar: string;
        en: string;
    };
    available: boolean;
}

interface PricingPlan {
    disscount: number;
    title: {
        ar: string;
        en: string;
    };
    price: {
        ar: string;
        en: string;
    };
    originalPrice: {
        ar: string;
        en: string;
    };
    ticketsCount: number;
    description: {
        ar: string;
        en: string;
    };
    features: Feature[];
}



// Static data with translations
const plans: PricingPlan[] = [
    {
        disscount: 20,
        title: {
            ar: "باقة اساسية",
            en: "Basic Package"
        },
        price: {
            ar: "٨٠ ريال",
            en: "80 SAR"
        },
        originalPrice: {
            ar: "١٠٠ ريال",
            en: "100 SAR"
        },
        ticketsCount: 50,
        description: {
            ar: "هذه الباقة مناسبة للمبتدئين وتتضمن المميزات الأساسية",
            en: "This package is suitable for beginners and includes basic features"
        },
        features: [
            {
                text: {
                    ar: "حضور الفعالية",
                    en: "Event Attendance"
                },
                available: true
            },
            {
                text: {
                    ar: "شهادة حضور",
                    en: "Attendance Certificate"
                },
                available: true
            },
            {
                text: {
                    ar: "محتوى تدريبي",
                    en: "Training Content"
                },
                available: true
            },
            {
                text: {
                    ar: "استشارة خاصة",
                    en: "Private Consultation"
                },
                available: false
            },
            {
                text: {
                    ar: "عضوية VIP",
                    en: "VIP Membership"
                },
                available: false
            }
        ]
    },
    {
        disscount: 20,
        title: {
            ar: "باقة اساسية",
            en: "Basic Package"
        },
        price: {
            ar: "٨٠ ريال",
            en: "80 SAR"
        },
        originalPrice: {
            ar: "١٠٠ ريال",
            en: "100 SAR"
        },
        ticketsCount: 50,
        description: {
            ar: "هذه الباقة مناسبة للمبتدئين وتتضمن المميزات الأساسية",
            en: "This package is suitable for beginners and includes basic features"
        },
        features: [
            {
                text: {
                    ar: "حضور الفعالية",
                    en: "Event Attendance"
                },
                available: true
            },
            {
                text: {
                    ar: "شهادة حضور",
                    en: "Attendance Certificate"
                },
                available: true
            },
            {
                text: {
                    ar: "محتوى تدريبي",
                    en: "Training Content"
                },
                available: true
            },
            {
                text: {
                    ar: "استشارة خاصة",
                    en: "Private Consultation"
                },
                available: false
            },
            {
                text: {
                    ar: "عضوية VIP",
                    en: "VIP Membership"
                },
                available: false
            }
        ]
    },
    {
        disscount: 20,
        title: {
            ar: "باقة اساسية",
            en: "Basic Package"
        },
        price: {
            ar: "٨٠ ريال",
            en: "80 SAR"
        },
        originalPrice: {
            ar: "١٠٠ ريال",
            en: "100 SAR"
        },
        ticketsCount: 50,
        description: {
            ar: "هذه الباقة مناسبة للمبتدئين وتتضمن المميزات الأساسية",
            en: "This package is suitable for beginners and includes basic features"
        },
        features: [
            {
                text: {
                    ar: "حضور الفعالية",
                    en: "Event Attendance"
                },
                available: true
            },
            {
                text: {
                    ar: "شهادة حضور",
                    en: "Attendance Certificate"
                },
                available: true
            },
            {
                text: {
                    ar: "محتوى تدريبي",
                    en: "Training Content"
                },
                available: true
            },
            {
                text: {
                    ar: "استشارة خاصة",
                    en: "Private Consultation"
                },
                available: false
            },
            {
                text: {
                    ar: "عضوية VIP",
                    en: "VIP Membership"
                },
                available: false
            }
        ]
    },
    // Add similar structure for other plans...
];

// Compute localized plans based on current locale
const localizedPlans = computed(() =>
    plans.map(plan => ({
        ...plan,
        title: plan.title[locale.value],
        price: plan.price[locale.value],
        originalPrice: plan.originalPrice[locale.value],
        description: plan.description[locale.value],
        features: plan.features.map(feature => ({
            ...feature,
            text: feature.text[locale.value]
        }))
    }))
);

const props = defineProps({
    event: Object,
    tickets: Object,
});

console.log('sssssssssssssssssss',props.tickets);

const { locale:localeLang } = useI18n();

let lang;
if(localeLang.value=='ae'){
    lang='ar'
}else {
    lang='en'
}

const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;

});
</script>

<style scoped>
.plan-card {
    box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.05);
}
</style>
