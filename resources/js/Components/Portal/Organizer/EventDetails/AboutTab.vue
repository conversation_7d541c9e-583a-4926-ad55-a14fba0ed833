<template>
    <div class="my-[24px]">
        <div class="mb-6">
            <h3 class="text-2xl font-semibold mb-2 text-gray-900 dark:text-gray-100">
                {{ event?.name[locale] }}
            </h3>
            <div
                v-if="event?.description"
                class="text-base text-gray-600 dark:text-gray-300 leading-relaxed"
                v-html="event.description[locale]"
            >
            </div>
        </div>
        <div class="mb-6">
            <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100 border-b-2 border-primary pe-6 leading-10 mb-4 inline-flex">
                {{$t('Events.tags')}}
            </h3>
            <div class="tags">
                <div class="flex flex-wrap gap-2">
                  <span
                      v-for="(tag, index) in event?.tags"
                      :key="index"
                      class="flex items-center bg-gray-background text-primary px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {{ tag.name[locale] }}
                  </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import type { Event } from '@/types/event';
import {ref, watch} from "vue";

const props = defineProps<{
    event: Event;
}>();

const { locale:localeLang } = useI18n();

let lang;
if(localeLang.value=='ae'){
    lang='ar'
}else {
    lang='en'
}

const locale = ref(lang);
watch(localeLang, (newLocale) => {
    locale.value = newLocale === 'ae' ? 'ar' : newLocale;

});
</script>

