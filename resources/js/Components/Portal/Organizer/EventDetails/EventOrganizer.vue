<template>
    <div class="p-4">
        <!-- Organizer Header Section -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-4">
                <!-- Organizer Image -->
                <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                    <img
                        :src="organizerImage"
                        :alt="organizerFullName"
                        class="w-full h-full object-cover"
                        @error="handleImageError"
                    />
                </div>

                <!-- Organizer Name and Rating -->
                <div class="flex flex-col gap-1">
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
                        {{ organizerFullName }}
                    </h3>
                    <div class="flex items-center gap-1">
                        <div class="flex items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400 ml-1">({{ rating }})</span>
                            <!-- Star Rating -->
                            <div class="flex items-center ml-2">
                                <svg v-for="index in 5"
                                     :key="index"
                                     :class="[
                                         'w-4 h-4',
                                         index <= Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
                                     ]"
                                     xmlns="http://www.w3.org/2000/svg"
                                     viewBox="0 0 20 20"
                                     fill="currentColor">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import type { Event } from '@/types';

const props = defineProps<{
    event: Event;
}>();

const { t } = useI18n();


// Mock rating - replace with actual rating from your data
const rating = ref(3.5);

const organizerFullName = computed(() => {
    const { first_name, last_name } = props.event.organizer || {};
    return `${first_name || ''} ${last_name || ''}`.trim() || t('Events.organizer.unknown_name');
});

const organizerImage = computed(() => {
    return `/assets/images/default-avatar.png`;
});

const handleImageError = (e: Event) => {
    const target = e.target as HTMLImageElement;
    target.src = '/assets/images/default-avatar.png';
};
</script>

<style scoped>
.star-rating {
    display: inline-flex;
    align-items: center;
}
</style>
