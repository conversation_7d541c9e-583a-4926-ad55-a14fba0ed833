<template>
  <div class="flex justify-between items-center gap-2 flex-wrap">
    <div class="flex flex-col gap-1">
      <p class="text-base font-semibold text-gray-text dark:text-gray-400">
        {{ $t('Events.Ticketing.Create.Steps.Two.label') }}
      </p>
      <span class="text-sm text-gray-secondary dark:text-gray-600">
        {{ $t('Events.Ticketing.Create.Steps.Two.description') }}
      </span>
    </div>
    <div class="flex flex-col items-center gap-2 py-2 px-4 rounded-lg bg-orange-orange-light">
      <p class="flex gap-2 items-center">
        <span class="text-orange text-lg font-bold">{{ remainingTickets }}</span>
        <span class="text-black-dark">{{ $t('Events.Ticketing.remaining_tickets') }}</span>
      </p>
      <span class="text-gray-secondary">{{ $t('Events.Ticketing.remaining_tickets_placeholder') }}</span>
    </div>
  </div>
  <InputError :message="errors['tickets']"/>
  <RepeaterInputElement
    v-model="tickets"
    :add-button-text="$t('Events.Ticketing.Create.Steps.Two.add_new_package')"
    :templateItem="templateItem"
  >
    <template #default="{ item, index }">
      <div class="flex flex-col gap-4">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="w-full">
            <InputElement
              class="w-full"
              :label="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_name_ar')"
              type="text"
              :placeholder="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_name_placeholder')"
              :required="true"
              :name="`ticket[${index}][name][ar]`"
              v-model="item.name.ar"
            />
            <InputError :message="errors[`tickets.${index}.name.ar`]"/>
          </div>
          <div class="w-full">
            <InputElement
              class="w-full"
              :label="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_name_en')"
              type="text"
              :placeholder="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_name_placeholder')"
              :required="true"
              :name="`ticket[${index}][name][en]`"
              v-model="item.name.en"
            />
            <InputError :message="errors[`tickets.${index}.name.en`]"/>
          </div>
        </div>
        <div class="flex flex-col md:flex-row gap-4">
          <div class="w-full">
            <InputElement
              class="w-full"
              :label="$t('Events.Ticketing.Create.Steps.Two.Form.number_of_tickets')"
              type="number"
              :placeholder="$t('Events.Ticketing.Create.Steps.Two.Form.number_of_tickets_placeholder')"
              :required="true"
              :name="`ticket[${index}][seats_count]`"
              v-model="item.seats_count"
              :max="max_attendees - totalTickets + (parseInt(item.seats_count) || 0)"
              @input="(event) => {
                event.target.value = Math.abs(event.target.value)
                if (!validateTicketCount(event, index)) {
                  item.seats_count = '';
                }
              }"
            />
            <InputError :message="errors[`tickets.${index}.seats_count`]"/>
          </div>
          <div class="w-full">
            <InputElement
              class="w-full"
              :label="$t('Events.Ticketing.Create.Steps.Two.Form.basic_ticket_price')"
              type="number"
              :placeholder="$t('Events.Ticketing.Create.Steps.Two.Form.basic_ticket_price_placeholder')"
              :required="false"
              textIcon="true"
              :icongroup="$t('riyal')"
              :name="`ticket[${index}][main_price]`"
              v-model="item.main_price"
            />
            <InputError :message="errors[`tickets.${index}.main_price`]"/>
          </div>
        </div>
        <div class="flex flex-col md:flex-row gap-4">
          <div class="w-full">
            <InputElement
              class="w-full"
              :label="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_description_ar')"
              type="text"
              :placeholder="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_description_placeholder')"
              :required="false"
              :name="`ticket[${index}][description]['ar']`"
              v-model="item.description.ar"
            />
            <InputError :message="errors[`tickets.${index}.description.ar`]"/>
          </div>
          <div class="w-full">
            <InputElement
              class="w-full"
              :label="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_description_en')"
              type="text"
              :placeholder="$t('Events.Ticketing.Create.Steps.Two.Form.ticket_description_placeholder')"
              :required="false"
              :name="`ticket[${index}][description]['en']`"
              v-model="item.description.en"
            />
            <InputError :message="errors[`tickets.${index}.description.en`]"/>
          </div>
        </div>
        <div class="border-b-thinner border-gray-outline dark:border-gray-800"></div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          <label v-for="session in page.props.eventSessions.data" :key="session.id"
               class="flex items-center gap-2 p-3 rounded-lg border border-gray-200 dark:border-gray-700 cursor-pointer m-0">

                <input
                type="checkbox"
                class="form-checkbox"
                :value="session.id"
                :id="`session-${session.id}-ticket-${index}`"
                :checked="item.sessions?.includes(session.id)"
                @change="updateSessions($event, item, session.id)"
                />
                <span class="font-medium text-gray-secondary dark:text-gray-500">{{ session.name[currentLang] }}</span>

          </label>
        </div>
      </div>
    </template>
  </RepeaterInputElement>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import InputElement from '@/Components/InputElement.vue';
import RepeaterInputElement from '@/Components/RepeaterInputElement.vue';
import InputError from '@/Components/InputError.vue';
import { usePage } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';

const page = usePage();
const errors = computed(() => page.props.errors);

const { locale } = useI18n();

let lang;
if(locale.value=='ae'){
  lang='ar'
}else if (locale.value) {
  lang = locale.value;
}

const currentLang = ref(lang);

const max_attendees = page.props.max_attendees

const totalTickets = computed(() => {
  return tickets.value.reduce((sum, ticket) => sum + (parseInt(ticket.seats_count) || 0), 0);
});

const validateTicketCount = (event: Event, currentIndex: number) => {
  const newValue = (event.target as HTMLInputElement).value;

  const newTotal = tickets.value.reduce((sum, ticket, index) => {
    return sum + (index === currentIndex ? parseInt(newValue) || 0 : (parseInt(ticket.seats_count) || 0));
  }, 0);

  if (newTotal > max_attendees) {
    return false;
  }
  return true;
};

const remainingTickets = computed(() => {
  return max_attendees - totalTickets.value;
});

const props = defineProps({
  modelValue: {
    type: Array,
    required: true
  },
  templateItem: {
    type: Object,
    required: true
  }
});

const updateSessions = (event: Event, item: any, sessionId: number) => {
  if (!item.sessions) {
    item.sessions = [];
  }

  if ((event.target as HTMLInputElement).checked) {
    item.sessions.push(sessionId);
  } else {
    const index = item.sessions.indexOf(sessionId);
    if (index > -1) {
      item.sessions.splice(index, 1);
    }
  }
};

const emit = defineEmits(['update:modelValue']);

const tickets = ref(props.modelValue);

watch(locale, (newLocale) => {
  currentLang.value = newLocale === 'ae' ? 'ar' : newLocale;
});

watch(tickets, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });
</script>
