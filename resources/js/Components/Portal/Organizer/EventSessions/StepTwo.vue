<template>
  <div class="flex flex-col gap-6">
    <div class="rounded-2xl shadow-soft dark:border dark:border-blue-darkness p-4">
      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-1">
          <div class="flex justify-between items-center gap-2 flex-wrap-reverse">
            <p class="font-semibold flex flex-col">
              <span>
                {{ formData.name['ar'] }}
              </span>
              <span>
                {{ formData.name['en'] }}
              </span>
            </p>
            <p class="px-4 py-1 bg-orange text-white font-semibold text-xs rounded-lg">
              {{ sessionType?.name[currentLang] }}
            </p>
          </div>
          <div v-if="formData.hall_name" class="flex gap-1 items-center">
            <div class="bg-primary-default-light dark:bg-primary-dark-light rounded-full p-1 h-8 w-8 flex justify-center items-center">
              <i class="ri-map-pin-line text-primary text-lg"></i>
            </div>
            <span class="text-gray-secondary text-xs">
              {{ formData.hall_name }}
            </span>
          </div>
          <div v-if="formData.link" class="flex gap-1 items-center">
            <div class="bg-primary-default-light dark:bg-primary-dark-light rounded-full p-1 h-8 w-8 flex justify-center items-center">
              <img alt="" src="/assets/images/LinkIcon.svg"/>
            </div>
            <span class="text-gray-secondary text-xs">
              <a :href=formData.link target="_blank">
                {{formData.link}}
              </a>
            </span>
          </div>
          <div class="flex gap-1 items-center">
            <div class="bg-primary-default-light dark:bg-primary-dark-light rounded-full p-1 h-8 w-8 flex justify-center items-center">
              <img alt="" src="/assets/images/GreenCalendar.svg"/>
            </div>
            <div class="flex gap-4 items-center">
              <span class="text-gray-secondary text-xs">
                {{ formattedDate }}
              </span>
              <div class=" border-r-thinner border-gray-800 h-3"></div>
              <span class="text-gray-secondary text-xs">
                {{ formattedTime }}
              </span>
            </div>
          </div>
        </div>
        <div class="border-b dark:border-gray-800"></div>
        <div class="flex flex-col gap-4">
          <p class="font-semibold">
            {{$t('Events.Sessions.Create.Steps.One.PanelLabel.session_details')}}
          </p>
          <p class="text-gray-secondary text-sm" v-html="formData.description[currentLang]">
          </p>
        </div>
      </div>
    </div>
    <PanelElement :title="$t('Events.Sessions.Create.Steps.One.PanelLabel.session_speakers')">
      <div class="grid grid-cols-1 sm:grid-cols-3 xl:grid-cols-4 gap-4">
        <div v-for="speaker in formData.speakers" :key="speaker.id" class="p-4 rounded-2xl shadow-soft dark:border dark:border-blue-darkness flex flex-col gap-4 relative">
          <div class="flex items-center gap-1">
            <img :alt="speaker.name" :src="speaker.avatar || '/assets/images/user-profile.jpeg'" class="rounded-full h-10 w-10"/>
            <span class="text-base font-semibold">{{ speaker.first_name }} {{ speaker.last_name }}</span>
          </div>
        </div>
      </div>
    </PanelElement>
  </div>
</template>

<script setup lang="ts">
import { ref,watch,computed } from 'vue';
import PanelElement from '@/Components/PanelElement.vue';
import { useI18n } from 'vue-i18n';
import { usePage } from '@inertiajs/vue3';
import dayjs from 'dayjs';
import 'dayjs/locale/ar';

const page = usePage();


const { locale } = useI18n();

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
});


const formattedDate = computed(() => {
  if (!props.formData.session_date) return '';
  dayjs.locale(currentLang.value === 'ar' ? 'ar' : 'en');
  return dayjs(props.formData.session_date).format('DD MMMM, YYYY');
});

const formattedTime = computed(() => {
  if (!props.formData.start_time || !props.formData.end_time) return '';

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const period = Number(hours) >= 12 ? (currentLang.value === 'ar' ? 'م' : 'PM') : (currentLang.value === 'ar' ? 'ص' : 'AM');
    const hour12 = Number(hours) % 12 || 12;
    return `${hour12}:${minutes} ${period}`;
  };

  const startTime = formatTime(props.formData.start_time);
  const endTime = formatTime(props.formData.end_time);

  return `${t('Events.Sessions.Create.Steps.Two.from_hour')} ${startTime} - ${t('Events.Sessions.Create.Steps.Two.to_hour')} ${endTime}`;
});


const sessionType = computed(() => {
  return page.props.sessionType.find(type => type.id === props.formData.type_id.id);
});

const currentLang = ref(locale.value === 'ae' ? 'ar' : locale.value);

watch(locale, (newLocale) => {
  currentLang.value = newLocale === 'ae' ? 'ar' : newLocale;
});


</script>
