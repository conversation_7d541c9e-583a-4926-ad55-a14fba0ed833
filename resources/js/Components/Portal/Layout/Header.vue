<template>
    <header class="z-40" :class="{ dark: store.semidark && store.menu === 'horizontal' }">
        <div class="shadow-sm">
            <div class="container">
                <div class="relative bg-white flex w-full items-center px-4 lg:px-4 py-2.5 dark:bg-black gap-2">
                    <div class="horizontal-logo flex justify-between items-center gap-6">
                        <div class="flex items-center">
                            <a
                                href="javascript:void(0);"
                                class="collapse-icon flex-none dark:text-black-light hover:text-primary dark:hover:text-primary flex lg:hidden ltr:mr-2 rtl:ml-2 p-2 rounded-full bg-white-light/40 dark:bg-dark/40 hover:bg-white-light/90 dark:hover:bg-dark/60 w-9 h-9"
                                @click="store.toggleNav()"
                            >
                                <i class="ri-menu-line text-lg leading-[1.25rem]"></i>
                            </a>
                            <Link href="/" class="main-logo flex items-center shrink-0 p-0">
                                <img class="w-16 inline" src="/assets/images/logo.svg" alt="" />
                            </Link>
                        </div>
                    </div>

                    <div class="sm:flex-1 flex justify-center header-navbar-menu">
                        <div class="fixed inset-0 bg-[black]/60 z-50 lg:hidden" :class="{ hidden: !store.nav }" @click="store.toggleNav()"></div>

                        <nav class="navbar bg-white lg:bg-transparent dark:bg-black lg:dark:bg-transparent lg:static fixed min-h-screen h-full lg:h-auto lg:min-h-fit top-0 bottom-0 w-[260px] lg:w-auto z-50 transition-all duration-300">
                            <div class="flex justify-between items-center px-4 py-3 lg:hidden">
                                <Link href="/" class="main-logo flex items-center shrink-0">
                                    <img class="w-14 ml-[5px] flex-none" src="/assets/images/logo.svg" alt="" />
                                </Link>
                                <a
                                    href="javascript:void(0);"
                                    class="justify-center collapse-icon w-8 h-8 rounded-full flex lg:hidden items-center hover:bg-gray-500/10 dark:hover:bg-dark-light/10 dark:text-white-light transition duration-300 rtl:rotate-180 hover:text-primary"
                                    @click="store.toggleNav()"
                                >
                                    <i class="ri-arrow-left-double-line text-lg"></i>
                                </a>
                            </div>

                            <ul class="flex lg:items-center lg:gap-4 gap-1 text-base cursor-pointer flex-col lg:flex-row p-4 lg:p-0">
                                <li class="nav-item">
                                    <Link href="/" class="group " @click="toggleMobileNav">
                                        <div class="flex items-center">
                                            <span class="text-black dark:text-blue-muted dark:group-hover:text-primary text-base">
                                                {{ $t('navigation.home') }}
                                            </span>
                                        </div>
                                    </Link>
                                </li>
                                <li class="nav-item">
                                    <Link href="/events" class="group" @click="toggleMobileNav">
                                        <div class="flex items-center">
                                            <span class="text-black dark:text-blue-muted dark:group-hover:text-primary text-base">
                                                {{ $t('navigation.events') }}
                                            </span>
                                        </div>
                                    </Link>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="group" @click="toggleMobileNav">
                                        <div class="flex items-center">
                                            <span class="text-black dark:text-blue-muted dark:group-hover:text-primary text-base">
                                                {{ $t('navigation.event_organizers') }}
                                            </span>
                                        </div>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="group" @click="toggleMobileNav">
                                        <div class="flex items-center">
                                            <span class="text-black dark:text-blue-muted dark:group-hover:text-primary text-base">
                                                {{ $t('navigation.contact_us') }}
                                            </span>
                                        </div>
                                    </a>
                                </li>

                            </ul>

                        </nav>
                    </div>

                    <div class="ltr:sm:ml-0 ltr:ml-auto sm:rtl:mr-0 rtl:mr-auto flex items-center dark:text-black-light gap-2 sm:gap-4">
                        <div class="sm:ltr:mr-auto sm:rtl:ml-auto">

                        </div>

                        <div v-if="page.props.auth.user" class="dropdown shrink-0">
                            <Popper :placement="store.rtlClass === 'rtl' ? 'bottom-end' : 'bottom-start'" offsetDistance="0" class="!block">
                                <button type="button" class="relative group block">
                                    <div class="flex items-center gap-2">
                                        <img
                                            class="w-9 h-9 rounded-full object-cover saturate-50 group-hover:saturate-100"
                                            src="/assets/images/user-profile.jpeg"
                                            alt=""
                                        />
                                        <div class="lg:flex gap-2 hidden">
                                            <p class="flex flex-col text-start">
                                            <span class="text-sm">
                                                {{page.props.auth.user.name}}
                                            </span>
                                                <span class="text-white-dark dark:text-gray-500 text-xs">
                                                {{page.props.auth.user.role}}
                                            </span>
                                            </p>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="fill-white-dark dark:fill-gray-500">
                                                <path d="M22.2775 10.6864H14.6717H7.82286C6.65086 10.6864 6.06486 12.1026 6.89503 12.9327L13.2189 19.2567C14.2322 20.2699 15.8804 20.2699 16.8937 19.2567L19.2987 16.8516L23.2176 12.9327C24.0355 12.1026 23.4495 10.6864 22.2775 10.6864Z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </button>
                                <template #content="{ close }">
                                    <ul class="text-dark dark:text-white-dark !py-0 w-[230px] font-semibold dark:text-white-light/90">
                                        <li>
                                            <div class="flex items-center px-4 py-4">
                                                <div class="flex-none">
                                                    <img class="rounded-md w-10 h-10 object-cover" src="/assets/images/user-profile.jpeg" alt="" />
                                                </div>
                                                <div class="ltr:pl-4 rtl:pr-4 truncate">
                                                    <h4 class="text-base">
                                                        {{page.props.auth.user.name}}
                                                    </h4>
                                                    <a class="text-black/60 hover:text-primary dark:text-dark-light/60 dark:hover:text-white" href="javascript:void(0);">
                                                        {{ page.props.auth.user.email }}
                                                    </a>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <Link href="/profile" class="dark:hover:text-white" @click="close()">
                                                <i class="ri-user-line ltr:mr-2 rtl:ml-2 text-base"></i>
                                                {{ $t('profile') }}
                                            </Link>
                                        </li>

                                        <li class="border-t border-white-light dark:border-white-light/10">
                                            <Link :href="route('logout')" method="post" as="button" class="text-danger !py-3" @click="close()">
                                                <i class="ri-logout-box-r-line ltr:mr-2 rtl:ml-2"></i> {{ $t('auth.sign_out') }}
                                            </Link>
                                        </li>
                                    </ul>
                                </template>
                            </Popper>
                        </div>
                        <div v-else>
                            <GeneralBtnElement as="link" href="/login" class="text-xs btn btn-primary border-primary max-sm:p-2">
                                {{ $t('auth.login') }}
                            </GeneralBtnElement>
                        </div>

                        <div class="w-[0.4px] bg-gray-medium shrink-0 h-9" v-if="page.props.auth.user"></div>

                        <div>
                            <a
                                href="javascript:void(0);"
                                v-show="store.theme === 'light'"
                                class="flex items-center rounded-full hover:text-primary"
                                @click="store.toggleTheme('dark')"
                            >
                                <i class="ri-sun-line text-xl"></i>
                            </a>
                            <a
                                href="javascript:void(0);"
                                v-show="store.theme === 'dark'"
                                class="flex items-center rounded-full hover:text-primary"
                                @click="store.toggleTheme('system')"
                            >
                                <i class="ri-moon-line text-xl"></i>
                            </a>
                            <a
                                href="javascript:void(0);"
                                v-show="store.theme === 'system'"
                                class="flex items-center rounded-full hover:text-primary"
                                @click="store.toggleTheme('light')"
                            >
                                <i class="ri-macbook-line text-xl"></i>
                            </a>
                        </div>

                        <div class="dropdown shrink-0">
                            <Popper :placement="store.rtlClass === 'rtl' ? 'bottom-end' : 'bottom-start'" offsetDistance="0" class="!block">
                                <button
                                    type="button"
                                    class="block rounded-full hover:text-primary"
                                >
                                    <img :src="currentFlag" alt="flag" class="w-5 h-5 object-cover rounded-full" />
                                </button>
                                <template #content="{ close }">
                                    <ul class="!px-2 text-dark dark:text-white-dark grid grid-cols-1 gap-2 font-semibold dark:text-white-light/90">
                                        <template v-for="item in store.languageList" :key="item.code">
                                            <li>
                                                <button
                                                    type="button"
                                                    class="w-full hover:text-primary"
                                                    :class="{ 'bg-primary/10 text-primary': i18n.locale === item.code }"
                                                    @click="changeLanguage(item), close()"
                                                >
                                                    <img
                                                        class="w-5 h-5 object-cover rounded-full"
                                                        :src="`/assets/images/flags/${item.code.toUpperCase()}.svg`"
                                                        alt=""
                                                    />
                                                    <span class="ltr:ml-3 rtl:mr-3">{{ item.name }}</span>
                                                </button>
                                            </li>
                                        </template>
                                    </ul>
                                </template>
                            </Popper>
                        </div>

                        <div v-if="page.props.auth.user" class="dropdown shrink-0">
                            <Popper :placement="store.rtlClass === 'rtl' ? 'bottom-end' : 'bottom-start'" offsetDistance="0" class="!block">
                                <button
                                    type="button"
                                    class="relative block rounded-full hover:text-primary"
                                >
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M19.0001 9.7041V9C19.0001 5.13401 15.8661 2 12.0001 2C8.13407 2 5.00006 5.13401 5.00006 9V9.7041C5.00006 10.5491 4.74995 11.3752 4.28123 12.0783L3.13263 13.8012C2.08349 15.3749 2.88442 17.5139 4.70913 18.0116C9.48258 19.3134 14.5175 19.3134 19.291 18.0116C21.1157 17.5139 21.9166 15.3749 20.8675 13.8012L19.7189 12.0783C19.2502 11.3752 19.0001 10.5491 19.0001 9.7041Z"
                                            stroke="currentColor"
                                            stroke-width="1.5"
                                        />
                                        <path
                                            d="M7.5 19C8.15503 20.7478 9.92246 22 12 22C14.0775 22 15.845 20.7478 16.5 19"
                                            stroke="currentColor"
                                            stroke-width="1.5"
                                            stroke-linecap="round"
                                        />
                                        <path d="M12 6V10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                                    </svg>

                                    <span class="flex absolute w-3 h-3 ltr:right-[5px] rtl:left-[5px] top-[-1px]">
                                    <span
                                        class="animate-ping absolute ltr:-left-[3px] rtl:-right-[3px] -top-[3px] inline-flex h-full w-full rounded-full bg-red-400 opacity-75"
                                    ></span>
                                    <span class="relative inline-flex rounded-full w-[6px] h-[6px] bg-red-500"></span>
                                </span>
                                </button>
                                <template #content="{ close }">
                                    <ul class="!py-0 text-dark dark:text-white-dark w-[300px] sm:w-[350px] divide-y dark:divide-white/10">
                                        <li>
                                            <div class="flex items-center px-4 py-2 justify-between font-semibold">
                                                <h4 class="text-lg">Notification</h4>
                                                <template v-if="notifications.length">
                                                    <span class="badge bg-primary/80" v-text="notifications.length + 'New'"></span>
                                                </template>
                                            </div>
                                        </li>
                                        <template v-for="notification in notifications" :key="notification.id">
                                            <li class="dark:text-white-light/90">
                                                <div class="group flex items-center px-4 py-2">
                                                    <div class="grid place-content-center rounded">
                                                        <div class="w-12 h-12 relative">
                                                            <img
                                                                class="w-12 h-12 rounded-full object-cover"
                                                                :src="`/assets/images/${notification.profile}`"
                                                                alt=""
                                                            />
                                                            <span class="bg-success w-2 h-2 rounded-full block absolute right-[6px] bottom-0"></span>
                                                        </div>
                                                    </div>
                                                    <div class="ltr:pl-3 rtl:pr-3 flex flex-auto">
                                                        <div class="ltr:pr-3 rtl:pl-3">
                                                            <h6 v-html="notification.message"></h6>
                                                            <span class="text-xs block font-normal dark:text-gray-500" v-text="notification.time"></span>
                                                        </div>
                                                        <button
                                                            type="button"
                                                            class="ltr:ml-auto rtl:mr-auto text-neutral-300 hover:text-danger opacity-0 group-hover:opacity-100"
                                                            @click="removeNotification(notification.id)"
                                                        >
                                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <circle opacity="0.5" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" />
                                                                <path
                                                                    d="M14.5 9.50002L9.5 14.5M9.49998 9.5L14.5 14.5"
                                                                    stroke="currentColor"
                                                                    stroke-width="1.5"
                                                                    stroke-linecap="round"
                                                                />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        </template>
                                        <template v-if="notifications.length">
                                            <li>
                                                <div class="p-4">
                                                    <button class="btn btn-primary block w-full btn-small" @click="close()">Read All Notifications</button>
                                                </div>
                                            </li>
                                        </template>
                                        <template v-if="!notifications.length">
                                            <li>
                                                <div class="!grid place-content-center hover:!bg-transparent text-lg min-h-[200px]">
                                                    <div class="mx-auto ring-4 ring-primary/30 rounded-full mb-4 text-primary">
                                                        <svg width="40" height="40" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                opacity="0.5"
                                                                d="M20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10Z"
                                                                fill="currentColor"
                                                            />
                                                            <path
                                                                d="M10 4.25C10.4142 4.25 10.75 4.58579 10.75 5V11C10.75 11.4142 10.4142 11.75 10 11.75C9.58579 11.75 9.25 11.4142 9.25 11V5C9.25 4.58579 9.58579 4.25 10 4.25Z"
                                                                fill="currentColor"
                                                            />
                                                            <path
                                                                d="M10 15C10.5523 15 11 14.5523 11 14C11 13.4477 10.5523 13 10 13C9.44772 13 9 13.4477 9 14C9 14.5523 9.44772 15 10 15Z"
                                                                fill="currentColor"
                                                            />
                                                        </svg>
                                                    </div>
                                                    No data available.
                                                </div>
                                            </li>
                                        </template>
                                    </ul>
                                </template>
                            </Popper>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
</template>

<script lang="ts" setup>
    import { ref, onMounted, computed, reactive, watch } from 'vue';
    import { useI18n } from 'vue-i18n';
    import { usePage } from '@inertiajs/vue3';
    const page = usePage();

    import appSetting from '@/app-setting';

    import Link from '@/Components/NavLink.vue';
    import { useAppStore } from '@/Stores/index';
    import GeneralBtnElement from "../../GeneralBtnElement.vue";
    const store = useAppStore();

    const search = ref(false);


    const i18n = reactive(useI18n());
    const changeLanguage = (item: any) => {
        i18n.locale = item.code;
        appSetting.toggleLanguage(item);
    };
    const currentFlag = computed(() => {
        return `/assets/images/flags/${i18n.locale.toUpperCase()}.svg`;
    });

    const notifications = ref([
        {
            id: 1,
            profile: 'user-profile.jpeg',
            message: '<strong class="text-sm mr-1">John Doe</strong>invite you to <strong>Prototyping</strong>',
            time: '45 min ago',
        },
        {
            id: 2,
            profile: 'profile-34.jpeg',
            message: '<strong class="text-sm mr-1">Adam Nolan</strong>mentioned you to <strong>UX Basics</strong>',
            time: '9h Ago',
        },
        {
            id: 3,
            profile: 'profile-16.jpeg',
            message: '<strong class="text-sm mr-1">Anna Morgan</strong>Upload a file',
            time: '9h Ago',
        },
    ]);

    const messages = ref([
        {
            id: 1,
            image: '<span class="grid place-content-center w-9 h-9 rounded-full bg-success-light dark:bg-success text-success dark:text-success-light"><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg></span>',
            title: 'Congratulations!',
            message: 'Your OS has been updated.',
            time: '1hr',
        },
        {
            id: 2,
            image: '<span class="grid place-content-center w-9 h-9 rounded-full bg-info-light dark:bg-info text-info dark:text-info-light"><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg></span>',
            title: 'Did you know?',
            message: 'You can switch between artboards.',
            time: '2hr',
        },
        {
            id: 3,
            image: '<span class="grid place-content-center w-9 h-9 rounded-full bg-danger-light dark:bg-danger text-danger dark:text-danger-light"> <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></span>',
            title: 'Something went wrong!',
            message: 'Send Reposrt',
            time: '2days',
        },
        {
            id: 4,
            image: '<span class="grid place-content-center w-9 h-9 rounded-full bg-warning-light dark:bg-warning text-warning dark:text-warning-light"><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">    <circle cx="12" cy="12" r="10"></circle>    <line x1="12" y1="8" x2="12" y2="12"></line>    <line x1="12" y1="16" x2="12.01" y2="16"></line></svg></span>',
            title: 'Warning',
            message: 'Your password strength is low.',
            time: '5days',
        },
    ]);

    onMounted(() => {
        setActiveDropdown();
    });

    watch(route, (to, from) => {
        setActiveDropdown();
    });

    const setActiveDropdown = () => {
        const selector = document.querySelector('ul.horizontal-menu a[href="' + window.location.pathname + '"]');
        if (selector) {
            selector.classList.add('active');
            const all: any = document.querySelectorAll('ul.horizontal-menu .nav-link.active');
            for (let i = 0; i < all.length; i++) {
                all[0]?.classList.remove('active');
            }
            const ul: any = selector.closest('ul.sub-menu');
            if (ul) {
                let ele: any = ul.closest('li.menu').querySelectorAll('.nav-link');
                if (ele) {
                    ele = ele[0];
                    setTimeout(() => {
                        ele?.classList.add('active');
                    });
                }
            }
        }
    };

    const removeNotification = (value: number) => {
        notifications.value = notifications.value.filter((d) => d.id !== value);
    };

    const removeMessage = (value: number) => {
        messages.value = messages.value.filter((d) => d.id !== value);
    };

    const toggleMobileNav = () => {
        if (window.innerWidth < 1024) {
            store.toggleNav();
        }
    };

    const wheelPropagationValue = computed(() => window.innerWidth >= 1024);
</script>
