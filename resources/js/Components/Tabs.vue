<template>
    <div class="min-h-8 w-full flex flex-wrap justify-between items-start gap-2">
        <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="handleTabClick(tab.id)"
            class="relative px-4 py-2 text-sm font-semibold leading-[21px] font-cairo whitespace-nowrap"
            :class="[
                activeTab === tab.id
                    ? 'text-primary border-b-2 border-primary'
                    : 'text-gray-secondary dark:text-gray-600'
            ]"
        >
            <div class="flex items-center gap-2">
                <component
                    v-if="tab.icon"
                    :is="dynamicComponent(tab.icon)"
                    class="w-4 h-4"
                    :class="activeTab === tab.id ? 'text-primary' : 'text-gray-secondary'"
                />
                {{ tab.label }}
            </div>
        </button>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { TabProps, TabId } from '@/types/event';
import * as Icons from '@heroicons/vue/24/outline';
import type { Component } from 'vue';

const props = defineProps<TabProps>();

const emit = defineEmits<{
    (e: 'update:tab', id: TabId): void;
}>();

const activeTab = ref<TabId>(props.defaultTab || (props.tabs[0]?.id as TabId));

const handleTabClick = (tabId: TabId): void => {
    activeTab.value = tabId;
    emit('update:tab', tabId);
};

const dynamicComponent = (iconName: string): Component | undefined => {
    return (Icons as { [key: string]: Component })[iconName];
};
</script>
