<script setup lang="ts">
defineProps<{
    message?: string;
    hint?: string;
}>();
</script>

<template>
    <div class="mt-1">
        <!-- Only show message if it's non-empty and not just spaces -->
        <p
            v-if="message && message.trim() !== ''"
            class="text-sm text-red-600 dark:text-red-400"
        >
            {{ message }}
        </p>

        <!-- Show hint only if there's no valid error message -->
        <p
            v-else-if="hint"
            class="text-sm text-gray-500 dark:text-gray-400"
        >
            {{ hint }}
        </p>
    </div>
</template>
