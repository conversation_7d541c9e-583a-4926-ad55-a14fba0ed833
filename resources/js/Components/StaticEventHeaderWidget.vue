<template>
    <Link :href="`/events/${event_id}/sessions`">
        <div class=" my-[24px] px-[24px] py-[16px] flex flex-col  rounded-2xl dark:border bg-[#FFFBF4] dark:bg-black dark:border-gray-800 shadow-soft">
            <span class="text-[#025259] font-[700] text-[18px]">{{ title }}</span>
            <div class="flex gap-2 justify-between flex-wrap">
                <p class="flex gap-1 items-center">
        <span class="text-[16px] font-[400] text-[#6F6F6F] dark:text-blue-muted text-sm">
          {{ desc }}
        </span>
                </p>
                <slot/>
            </div>
        </div>
    </Link>
</template>

<script setup lang="ts">
import { Link } from "@inertiajs/vue3";

interface Props {
  event_id: number | string;
  title: string;
  desc: string;
}

defineProps<Props>();
</script>
