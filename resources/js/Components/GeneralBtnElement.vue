<script setup lang="ts">
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps<{
    as?: string;
    href?: string;
    iconimg?: string;
    icon?: string;
    className?: string;
}>();

// Validator function
const validAsValues = ['button', 'link'] as const;
const isValidAs = (value: string) => validAsValues.includes(value as any);

// Default values and validation
const asType = computed(() => (isValidAs(props.as || '') ? props.as : 'button'));
const isLink = computed(() => asType.value === 'link');
const tag = computed(() => (isLink.value ? Link : 'button'));

const isImageIcon = computed(() => /\.(jpg|jpeg|png|svg|gif)$/i.test(props.icon || ""));
const isImageIconImage = computed(() => /\.(jpg|jpeg|png|svg|gif)$/i.test(props.iconimg || ""));

</script>

<template>
    <component
        :is="tag"
        :href="isLink ? href : undefined"
        v-bind="$attrs"
        :class="['flex h-8 p-4 gap-[10px] justify-center items-center rounded-full border border-solid border-[#a9a9a9] shadow-sm dark:border-gray-700', props.class]"
    >
        <img v-if="isImageIcon" :src="icon" alt="icon" class="w-4 h-4" />
        <img v-if="isImageIconImage" :src="iconimg" alt="iconimg" class="w-5 h-5" />
        <span><slot/></span>
    </component>
</template>
