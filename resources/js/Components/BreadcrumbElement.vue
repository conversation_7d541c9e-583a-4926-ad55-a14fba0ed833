<template>
  <div class="p-4 flex flex-col gap-4 rounded-lg shadow-[0px_0px_4px_0px_rgba(0,0,0,0.16)] bg-headerPattern bg-cover bg-center bg-no-repeat">
    <span class=" text-lg font-medium text-black dark:text-[#506690]">
      {{ title }}
    </span>
    <p class="text-white-dark dark:text-gray-500 text-sm font-medium flex items-center">
      <template v-for="(item, index) in items" :key="index">
        <span>
          {{ item }}
        </span>
        <svg v-if="index < items.length - 1"  xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class=" ltr:rotate-180 fill-[#DADADA] dark:fill-gray-500">
            <path d="M9 12.7371C9 12.9258 9.07203 13.1144 9.21578 13.2582L13.7417 17.7841C14.0296 18.072 14.4964 18.072 14.7842 17.7841C15.0719 17.4963 15.0719 17.0296 14.7842 16.7417L10.7794 12.7371L14.784 8.73257C15.0718 8.44467 15.0718 7.97802 14.784 7.69026C14.4962 7.40222 14.0294 7.40222 13.7415 7.69026L9.21564 12.216C9.07186 12.3599 9 12.5486 9 12.7371Z"/>
        </svg>
      </template>
    </p>
  </div>
</template>

<script setup lang="ts">
  defineProps<{
    title: string;
    items: string[];
  }>();
</script>
