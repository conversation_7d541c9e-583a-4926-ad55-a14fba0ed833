<template>
    <div class="custom-file-container">
        <div class="label-container">
            <label class="font-medium text-sm m-0">{{ label }}</label>
            <a
                v-if="fileName"
                href="javascript:;"
                class="custom-file-container__image-clear"
                title="Clear Image"
                @click="clearFile"
            >×</a>
        </div>
        <label class="custom-file-container__custom-file">
            <input
                ref="fileInput"
                type="file"
                class="custom-file-container__custom-file__custom-file-input"
                :accept="accept || 'image/*'"
                @change="handleFileChange"
                :required="required"
            />
            <input type="hidden" name="MAX_FILE_SIZE" :value="maxFileSize" />
            <span class="custom-file-container__custom-file__custom-file-control">
                <span class="browse">{{ $t('components.browse') }}</span>
                <span class="file-name">{{ fileName || $t('components.choose_file') }}</span>
            </span>
        </label>
        <!-- Show image preview for both URLs and Files -->
        <div v-if="showPreview && previewUrl || showPreview && imagePreviewUrl" class="custom-file-container__image-preview bg-white w-fit">
            <img :src="previewUrl || imagePreviewUrl" class="h-full" alt="Image preview" />
        </div>
        <!-- Error messages -->
        <p v-if="error" class="text-red-500 text-xs">{{ error }}</p>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

interface Props {
    label: string;
    accept?: string;
    maxFileSize?: number;
    required?: boolean;
    modelValue?: File | null;
    previewUrl?: string; // Existing image URL for preview
}

const props = withDefaults(defineProps<Props>(), {
    accept: 'image/*',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    required: false,
    modelValue: null,
    previewUrl: '',
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: File | null | string): void; // Allow string for clearing
}>();

// Refs
const fileInput = ref<HTMLInputElement | null>(null);
const imagePreviewUrl = ref<string | null>(null);
const fileName = ref<string>('');

const showPreview = computed(() => {
    if (props.previewUrl) {
        return props.previewUrl.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);
    }
    if (props.modelValue instanceof File) {
        return props.modelValue.type.startsWith('image/');
    }
    return false; // No preview if no file or URL, or if it's not an image

});

// Set the preview based on props - called in watch and on mount
const setPreview = () => {
    if (props.modelValue instanceof File) {
        fileName.value = props.modelValue.name;
        const reader = new FileReader();
        reader.onload = () => (imagePreviewUrl.value = reader.result as string);
        reader.readAsDataURL(props.modelValue);
    } else if (props.previewUrl) {
        fileName.value = props.previewUrl.split('/').pop() || 'Existing file';
        imagePreviewUrl.value = props.previewUrl;
    } else {
        clearPreview(); // Important: Clear if no file or URL
    }
};

// Clear the preview and file input
const clearPreview = () => {
    fileInput.value && (fileInput.value.value = '');
    imagePreviewUrl.value = null;
    fileName.value = '';
};

const clearFile = () => {
    clearPreview();
    emit('update:modelValue', null); // Emit null to signal clearing
};

// Watch for changes in props - ONLY for setting the preview
watch(() => [props.modelValue, props.previewUrl], setPreview, { immediate: true });

// Handle file change
const handleFileChange = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (file) {
        const reader = new FileReader();
        reader.onload = () => {
            imagePreviewUrl.value = reader.result as string;
            fileName.value = file.name;
            emit('update:modelValue', file);
        };
        reader.readAsDataURL(file);
    } else {
        setPreview();
    }
};
</script>

<style scoped>
.custom-file-container {
    @apply flex flex-col gap-2;
}

.custom-file-container__image-preview img {
    @apply max-h-40 object-contain;
}
</style>
