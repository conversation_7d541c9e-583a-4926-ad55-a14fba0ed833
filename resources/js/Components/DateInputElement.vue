<template>
  <div class="flex flex-col gap-2">
    <slot/>
    <div class="relative">
      <span v-if="icongroup" class="absolute inset-y-0 end-0 w-[56px] flex items-center justify-center bg-primary rounded-e-lg">
        <img v-if="isImageIconGroup" :src="icongroup" alt="icongroup" class="w-5 h-5" />
        <i v-else :class="icongroup" class="text-gray-500 text-[16px] text-white"></i>
      </span>
      <flat-pickr
        class="form-input text-sm font-medium rounded-lg"
        :config="calendarConf"
        :placeholder="placeholder"
        v-bind="$attrs"
      >
      </flat-pickr>
    </div>
  </div>
</template>


<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useAppStore } from '@/Stores/index';

  //flatpickr
  import flatPickr from 'vue-flatpickr-component';
  import 'flatpickr/dist/flatpickr.css';

  const props = defineProps<{
    placeholder?: string;
    icongroup?: string;
    enableTime?: boolean;
    dateFormat?: string;
    noCalendar?: boolean;
  }>();

  const isImageIconGroup = computed(() => /\.(jpg|jpeg|png|svg|gif)$/i.test(props.icongroup || ""));

  const store = useAppStore();

  const calendarConf: any = ref({
    enableTime: props.enableTime ?? true,
    dateFormat:  props.dateFormat ?? 'Y-m-d H:i',
    position: store.rtlClass === 'rtl' ? 'auto right' : 'auto left',
    noCalendar: props.noCalendar ?? false,
  });
</script>
