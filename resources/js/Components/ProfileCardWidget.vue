<script setup lang="ts">
import { computed } from "vue";

// Assign props to a variable
const props = defineProps<{
    image: string;
    name: string;
    fileLink?: string | null;
    fileLabel?: string;
    website?: string;
}>();

// Dynamically determine the component type
const FileComponent = computed(() => (props.fileLink ? "a" : "div"));

</script>

<template>
    <div class="min-h-24 p-4 bg-white dark:bg-[#181f32] rounded-2xl shadow items-center gap-4 inline-flex flex-wrap overflow-hidden">
        <img class="w-16 h-16 rounded-full" :src="image" />
        <div class="flex-col  items-start inline-flex">
            <div class="self-stretch items-center gap-2 inline-flex">
                <div class=" text-base font-semibold ">
                    {{ name }}
                </div>
                <div class=" items-center gap-1 flex">
                    <i class="ri-star-fill text-[#FFC107]"></i>
                    <div class="text-center  text-xs font-normal ">{{$t('profile.rating')}}</div>
                    <div class=" text-xs font-normal ">(3.5)</div>
                </div>
            </div>
            <div class="self-stretch  items-center gap-2 inline-flex">
                <div class=" items-center gap-1 flex">
                    <component :is="FileComponent" :href="fileLink || null" target="_blank" class="flex items-center gap-1">
                        <div class="bg-[#F5F9F9] dark:bg-[#141729] w-[24px] h-[24px] rounded-full flex items-center justify-center">
                            <i class="ri-file-download-line text-[#007172]"></i>
                        </div>
                        <div class="text-center text-[10px] font-normal ">
                            {{ fileLabel }}
                        </div>
                    </component>
                </div>
                <div class="w-[0.40px] h-6 bg-[#9da4a7]"></div>
                <div class=" items-center gap-1 flex">
                    <div class="bg-[#F5F9F9] dark:bg-[#141729] w-[24px] h-[24px] rounded-full flex items-center justify-center">
                        <i class="ri-global-line text-[#007172]"></i>
                    </div>
                    <div class="text-center text-[10px] font-normal ">
                        {{ website || $t("profile.no_website") }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
