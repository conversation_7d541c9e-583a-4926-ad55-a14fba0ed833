<template>
    <div>
        <!-- Price Plans Section Title -->
        <div class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
            {{ $t('Events.price_plans.title') }}
        </div>
        <!-- Pricing Cards -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
            <div v-for="plan in plans" :key="plan.title"
                 class="relative w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden plan-card">
                <!-- Discount Badge -->
                <div v-if="plan.disscount" class="absolute top-0 right-0 rtl:left-0 rtl:right-auto">
                    <div class="bg-teal-600 text-white rounded-bl-lg rtl:rounded-br-lg px-2 py-4 text-[12px]">
                        {{ $t('Events.price_plans.discount') }} {{ plan.disscount }}%
                    </div>
                </div>
                <!-- Plan Content -->
                <div class="relative p-6">
                    <!-- Title and Price Section -->
                    <div class="mb-8">
                        <h3 class="text-[16px] font-bold text-gray-800 dark:text-white mb-4">
                            {{ plan.title[currentLang] }}
                        </h3>
                        <div class="flex items-center gap-2 mb-2">
                            <span v-if="plan.disscount" class="text-[20px] font-bold text-orange-700">
                                {{ plan.price }}
                            </span>
                            <span
                                v-if="plan.disscount"
                                class=""
                                :class="{
                                    'line-through text-[16px] text-gray-400':plan.disscount,
                                    'text-[20px] font-bold text-orange-700':!plan.disscount
                                    }"
                            >
                                {{ plan.originalPrice }}
                            </span>
                            <span
                                v-else
                                class="text-[20px] font-bold text-orange-700"
                            >
                                {{ plan.price }}
                            </span>
                        </div>
                        <div class="text-[14px] font-bold text-gray-600 dark:text-white mb-[24px]">
                            {{ plan.ticketsCount }} {{ $t('Events.price_plans.ticket_available') }}
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="rounded-lg mb-[24px]">
                        <p class="text-gray-400 dark:text-gray-300 text-[14px] leading-relaxed">
                            {{ plan.description[currentLang] }}
                        </p>
                    </div>

                    <hr class="mb-[24px]">

                    <!-- Features Section -->
                    <div>
                        <h4 class="text-[14px] font-semibold text-gray-800 dark:text-white my-[24px]">
                            {{ $t('Events.price_plans.available_features') }}
                        </h4>
                        <ul class="space-y-4">
                            <li v-for="(feature, index) in plan.features" :key="index"
                                class="flex items-center gap-3">
                                <span v-if="feature.available"
                                      class="flex-shrink-0 w-6 h-6 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center">
                                    <svg class="w-4 h-4 text-teal-600 dark:text-teal-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                </span>
                                <span v-else
                                      class="flex-shrink-0 w-6 h-6 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                                    <svg class="w-4 h-4 text-red-600 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </span>
                                <span class="text-gray-600 dark:text-gray-300 text-[12px]">{{ feature.text }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed,ref,watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { locale } = useI18n();
let lang;
if(locale.value=='ae'){
  lang='ar'
}else if (locale.value) {
  lang = locale.value;
}

const currentLang = ref(lang);

watch(locale, (newLocale) => {
    currentLang.value = newLocale === 'ae' ? 'ar' : newLocale;
});


interface Feature {
    text: string;
    available: boolean;
}

interface PricingPlan {
    disscount: number;
    title: string;
    price: string;
    originalPrice: string;
    ticketsCount: number;
    description: string;
    features: Feature[];
}

defineProps<{
    plans: PricingPlan[]
}>();
</script>

<style scoped>
.plan-card {
    box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.05);
}
</style>
