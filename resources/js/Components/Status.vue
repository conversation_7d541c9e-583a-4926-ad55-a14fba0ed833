<template>
    <div class="status-badge h-8 my-6 rtl:pr-4 ltr:pl-4 bg-teal-light dark:bg-black rounded-lg justify-between items-center inline-flex overflow-hidden w-full">
        <!-- Category -->
        <div class="category-container justify-start items-center gap-2 flex">
            <span class="text-right text-primary text-sm font-semibold leading-[21px] font-cairo">
                {{ category }}
            </span>
        </div>
        <!-- Status Indicator -->
        <div
            class="status-indicator px-4 rounded-lg justify-center items-center gap-2.5 flex h-full"
            :class="statusColorClass"
        >
            <span class="text-right text-sm font-semibold leading-[21px] font-cairo" :class="statusTextColorClass">
                {{ status }}
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { StatusBadgeProps } from '@/types/event';

type StatusVariant = 'قادمة' | 'ongoing' | 'completed' | 'cancelled';

interface ColorClasses {
    [key: string]: string;
}

interface Props {
    status?: string
    category?: string
    variant?: StatusVariant
}

const props = withDefaults(defineProps<Props>(), {
    status: 'قادمة',
    category: 'ادارة اعمال',
    variant: 'قادمة'
})

const statusColorClass = computed<string>(() => {
    const classes: ColorClasses = {
        'قادمة': 'bg-orange-light',
        'ongoing': 'bg-success-light',
        'completed': 'bg-info-light',
        'cancelled': 'bg-danger-light'
    }
    return classes[props.variant] || classes['قادمة'];
})

const statusTextColorClass = computed<string>(() => {
    const classes: ColorClasses = {
        'قادمة': 'text-orange',
        'ongoing': 'text-success',
        'completed': 'text-info',
        'cancelled': 'text-danger'
    }
    return classes[props.variant] || classes['قادمة'];
})
</script>
