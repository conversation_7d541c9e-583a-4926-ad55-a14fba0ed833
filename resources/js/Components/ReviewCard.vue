<template>
    <div class="flex-shrink-0 w-full p-3 sm:p-4 bg-white dark:bg-primary-dark-light rounded-2xl shadow-soft flex flex-col gap-2 my-[24px]">
        <div class="flex justify-between items-center">
            <div class="flex items-center gap-1">
                <div class="flex flex-col ">
                    <div class="text-right text-gray-text text-base dark:text-white font-semibold leading-6 font-cairo">
                        {{ props?.user?.first_name }} {{ props?.user?.last_name }}
                    </div>
                    <div class="flex items-center  text-warning">
                        <i v-for="star in rate" class="ri-star-fill text-lg"></i>
                    </div>
                </div>
            </div>
            <div class="text-gray-medium text-xs font-normal leading-[18px] font-cairo">
                {{ formatDate(created_at) }}
            </div>
        </div>
        <p class=" text-gray-secondary text-sm font-normal leading-[21px] font-cairo">
            {{ comment }}
        </p>
    </div>
</template>

<script setup lang="ts">
import type { ReviewCardProps } from '@/types/event';
import moment from "moment/moment";

const props = defineProps<ReviewCardProps>();

const formatDate = (date: string | Date): string => {
    if (!date) return '';
    return moment(date).format('YYYY-MM-DD ');
};

</script>
