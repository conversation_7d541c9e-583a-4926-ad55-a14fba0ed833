.wizard-tab-container {
    @apply flex flex-col gap-[16px];
}

.vue-form-wizard.text-only .wizard-nav li .stepTitle {
    @apply absolute top-1/2 -translate-y-1/2 !mt-0;
}
.vue-form-wizard .wizard-nav li .stepTitle {
    @apply !text-inherit !mt-2;
}
.vue-form-wizard.text-only .wizard-nav li .stepTitle.active {
    @apply !text-white;
}
.vue-form-wizard .wizard-nav li .stepTitle.active {
    @apply !text-primary;
}
.vue-form-wizard .wizard-nav-pills > li > a {
    @apply !top-0 !text-black dark:!text-white-dark ;
}
.vue-form-wizard.text-only .wizard-nav-pills > li > a {
    @apply !mb-2;
}
.vue-form-wizard.text-only .wizard-nav li .wizard-icon {
    @apply !hidden;
}
.vue-form-wizard .wizard-icon-circle.tab_shape {
    @apply !rounded-full overflow-hidden dark:bg-[#1b2e4b];
}
.vue-form-wizard.pills .wizard-navigation .wizard-progress-with-circle {
    @apply !h-0;
}
.vue-form-wizard .wizard-tab-content {
    @apply !min-h-0 !p-0 my-6 -top-1 relative;
}
.vue-form-wizard {
    @apply !pb-0;
}
.vue-form-wizard .wizard-btn {
    @apply !py-2 !px-5 !border !rounded-md !min-w-0 shadow-[0_10px_20px_-10px] !bg-primary !border-primary shadow-primary/60 hover:shadow-none;
}
.vue-form-wizard .wizard-card-footer {
    @apply !px-0;
}
.vue-form-wizard.circle .wizard-icon-circle,
.vue-form-wizard.square .wizard-icon-circle {
    @apply !w-[32px] !h-[32px] !border-[#9da4a7] dark:!border-[#1b2e4b] bg-white dark:bg-[#253b5c] border-[2px];
}
.vue-form-wizard .wizard-navigation .wizard-progress-with-circle {
    @apply !top-[15px] bg-[#eee];
}
.vue-form-wizard.circle .wizard-nav-pills,
.vue-form-wizard.square .wizard-nav-pills {
    @apply -top-1 gap-16;
}
.vue-form-wizard .wizard-progress-bar {
    @apply rtl:!float-right !bg-primary;
}

.vue-form-wizard .wizard-icon{
    @apply !text-[13px];
}

.active .wizard-icon-container{@apply !bg-primary}
.vue-form-wizard .wizard-nav-pills li{@apply bg-[#fff] dark:bg-[#0e1726]}
.vue-form-wizard .wizard-nav-pills li{@apply bg-[#fff] dark:bg-[#060818]}
.vue-form-wizard .wizard-icon-circle.checked > i.wizard-icon {@apply hidden;}
.vue-form-wizard.circle .wizard-icon-circle.checked{@apply !bg-primary !border-primary text-white}
.vue-form-wizard.circle .wizard-icon-circle.checked span{@apply hidden}
.vue-form-wizard.circle .wizard-icon-circle.checked::before{content: "✓";@apply flex items-center}
.vue-form-wizard.circle li.active .wizard-icon-circle.checked::before{@apply hidden}

.vue-form-wizard .previous{@apply me-auto btn bg-transparent border border-primary rounded-full shadow-none}
.vue-form-wizard .next{@apply ms-auto btn btn-primary rounded-full shadow-none}
.vue-form-wizard .finish{@apply ms-auto btn btn-primary rounded-full shadow-none}

.rtl .rotate-rtl{transform: rotateY(180deg)}

.wizard-tab-container > div{@apply flex flex-col gap-4}
.stepTitle{@apply text-[12px]}

.wizard-nav.wizard-nav-pills li{@apply pointer-events-none}
