.custom-multiselect.multiselect {
    @apply !text-inherit !min-h-max;
}

.custom-multiselect.multiselect,
.custom-multiselect .multiselect__input,
.custom-multiselect .multiselect__single {
    @apply !text-sm;
}

.custom-multiselect.multiselect .multiselect__tags {
    @apply ltr:pl-4.5 rtl:pr-4.5 !py-2  !min-h-max;
}

.custom-multiselect.multiselect .multiselect__tags,
.custom-multiselect .multiselect__content-wrapper,
.custom-multiselect .multiselect--above .multiselect__content-wrapper {
    @apply bg-white dark:bg-[#1b2e4b] border-[rgb(224,230,237)] dark:border-[#253b5c];
}

.custom-multiselect .multiselect__single,
.custom-multiselect .multiselect__placeholder,
.custom-multiselect .multiselect__input {
    @apply !mb-0 !pl-0 dark:bg-[#1b2e4b];
}

.custom-multiselect .multiselect__option--highlight,
.custom-multiselect .multiselect__option--selected.multiselect__option--highlight,
.custom-multiselect .multiselect__option.multiselect__option--selected {
    @apply !bg-[#f6f6f6] dark:!bg-[#132136] !text-inherit;
}

.custom-multiselect .multiselect__option.multiselect__option--disabled {
    @apply !bg-transparent !text-[#999];
}

.custom-multiselect.multiselect--active .multiselect__placeholder {
    @apply !inline-block;
}

.custom-multiselect .multiselect__placeholder {
    @apply !pt-0 text-black dark:text-white-dark;
}

.custom-multiselect .multiselect__tag {
    @apply !mb-0 !py-[3px] !bg-success;
}

.custom-multiselect .multiselect__tags-wrap {
    @apply !flex;
}

.custom-multiselect .multiselect__tag-icon {
    @apply !top-auto;
}

.multiselect .multiselect__select{
    @apply top-0 h-[40px] bg-primary z-10;
}
.multiselect .multiselect__tags {
    height: 40px;
}

body .multiselect--active .multiselect__select{
    @apply transform rotate-0;
}

.multiselect .multiselect__select{
    border-end-end-radius: 5px;
    border-start-end-radius: 5px;
    width: 56px;
}
body .multiselect__select::before {
    content: "\ea4e";
    font-family: remixicon !important;
    border: 0;
    top: 25%;
    font-size: 16px;
    color: #fff;
}
body .multiselect--active .multiselect__select::before{
    @apply inline-block transform rotate-180 m-0;
}
body .multiselect__tags{
    padding-inline-end: 56px;
}
