<?php

use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Modules\User\app\Http\Controllers\Portal\ConfirmablePasswordController;
use Modules\User\app\Http\Controllers\Portal\EmailVerificationNotificationController;
use Modules\User\app\Http\Controllers\Portal\EmailVerificationPromptController;
use Modules\User\app\Http\Controllers\Portal\LoginController;
use Modules\User\app\Http\Controllers\Portal\NewPasswordController;
use Modules\User\app\Http\Controllers\Portal\PasswordController;
use Modules\User\app\Http\Controllers\Portal\PasswordResetLinkController;
use Modules\User\app\Http\Controllers\Portal\RegisterController;
use Modules\User\app\Http\Controllers\Portal\VerifyEmailController;
use Modules\User\app\Http\Controllers\Portal\VerifyOtpController;




Route::middleware('guest')->group(function () {
    Route::get('/', [DashboardController::class, 'portalIndex'])->name('home');
    Route::get('register', [RegisterController::class, 'create'])
                ->name('register');

    Route::post('register', [RegisterController::class, 'store']);

    Route::get('register/attendee', [\Modules\User\app\Http\Controllers\Portal\AttendeeRegisterController::class, 'create'])
                ->name('register.attendee');

    Route::post('register/attendee', [\Modules\User\app\Http\Controllers\Portal\AttendeeRegisterController::class, 'store']);

    Route::get('login', [LoginController::class, 'create'])
                ->name('login');

    Route::middleware(['throttle:5,3'])->group(function () {
        Route::post('login', [LoginController::class, 'store']);
    });

    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
                ->name('password.request');

    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
                ->name('password.email');

    Route::get('reset-password', [NewPasswordController::class, 'create'])
                ->name('password.reset');

    Route::post('reset-password', [NewPasswordController::class, 'store'])
                ->name('password.store');

    Route::get('verify-otp', [VerifyOtpController::class, 'create'])
        ->name('otp.reset');

    Route::post('verify-otp', [VerifyOtpController::class, 'validateCode'])
        ->name('otp.store');

    Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');
});

Route::middleware('auth')->group(function () {;

    Route::get('verify-email', EmailVerificationPromptController::class)
                ->name('verification.notice');

    Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
                ->middleware('throttle:6,1')
                ->name('verification.send');

    Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
                ->name('password.confirm');

    Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    Route::put('password', [PasswordController::class, 'update'])->name('password.update');

    Route::post('logout', [LoginController::class, 'destroy'])
                ->name('logout');

});
