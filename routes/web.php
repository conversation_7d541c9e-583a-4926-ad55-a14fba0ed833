<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Modules\Review\Http\Controllers\Portal\ReviewController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

require __DIR__ . '/auth.php';

Route::post('/language/switch', [\App\Http\Controllers\LanguageSwitcher::class, 'switch'])->name('language.switch');

Route::middleware(['web', 'auth'])->group(function() {
    Route::resource ('reviews', ReviewController::class);
});
