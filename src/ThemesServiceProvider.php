<?php

namespace Khaleds\Themes;

use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Khaleds\Shared\Helpers\Hooks;
use Khaleds\Shared\RoutServiceProvider;

class ThemesServiceProvider extends ServiceProvider
{
    public function register(): void
    {


    }

    public function boot(): void
    {
        $this->loadViewsFrom([
            base_path('core/khaleds/laravel-skin/'.env('ADMIN_THEME')),
            base_path('core/khaleds/laravel-skin/metronic/views/components'), // Components directory
        ], 'theme');

        // Register the component explicitly with correct path
        Blade::component('theme::dynamic-layout', 'theme::dynamic-layout');
        Blade::component('theme::info-table', 'theme::info-table');
        Blade::component('theme::info-table-row', 'theme::info-table-row');
        
        // Also register via namespace
        $namespace = 'Khaleds\Themes\metronic\Components';
        Blade::componentNamespace($namespace, 'theme');
    }
}
