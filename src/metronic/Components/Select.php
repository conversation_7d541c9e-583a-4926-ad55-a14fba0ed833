<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Select extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public $name,
        public $label = null,
        public $type = 'select',
        public $description = null,
        public $value = null,
        public $classes = '',
        public $placeholder = '',
        public $required = false,
        public $model = null,
        public $options = []
    )
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.select');
    }
}
