<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Toggle extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public string $name,
        public ?string $label = null,
        public bool $checked = false,
        public string $classes = '',
        public ?string $description = null,
        public bool $required = false,
        public ?object $model = null,
        public string $onText = 'On',
        public string $offText = 'Off',
        public string $onColor = 'primary',
        public string $offColor = 'light',
        public string $size = 'md',
        public bool $disabled = false
    )
    {
        // If model is provided, check if the toggle is checked
        if ($this->model && $this->name && isset($this->model->{$this->name})) {
            $this->checked = (bool) $this->model->{$this->name};
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.toggle');
    }
}
