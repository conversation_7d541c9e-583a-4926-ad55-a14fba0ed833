<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class DatePicker extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public $name,
        public $label = null,
        public $value = null,
        public $classes = '',
        public $placeholder = 'Select a date',
        public $required = false,
        public $model = null,
        public $description = null,
        public $icon = 'ki-duotone ki-element-11',
        public $iconBgClass = 'bg-secondary',
        public $format = 'Y-m-d',
        public $enableTime = false,
        public $minDate = null,
        public $maxDate = null,
    )
    {
        // Set value from model if provided
        if ($this->model && $this->name) {
            $this->value = $this->model->{$this->name};
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.date-picker');
    }
}
