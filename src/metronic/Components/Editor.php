<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Editor extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public $name,
        public $label = null,
        public $value = null,
        public $classes = '',
        public $placeholder = '',
        public $required = false,
        public $model = null,
        public $description = null,
        public $minHeight = '200px',
        public $toolbar = 'full',
        public $readonly = false,
    )
    {
        // Set value from model if provided
        if ($this->model && $this->name) {
            $this->value = $this->model->{$this->name};
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.editor');
    }
}
