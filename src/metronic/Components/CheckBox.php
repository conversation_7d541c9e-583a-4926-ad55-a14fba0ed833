<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class CheckBox extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public $name,
        public $label = null,
        public $value = '1',
        public $checked = false,
        public $classes = '',
        public $description = null,
        public $required = false,
        public $model = null,
        public $options = [],
        public $inline = false
    )
    {
        // If model is provided, check if the current value is selected
        if ($this->model && $this->name) {
            // For array values (multiple checkboxes with the same name)
            if (is_array($this->model->{$this->name}) && $this->value) {
                $this->checked = in_array($this->value, $this->model->{$this->name});
            }
            // For single checkbox
            elseif ($this->value) {
                $this->checked = $this->model->{$this->name} == $this->value;
            }
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.check-box');
    }
}
