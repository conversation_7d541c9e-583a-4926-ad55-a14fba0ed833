<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Yajra\DataTables\Html\Builder;

class Table extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public Builder $dataTable,
        public array   $filterColumns = [],
    )
    {
        //
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.table');
    }
}
