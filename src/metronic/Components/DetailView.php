<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use function App\View\Components\str_plural;

class DetailView extends Component
{
    public $item;
    public $fields;
    public $modelName;
    public $modelNamePlural;
    public $backRoute;
    public $editRoute;
    public $deleteRoute;
    public $title;

    /**
     * Create a new component instance.
     */
    public function __construct($item, $modelName = null, $modelNamePlural = null, $backRoute = null, $editRoute = null, $deleteRoute = null, $title = null , $fields = [])
    {
        $this->item = $item;
        $this->modelName = $modelName;
        $this->modelNamePlural = $modelNamePlural ?? str_plural($modelName);
        $this->backRoute = $backRoute;
        $this->editRoute = $editRoute;
        $this->deleteRoute = $deleteRoute;
        $this->title = $title;
        $this->fields = $fields;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.detail-view');
    }
}
