<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class FileUpload extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public string $name,
        public ?string $label = null,
        public ?string $value = null,
        public string $classes = '',
        public ?string $placeholder = 'Supported file types: All files. Max file size: 10MB.',
        public bool $required = false,
        public ?object $model = null,
        public ?string $description = null,
        public string $accept = '*',
        public bool $multiple = false,
        public int $maxFiles = 10,
        public int $maxSize = 10, // in MB
        public ?string $previewTemplate = null,
        public ?string $uploadUrl = null,
        public ?string $deleteUrl = null,
        public ?string $thumbnailWidth = '120px',
        public ?string $thumbnailHeight = '120px',
        public ?string $icon = 'ki-duotone ki-file-up text-primary fs-3x',
    )
    {
        // Set value from model if provided
        if ($this->model && $this->name && isset($this->model->{$this->name})) {
            $this->value = $this->model->{$this->name};
        }

        // Convert accept to proper format if it's a common type
        $this->accept = $this->formatAcceptAttribute($accept);
    }

    /**
     * Format the accept attribute to proper MIME types
     */
    private function formatAcceptAttribute(string $accept): string
    {
        $commonTypes = [
            'image' => 'image/*',
            'images' => 'image/*',
            'audio' => 'audio/*',
            'video' => 'video/*',
            'pdf' => 'application/pdf',
            'doc' => '.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'excel' => '.xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv' => 'text/csv',
            'zip' => 'application/zip,application/x-zip-compressed',
        ];

        return $commonTypes[$accept] ?? $accept;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.file-upload-fixed');
    }
}
