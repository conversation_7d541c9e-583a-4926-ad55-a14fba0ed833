<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ImageInput extends Component
{
    public $name;
    public $value;
    public $width;
    public $height;
    public $placeholderPath;
    public $currentImagePath;
    /**
     * Create a new component instance.
     */
    public function __construct( $name = 'avatar',
                                 $value = null,
                                 $width = '125px',
                                 $height = '125px',
                                 $placeholderPath = '/assets/media/svg/avatars/blank.svg',
                                 $currentImagePath = null)
    {
        $this->name = $name;
        $this->value = $value;
        $this->width = $width;
        $this->height = $height;
        $this->placeholderPath = $placeholderPath;
        $this->currentImagePath = $currentImagePath ?: $placeholderPath;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.image-input');
    }
}
