<?php

namespace Khaleds\Themes\metronic\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Tags extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public string $name,
        public ?string $label = null,
        public ?string $value = null,
        public string $classes = '',
        public ?string $placeholder = 'Type something...',
        public bool $required = false,
        public ?object $model = null,
        public ?string $description = null,
        public bool $solid = false,
        public ?string $id = null
    )
    {
        // Set value from model if provided
        if ($this->model && $this->name && isset($this->model->{$this->name})) {
            $this->value = $this->model->{$this->name};
        }
        
        // Generate a unique ID if not provided
        if (!$this->id) {
            $this->id = 'kt_tagify_' . uniqid();
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('theme::admin.components.tags');
    }
}
