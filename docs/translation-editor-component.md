# Translation Editor Component

A reusable Laravel Blade component for creating multilingual rich text editors in your admin dashboard using Quill. This component supports multiple languages with tabbed navigation and provides rich text editing capabilities.

## Features

- Multilingual content editing with tabbed interface
- Rich text editing with formatting options
- Customizable toolbar (full, basic, minimal)
- Adjustable minimum height
- Read-only mode option
- Error handling with tab highlighting
- Model binding support for translated content
- RTL support for Arabic content
- Optional label and description

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Dependencies

This component relies on Quill for the rich text editor functionality and Bootstrap for the tabs. Make sure these are included in your layout:

```html
<!-- Include in your layout's head section -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
```

## Basic Usage

```php
<x-theme::translation-editor
    name="description"
    label="Product Description"
    :required="true"
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The field name to create the editor for. |
| `label` | string | null | The label text to display above the editor. |
| `description` | string | null | Additional description text displayed below the editor. |
| `placeholder` | string | null | Custom placeholder text for the editor. If not provided, a default placeholder is generated. |
| `required` | boolean | false | Whether the field is required. |
| `languages` | array\|string | ['en', 'ar'] | Array of language codes or comma-separated string of language codes to create tabs for. |
| `model` | object | null | Eloquent model to automatically retrieve translated values from. |
| `minHeight` | string | '200px' | Minimum height of the editor. |
| `toolbar` | string | 'full' | Toolbar configuration: 'full', 'basic', or 'minimal'. |
| `readonly` | boolean | false | Whether the editor is in read-only mode. |

## Examples

### Basic Translation Editor

```php
<x-theme::translation-editor
    name="description"
    label="Product Description"
    description="Set a description to the product for better visibility."
    :required="true"
/>
```

### Translation Editor with Custom Toolbar

```php
<x-theme::translation-editor
    name="summary"
    label="Product Summary"
    toolbar="basic"
    minHeight="150px"
    description="A brief summary that appears in product listings."
/>
```

### Translation Editor with Custom Placeholder

```php
<x-theme::translation-editor
    name="features"
    label="Product Features"
    placeholder="List the key features of your product here..."
    description="Highlight the most important features of your product."
/>
```

### Translation Editor with Custom Languages

```php
<x-theme::translation-editor
    name="description"
    label="Product Description"
    :languages="['en', 'ar', 'fr']"
    description="Description in multiple languages."
/>
```

### Translation Editor with Languages as String

```php
<x-theme::translation-editor
    name="description"
    label="Product Description"
    languages="en,ar,fr"
    description="Description in multiple languages."
/>
```

### Translation Editor with Model

```php
<x-theme::translation-editor
    name="specifications"
    label="Product Specifications"
    :model="$product"
    description="Technical details and specifications of the product."
/>
```

### Read-only Translation Editor

```php
<x-theme::translation-editor
    name="terms_conditions"
    label="Terms and Conditions"
    :model="$terms"
    :readonly="true"
    description="Legal terms that users must agree to."
/>
```

## Model Requirements

The component expects the model to have a `getTranslation` method that accepts the field name, locale, and a fallback flag:

```php
public function getTranslation($field, $locale, $fallback = true)
{
    // Return the translated value for the field in the specified locale
}
```

This is compatible with the Laravel Translatable package or any custom implementation that follows this pattern.

## Toolbar Options

The component supports three predefined toolbar configurations:

### Full Toolbar (default)
Includes all formatting options: bold, italic, underline, strike, blockquote, code-block, headers, lists, indentation, text direction, font size, text color, background color, font family, alignment, links, images, and videos.

### Basic Toolbar
Includes only essential formatting options: bold, italic, underline, and clear formatting.

### Minimal Toolbar
Includes only the most basic formatting options: bold, italic, and clear formatting.

## Handling Form Submission

The editor content is stored in hidden input fields with names in the format `fieldname[locale]`, so you can handle it in your controller like this:

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'description.en' => 'required|string',
        'description.ar' => 'required|string',
    ]);

    $product->setTranslation('description', 'en', $request->input('description.en'));
    $product->setTranslation('description', 'ar', $request->input('description.ar'));
    $product->save();

    // ...
}
```

## RTL Support

The component automatically detects Arabic content and sets the appropriate text direction (RTL) for Arabic editors. This ensures proper display and editing of right-to-left text.

## Error Handling

The component automatically highlights tabs with validation errors and displays error messages below the corresponding fields. It also automatically switches to the first tab with errors when the page loads.

## Styling

The component uses the Quill editor with the Snow theme by default, which provides a clean and modern interface. The editor container has a minimum height specified by the `minHeight` prop.

## Advanced Usage

### Custom Attributes

You can pass additional HTML attributes to the component:

```php
<x-theme::translation-editor
    name="description"
    data-kt-target="#some-element"
    data-kt-action="update"
/>
```

### Handling HTML Content

Since the editor produces HTML content, you may want to sanitize the input before storing it in your database:

```php
use Illuminate\Support\Facades\Purifier;

public function store(Request $request)
{
    $validated = $request->validate([
        'description.en' => 'required|string',
        'description.ar' => 'required|string',
    ]);

    $product->setTranslation('description', 'en', Purifier::clean($request->input('description.en')));
    $product->setTranslation('description', 'ar', Purifier::clean($request->input('description.ar')));
    $product->save();

    // ...
}
```

## Displaying Translated Content

When displaying the content from the editor in your views, make sure to use the `{!! !!}` syntax to render the HTML:

```php
<div class="product-description">
    {!! $product->getTranslation('description', app()->getLocale()) !!}
</div>
```
