# File Upload Component

A reusable Laravel Blade component for creating file upload interfaces in your admin dashboard using Dropzone.js.

## Features

- Single or multiple file uploads
- File type restrictions
- File size limits
- Custom preview templates
- Server-side upload and delete integration
- Drag and drop support
- Progress indicators
- Thumbnail previews for images
- Error handling
- Model binding support

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Dependencies

This component relies on Dropzone.js which is included in the Metronic plugins bundle. Make sure these are included in your layout:

```html
<!-- Include in your layout's head section -->
<link href="{{ asset('metronic/plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css" />
<script src="{{ asset('metronic/plugins/global/plugins.bundle.js') }}"></script>
```

## Basic Usage

```php
<x-theme::file-upload
    name="document"
    label="Upload Document"
    description="Max file size is 10MB and max number of files is 10."
    :required="true"
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The name attribute for the file input. |
| `label` | string | null | The label text for the file input. |
| `value` | string | null | JSON string of existing files to display. |
| `classes` | string | '' | Additional CSS classes for the dropzone container. |
| `placeholder` | string | 'Supported file types: All files. Max file size: 10MB.' | Informational text displayed next to the control buttons. |
| `required` | boolean | false | Whether the field is required. |
| `model` | object | null | Eloquent model to automatically retrieve the value from. |
| `description` | string | null | Additional description text displayed below the dropzone. |
| `accept` | string | '*' | File types to accept (e.g., 'image/*', '.pdf,.doc', etc.). |
| `multiple` | boolean | false | Whether to allow multiple file uploads. |
| `maxFiles` | integer | 10 | Maximum number of files allowed when multiple is true. |
| `maxSize` | integer | 10 | Maximum file size in MB. |
| `previewTemplate` | string | null | Custom HTML template for file previews. |
| `uploadUrl` | string | null | URL for server-side upload handling. |
| `deleteUrl` | string | null | URL for server-side file deletion. |
| `thumbnailWidth` | string | '120px' | Width of thumbnail previews. |
| `thumbnailHeight` | string | '120px' | Height of thumbnail previews. |
| `icon` | string | 'ki-duotone ki-file-up text-primary fs-3x' | Icon class for the dropzone. |
| `columnSize` | string | 'col-md-12' | Bootstrap column size class. |

## Examples

### Basic File Upload

```php
<x-theme::file-upload
    name="document"
    label="Upload Document"
    description="Max file size is 10MB"
    :required="true"
/>
```

### Image Upload with Preview

```php
<x-theme::file-upload
    name="image"
    label="Upload Image"
    accept="image"
    description="Accepted formats: JPG, PNG, GIF"
    placeholder="Drop image here or click to browse"
    icon="ki-duotone ki-picture fs-3x text-primary"
/>
```

### Multiple File Upload

```php
<x-theme::file-upload
    name="attachments"
    label="Upload Attachments"
    :multiple="true"
    :maxFiles="5"
    description="Upload up to 5 files (10MB each)"
    placeholder="Drop files here or click to browse"
/>
```

### Document Upload with Specific Types

```php
<x-theme::file-upload
    name="documents"
    label="Upload Documents"
    accept=".pdf,.doc,.docx"
    :multiple="true"
    description="Accepted formats: PDF, DOC, DOCX"
    placeholder="Drop documents here or click to browse"
    icon="ki-duotone ki-document fs-3x text-primary"
/>
```

### File Upload with Server Configuration

```php
<x-theme::file-upload
    name="server_files"
    label="Upload to Server"
    uploadUrl="{{ route('api.uploads.store') }}"
    deleteUrl="{{ route('api.uploads.destroy') }}"
    :multiple="true"
    description="Files will be uploaded immediately"
    placeholder="Drop files here or click to browse"
/>
```

### File Upload with Existing Files

```php
@php
    $existingFiles = json_encode([
        [
            'name' => 'example.jpg',
            'size' => 1024 * 1024 * 2, // 2MB
            'url' => asset('path/to/example.jpg'),
            'id' => '123456'
        ]
    ]);
@endphp

<x-theme::file-upload
    name="existing_files"
    label="Manage Files"
    :value="$existingFiles"
    :multiple="true"
    description="You can add or remove files"
    placeholder="Drop files here or click to browse"
/>
```

## Handling File Uploads

### Client-Side Only

If you don't specify an `uploadUrl`, the component will handle files client-side only. The files will be submitted with the form as base64-encoded data in a hidden input field.

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'document' => 'required|string', // JSON string with file data
    ]);

    $document = json_decode($request->document, true);

    // Process the file data
    // $document contains: name, size, type, id, url (base64 data URL)

    // ...
}
```

### Server-Side Upload

If you specify an `uploadUrl`, files will be uploaded to the server immediately when added to the dropzone.

```php
// routes/web.php
Route::post('/api/uploads', [UploadController::class, 'store'])->name('api.uploads.store');
Route::post('/api/uploads/delete', [UploadController::class, 'destroy'])->name('api.uploads.destroy');

// UploadController.php
public function store(Request $request)
{
    $file = $request->file('file');
    $path = $file->store('uploads');

    return response()->json([
        'id' => $path,
        'url' => Storage::url($path),
        'name' => $file->getClientOriginalName(),
        'size' => $file->getSize()
    ]);
}

public function destroy(Request $request)
{
    $id = $request->input('id');
    Storage::delete($id);

    return response()->json(['success' => true]);
}
```

## File Value Format

The component expects and produces a JSON string with the following structure:

### Single File

```json
{
    "name": "example.jpg",
    "size": 2097152,
    "type": "image/jpeg",
    "id": "123456",
    "url": "https://example.com/path/to/example.jpg"
}
```

### Multiple Files

```json
[
    {
        "name": "example1.jpg",
        "size": 2097152,
        "type": "image/jpeg",
        "id": "123456",
        "url": "https://example.com/path/to/example1.jpg"
    },
    {
        "name": "example2.pdf",
        "size": 1048576,
        "type": "application/pdf",
        "id": "789012",
        "url": "https://example.com/path/to/example2.pdf"
    }
]
```

## Component Structure

The file upload component follows the Metronic admin theme structure with the following elements:

1. **Controls Panel**: Contains buttons for attaching files and removing all files
   ```html
   <div class="dropzone-panel mb-lg-0 mb-2">
       <a class="dropzone-select btn btn-sm btn-primary me-2">Attach files</a>
       <a class="dropzone-remove-all btn btn-sm btn-light-primary">Remove All</a>
   </div>
   ```

2. **Items Container**: Displays the uploaded files with progress bars
   ```html
   <div class="dropzone-items wm-200px">
       <div class="dropzone-item">
           <!-- File details, progress bar, and remove button -->
       </div>
   </div>
   ```

## Styling

The component uses the Metronic admin theme's styling by default. It applies the following classes:
- `dropzone`
- `dropzone-queue`
- `dropzone-panel`
- `dropzone-items`
- `dropzone-item`

You can add additional classes using the `classes` prop.

## Error Handling

The component automatically displays validation errors if they exist for the given field name.

```php
@error($name)
<div class="invalid-feedback d-block">
    <strong>{{ $message }}</strong>
</div>
@enderror
```

## Advanced Usage

### Custom Preview Template

You can provide a custom preview template for the files:

```php
<x-theme::file-upload
    name="custom_preview"
    label="Custom Preview"
    :previewTemplate="'<div class=\"dz-preview dz-file-preview\">
        <div class=\"dz-details\">
            <div class=\"dz-filename\"><span data-dz-name></span></div>
            <div class=\"dz-size\" data-dz-size></div>
            <img data-dz-thumbnail />
        </div>
        <div class=\"dz-progress\"><span class=\"dz-upload\" data-dz-uploadprogress></span></div>
        <div class=\"dz-success-mark\"><span>✔</span></div>
        <div class=\"dz-error-mark\"><span>✘</span></div>
        <div class=\"dz-error-message\"><span data-dz-errormessage></span></div>
    </div>'"
/>
```

### CSRF Token

Make sure to include the CSRF token in your layout for server-side uploads:

```html
<meta name="csrf-token" content="{{ csrf_token() }}">
```

### File Type Shortcuts

The component provides shortcuts for common file types:

- `image` or `images`: Accepts all image types
- `audio`: Accepts all audio types
- `video`: Accepts all video types
- `pdf`: Accepts PDF files
- `doc`: Accepts Word documents
- `excel`: Accepts Excel spreadsheets
- `csv`: Accepts CSV files
- `zip`: Accepts ZIP archives

```php
<x-theme::file-upload
    name="images"
    label="Upload Images"
    accept="image"
/>
```

is equivalent to:

```php
<x-theme::file-upload
    name="images"
    label="Upload Images"
    accept="image/*"
/>
```
