# Checkbox Component

A reusable Laravel Blade component for creating checkboxes and checkbox groups in your admin dashboard.

## Features

- Single checkboxes
- Checkbox groups (multiple selection)
- Inline or vertical layout options
- Customizable labels and descriptions
- Error handling integration
- Pre-selected values support
- Required field validation
- Model binding support

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Basic Usage

### Single Checkbox

```php
<x-theme::check-box 
    name="agree_terms"
    label="I agree to the terms and conditions"
    value="1"
    :checked="true"
/>
```

### Checkbox Group

```php
<x-theme::check-box 
    name="interests"
    label="Select Your Interests"
    :options="[
        'technology' => 'Technology',
        'sports' => 'Sports',
        'music' => 'Music'
    ]"
/>
```

### Inline Checkbox Group

```php
<x-theme::check-box 
    name="notifications"
    label="Notification Preferences"
    :inline="true"
    :options="[
        'email' => 'Email',
        'sms' => 'SMS',
        'push' => 'Push Notification'
    ]"
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The name attribute for the checkbox element. For groups, `[]` is automatically appended. |
| `label` | string | null | The label text for the checkbox element. |
| `value` | string | '1' | The value of the checkbox. |
| `checked` | boolean | false | Whether the checkbox is checked by default (for single checkboxes). |
| `classes` | string | '' | Additional CSS classes for the checkbox element. |
| `description` | string | null | Additional description text displayed below the checkbox. |
| `required` | boolean | false | Whether the field is required. |
| `model` | object | null | Eloquent model to automatically retrieve the value from. |
| `options` | array | [] | Array of options for checkbox groups. Format: `['value' => 'label']` |
| `inline` | boolean | false | Whether to display checkboxes inline (horizontally) instead of vertically. |

## Examples

### Single Checkbox

```php
<x-theme::check-box 
    name="agree_terms"
    label="I agree to the terms and conditions"
    value="1"
    :checked="true"
    description="By checking this box, you agree to our Terms of Service and Privacy Policy."
/>
```

### Checkbox Group (Vertical)

```php
<x-theme::check-box 
    name="interests"
    label="Select Your Interests"
    :options="[
        'technology' => 'Technology',
        'sports' => 'Sports',
        'music' => 'Music',
        'travel' => 'Travel',
        'food' => 'Food & Cooking'
    ]"
    :required="true"
/>
```

### Checkbox Group (Inline)

```php
<x-theme::check-box 
    name="notifications"
    label="Notification Preferences"
    :inline="true"
    :options="[
        'email' => 'Email',
        'sms' => 'SMS',
        'push' => 'Push Notification'
    ]"
    description="Choose how you'd like to receive notifications."
/>
```

### With Eloquent Model

When using with an Eloquent model, the component will automatically check the checkboxes that match the model's values:

```php
<x-theme::check-box 
    name="permissions"
    label="User Permissions"
    :model="$user"
    :options="[
        'view' => 'View',
        'edit' => 'Edit',
        'create' => 'Create',
        'delete' => 'Delete'
    ]"
    :inline="true"
/>
```

## Handling Form Submission

For checkbox groups, the values are submitted as an array:

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'interests' => 'required|array',
        'interests.*' => 'in:technology,sports,music,travel,food',
    ]);
    
    $user->interests = $request->interests;
    $user->save();
    
    // ...
}
```

For single checkboxes, you can check if the value is present:

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'agree_terms' => 'required|accepted',
    ]);
    
    $user->has_agreed_terms = (bool) $request->agree_terms;
    $user->save();
    
    // ...
}
```

## Styling

The component uses the Metronic admin theme's styling by default. It applies the following classes:
- `form-check`
- `form-check-custom`
- `form-check-solid`
- `form-check-input`
- `form-check-label`

You can add additional classes using the `classes` prop.

## Error Handling

The component automatically displays validation errors if they exist for the given field name.

```php
@error($name)
<span class="invalid-feedback" role="alert">
    <strong>{{ $message }}</strong>
</span>
@enderror
```

## Advanced Usage

### Custom Attributes

You can pass additional HTML attributes to the checkbox element:

```php
<x-theme::check-box 
    name="agree_terms"
    label="I agree to the terms and conditions"
    value="1"
    data-kt-check="true"
    data-kt-check-target="#kt_some_element"
/>
```

### With a Different Name Format

If you need a different name format for nested forms:

```php
<x-theme::check-box 
    name="user[settings]"
    label="User Settings"
    :options="$settingsArray"
/>
```

Note: For checkbox groups with nested names, you may need to handle the array notation manually in your controller.
