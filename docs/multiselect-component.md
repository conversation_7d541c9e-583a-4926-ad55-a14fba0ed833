# Multiselect Component

A reusable Laravel Blade component for creating multiselect dropdowns in your admin dashboard using Select2.

## Features

- Multiple item selection
- Searchable dropdown
- Taggable option (create new entries)
- Customizable placeholder, label, and description
- Error handling integration
- Pre-selected values support
- Required field validation

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Basic Usage

```php
<x-theme::multiselect 
    name="categories"
    label="Select Categories"
    placeholder="Choose multiple categories"
    :options="[
        '1' => 'Category 1',
        '2' => 'Category 2',
        '3' => 'Category 3'
    ]"
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The name attribute for the select element. Will be submitted as an array. |
| `label` | string | null | The label text for the select element. |
| `description` | string | null | Additional description text displayed below the select element. |
| `value` | array | [] | Pre-selected values. |
| `classes` | string | '' | Additional CSS classes for the select element. |
| `placeholder` | string | '' | Placeholder text for the select element. |
| `required` | boolean | false | Whether the field is required. |
| `model` | object | null | Eloquent model to automatically retrieve the value from. |
| `options` | array | [] | Array of options for the select element. Format: `['value' => 'label']` |
| `allowClear` | boolean | true | Whether to allow clearing the selection. |
| `closeOnSelect` | boolean | false | Whether to close the dropdown after selecting an option. |
| `taggable` | boolean | false | Whether to allow creating new options that don't exist in the list. |

## Examples

### Basic Multiselect

```php
<x-theme::multiselect 
    name="categories"
    label="Select Categories"
    placeholder="Choose multiple categories"
    :options="$categoriesArray"
/>
```

### With Pre-selected Values

```php
<x-theme::multiselect 
    name="categories"
    label="Select Categories"
    placeholder="Choose multiple categories"
    :value="['1', '3']"
    :options="[
        '1' => 'Category 1',
        '2' => 'Category 2',
        '3' => 'Category 3'
    ]"
/>
```

### Taggable Multiselect (Create New Options)

```php
<x-theme::multiselect 
    name="tags"
    label="Select or Create Tags"
    placeholder="Select or type to create new tags"
    :taggable="true"
    :options="$tagsArray"
    description="Type to create new options that don't exist in the list"
/>
```

### Required Multiselect

```php
<x-theme::multiselect 
    name="categories"
    label="Select Categories"
    placeholder="Select categories (required)"
    :required="true"
    :options="$categoriesArray"
/>
```

### With Eloquent Model

When using with an Eloquent model, the component will automatically retrieve the value from the model:

```php
<x-theme::multiselect 
    name="categories"
    label="Select Categories"
    placeholder="Choose multiple categories"
    :model="$post"
    :options="$categoriesArray"
/>
```

## Handling Form Submission

Since the multiselect submits as an array, you can handle it in your controller like this:

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'categories' => 'required|array',
        'categories.*' => 'exists:categories,id',
    ]);
    
    $model->categories()->sync($request->categories);
    
    // ...
}
```

## Styling

The component uses the Metronic admin theme's styling by default. It applies the following classes:
- `form-select`
- `form-select-solid`

You can add additional classes using the `classes` prop.

## JavaScript Dependencies

This component relies on Select2 for enhanced functionality. Make sure the Select2 library is loaded in your layout.

## Error Handling

The component automatically displays validation errors if they exist for the given field name.

```php
@error($name)
<span class="invalid-feedback" role="alert">
    <strong>{{ $message }}</strong>
</span>
@enderror
```

## Advanced Usage

### Custom Attributes

You can pass additional HTML attributes to the select element:

```php
<x-theme::multiselect 
    name="categories"
    label="Select Categories"
    :options="$categoriesArray"
    data-dropdown-parent="#modal_id"
/>
```

### With a Different Name Format

If you need a different name format for nested forms:

```php
<x-theme::multiselect 
    name="post[categories]"
    label="Select Categories"
    :options="$categoriesArray"
/>
```

Note: The component will automatically add `[]` to the name attribute to ensure it's submitted as an array.
