# Toggle Switch Component

A reusable Laravel Blade component for creating toggle switches in your admin dashboard.

## Features

- Simple on/off toggle switches
- Customizable on/off text
- Multiple size options (small, medium, large)
- Customizable colors
- Error handling integration
- Pre-selected state support
- Required field validation
- Model binding support
- Disabled state support

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Basic Usage

### Simple Toggle Switch

```php
<x-theme::toggle 
    name="is_active"
    label="Active Status"
    :checked="true"
/>
```

### Toggle Switch with Custom Text

```php
<x-theme::toggle 
    name="notifications"
    label="Email Notifications"
    onText="Enabled"
    offText="Disabled"
    description="Receive email notifications for new messages."
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The name attribute for the toggle input. |
| `label` | string | null | The label text for the toggle. |
| `checked` | boolean | false | Whether the toggle is checked by default. |
| `classes` | string | '' | Additional CSS classes for the toggle. |
| `description` | string | null | Description text displayed below the label. |
| `required` | boolean | false | Whether the field is required. |
| `model` | object | null | Model instance for binding data. |
| `onText` | string | 'On' | Text displayed when toggle is on. |
| `offText` | string | 'Off' | Text displayed when toggle is off. |
| `onColor` | string | 'primary' | Color theme for the toggle when on (primary, success, info, warning, danger). |
| `offColor` | string | 'light' | Color theme for the toggle when off. |
| `size` | string | 'md' | Size of the toggle (sm, md, lg). |
| `disabled` | boolean | false | Whether the toggle is disabled. |

## Examples

### Basic Toggle

```php
<x-theme::toggle 
    name="is_active"
    label="Active Status"
    :checked="true"
/>
```

### Custom Colors and Size

```php
<x-theme::toggle 
    name="is_featured"
    label="Featured Item"
    onText="Featured"
    offText="Regular"
    onColor="success"
    size="lg"
    description="Featured items appear at the top of the list."
/>
```

### Toggle with Model Binding

```php
<x-theme::toggle 
    name="is_published"
    label="Publication Status"
    onText="Published"
    offText="Draft"
    :model="$post"
    description="Published posts are visible to all users."
/>
```

### Disabled Toggle

```php
<x-theme::toggle 
    name="is_premium"
    label="Premium Feature"
    :checked="true"
    :disabled="true"
    description="This feature is only available to premium users."
/>
```

## Events

The toggle component dispatches a custom `toggle-changed` event when its state changes. You can listen for this event to perform additional actions:

```javascript
document.addEventListener('toggle-changed', function(event) {
    console.log('Toggle changed:', event.detail.name, 'is now', event.detail.checked);
    // Perform additional actions based on the toggle state
});
```

## Styling

The toggle component uses Bootstrap's form-switch component with custom styling. You can customize the appearance further by adding your own CSS classes.

## Validation

The toggle component integrates with Laravel's validation system. If validation fails, error messages will be displayed below the toggle.

```php
// In your controller
$request->validate([
    'is_active' => 'required|accepted',
]);
```

## Accessibility

The toggle component is built with accessibility in mind, using proper labels and ARIA attributes for screen readers.
