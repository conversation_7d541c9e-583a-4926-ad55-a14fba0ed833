# Radio Button Component

A reusable Laravel Blade component for creating radio buttons and radio button groups in your admin dashboard.

## Features

- Single radio buttons
- Radio button groups
- Inline or vertical layout options
- Customizable labels and descriptions
- Error handling integration
- Pre-selected values support
- Required field validation
- Model binding support

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Basic Usage

### Single Radio Button

```php
<x-theme::radio 
    name="agree_terms"
    label="I agree to the terms and conditions"
    value="1"
    :checked="true"
/>
```

### Radio Button Group

```php
<x-theme::radio 
    name="gender"
    label="Gender"
    value="male"
    :options="[
        'male' => 'Male',
        'female' => 'Female',
        'other' => 'Other'
    ]"
/>
```

### Inline Radio Button Group

```php
<x-theme::radio 
    name="payment_method"
    label="Payment Method"
    value="credit_card"
    :inline="true"
    :options="[
        'credit_card' => 'Credit Card',
        'paypal' => 'PayPal',
        'bank_transfer' => 'Bank Transfer'
    ]"
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The name attribute for the radio button element. |
| `label` | string | null | The label text for the radio button element. |
| `value` | string | null | The value of the radio button. Also used as the default selected value for radio groups. |
| `checked` | boolean | false | Whether the radio button is checked by default (for single radio buttons). |
| `classes` | string | '' | Additional CSS classes for the radio button element. |
| `description` | string | null | Additional description text displayed below the radio button. |
| `required` | boolean | false | Whether the field is required. |
| `model` | object | null | Eloquent model to automatically retrieve the value from. |
| `options` | array | [] | Array of options for radio button groups. Format: `['value' => 'label']` |
| `inline` | boolean | false | Whether to display radio buttons inline (horizontally) instead of vertically. |

## Examples

### Single Radio Button

```php
<x-theme::radio 
    name="agree_terms"
    label="I agree to the terms and conditions"
    value="1"
    :checked="true"
    description="By checking this box, you agree to our Terms of Service and Privacy Policy."
/>
```

### Radio Button Group (Vertical)

```php
<x-theme::radio 
    name="gender"
    label="Gender"
    value="male"
    :options="[
        'male' => 'Male',
        'female' => 'Female',
        'other' => 'Other'
    ]"
    :required="true"
/>
```

### Radio Button Group (Inline)

```php
<x-theme::radio 
    name="payment_method"
    label="Payment Method"
    value="credit_card"
    :inline="true"
    :options="[
        'credit_card' => 'Credit Card',
        'paypal' => 'PayPal',
        'bank_transfer' => 'Bank Transfer'
    ]"
    description="Choose your preferred payment method."
/>
```

### With Eloquent Model

When using with an Eloquent model, the component will automatically check the radio button that matches the model's value:

```php
<x-theme::radio 
    name="notification_preference"
    label="Notification Preferences"
    :model="$user"
    :options="[
        'email' => 'Email',
        'sms' => 'SMS',
        'push' => 'Push Notification',
        'none' => 'No Notifications'
    ]"
    :inline="true"
/>
```

## Handling Form Submission

You can handle radio button submissions in your controller like this:

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'gender' => 'required|in:male,female,other',
    ]);
    
    $user->gender = $request->gender;
    $user->save();
    
    // ...
}
```

## Styling

The component uses the Metronic admin theme's styling by default. It applies the following classes:
- `form-check`
- `form-check-custom`
- `form-check-solid`
- `form-check-input`
- `form-check-label`

You can add additional classes using the `classes` prop.

## Error Handling

The component automatically displays validation errors if they exist for the given field name.

```php
@error($name)
<span class="invalid-feedback" role="alert">
    <strong>{{ $message }}</strong>
</span>
@enderror
```

## Advanced Usage

### Custom Attributes

You can pass additional HTML attributes to the radio button element:

```php
<x-theme::radio 
    name="agree_terms"
    label="I agree to the terms and conditions"
    value="1"
    data-kt-check="true"
    data-kt-check-target="#kt_some_element"
/>
```

### With a Different Name Format

If you need a different name format for nested forms:

```php
<x-theme::radio 
    name="user[preferences]"
    label="Preferences"
    :options="$preferencesArray"
/>
```
