# Tags Component

A simple Laravel Blade component for creating tag input fields in your admin dashboard, powered by Tagify.

## Features

- Simple tag input with Tagify integration
- Regular and solid background styles
- Error handling integration
- Model binding support

## Installation

The component is already installed as part of the Laravel Skin package. No additional installation steps are required.

## Basic Usage

### Default Style

```php
<x-theme::tags 
    name="tags"
    label="Tags"
    value="tag1, tag2, tag3"
/>
```

### Solid Background Style

```php
<x-theme::tags 
    name="categories"
    label="Categories"
    value="category1, category2, category3"
    :solid="true"
/>
```

## Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | The name attribute for the input. |
| `label` | string | null | The label text for the input. |
| `value` | string | null | Pre-populated tags (comma-separated). |
| `classes` | string | '' | Additional CSS classes for the input. |
| `placeholder` | string | 'Type something...' | Placeholder text for the input. |
| `required` | boolean | false | Whether the field is required. |
| `model` | object | null | Model instance for binding data. |
| `description` | string | null | Description text displayed below the label. |
| `solid` | boolean | false | Whether to use solid background style. |
| `id` | string | auto-generated | Custom ID for the input element. |

## Examples

### Basic Tags Input

```php
<x-theme::tags 
    name="tags"
    label="Tags"
    placeholder="Add tags..."
/>
```

### Tags with Model Binding

```php
<x-theme::tags 
    name="tags"
    label="Tags"
    :model="$post"
    description="Enter tags separated by commas"
/>
```

### Solid Background Style

```php
<x-theme::tags 
    name="categories"
    label="Categories"
    :solid="true"
    placeholder="Add categories..."
/>
```

## Handling Tags in Controllers

```php
public function store(Request $request)
{
    $validated = $request->validate([
        'tags' => 'required|string',
    ]);

    // Convert comma-separated tags to array if needed
    $tags = explode(',', $request->tags);
    
    // Process tags...
}
```
