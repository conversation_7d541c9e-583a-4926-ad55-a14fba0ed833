<?php

namespace Modules\Favorite\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class FavoriteUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'favorite_type' => 'sometimes|max:255|string',
            'favorite_id' => 'sometimes',
            'favoriteable_type' => 'sometimes|max:255|string',
            'favoriteable_id' => 'sometimes'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
