<?php

namespace Modules\Favorite\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FavoriteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $matchedKey = collect(config('favorite.favoriteable'))
            ->filter(fn($item) => $item['model'] === $this->favoriteable_type)
            ->keys()
            ->first();

        $resourceClass = Config("favorite.favoriteable.{$matchedKey}.resource");

        return [
            "id" => $this->id ?? null,
            "favorite" => $this->favorite ?? null,
            "favoriteable" => $resourceClass::make($this->favoriteable),
        ];
    }
}
