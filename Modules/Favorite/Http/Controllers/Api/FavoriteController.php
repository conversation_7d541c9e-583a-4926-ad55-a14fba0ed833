<?php

namespace Modules\Favorite\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Favorite\Http\Requests\Api\FavoriteStoreRequest;
use Modules\Favorite\Http\Requests\Api\FavoriteUpdateRequest;
use Modules\Favorite\Http\Resources\FavoriteResource;
use Modules\Favorite\Services\FavoriteService;

class FavoriteController extends ControllerAbstract
{


    protected string $jsonResourceClass = FavoriteResource::class;
    protected string $storeRequestClass = FavoriteStoreRequest::class;
    protected string $updateRequestClass = FavoriteUpdateRequest::class;


    public function __construct(FavoriteService $service)
    {
        parent::__construct($service);
        $this->filter = [
            'favorite_id'=>Auth::id(),
            'favorite_type'=>Auth::user()::class,
        ];
    }


    public function toggle(FavoriteStoreRequest $request): JsonResponse
    {
        $result = $this->service->toggle($request->validated());

        if($result['state']){
            return ApiResponse::success($result['message']);
        }
        return ApiResponse::error(__('something wrong'));
    }

}
