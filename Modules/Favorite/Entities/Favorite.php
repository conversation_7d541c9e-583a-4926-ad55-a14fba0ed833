<?php

namespace Modules\Favorite\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON>haleds\Shared\Models\BaseModel;

class Favorite extends BaseModel
{
    use HasFactory;

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['favorite_type', 'favorite_id', 'favoriteable_type', 'favoriteable_id', 'created_at', 'updated_at'];


    public function favorite(){
        return $this->morphTo();
    }

    public function favoriteable(){
        return $this->morphTo();
    }
}
