<?php

namespace Modules\Review\Entities;

use Faker\Provider\Base;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Khaleds\Shared\Models\BaseModel;
use Modules\User\app\Models\User;
use Modules\Review\Database\Factories\ReviewFactory;


class Review extends BaseModel
{
    use HasFactory;
    protected $fillable = ['rated_id', 'rated_type', 'user_id', 'is_active', 'rate', 'comment'];

    public function user()
    {
    return $this->belongsTo(User::class , 'user_id');
    }
    public function rateable(): MorphTo
    {
        return $this->morphTo('rateable', 'rated_type', 'rated_id');
    }
    public function rated()
    {
        return $this->morphTo();
    }
    public static function AverageRating($ratedType, $ratedId)
    {
        return self::where('rated_type', $ratedType)
            ->where('rated_id', $ratedId)
            ->avg('rate');
    }



    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    protected static function newFactory()
    {
        return ReviewFactory::new();
    }
    protected $casts = [
        'created_at' => 'datetime',
    ];
}
