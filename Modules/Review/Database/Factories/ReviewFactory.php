<?php

namespace Modules\Review\Database\Factories;
use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\User\app\Models\User;
use Modules\User\Enums\UserRoleEnum;

class ReviewFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = \Modules\Review\Entities\Review::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'rated_type' => "Modules\GeneralEvent\Entities\GeneralEvent",
            'rated_id'   => GeneralEvent::inRandomOrder()->first()?->id ?? 1,
            'user_id'    => User::inRandomOrder()->first()?->id ?? 1,
            'is_active' => $this->faker->boolean(),
            'rate'      => $this->faker->numberBetween(1, 5),
            'comment'   => $this->faker->sentence(),
        ];
    }
}

