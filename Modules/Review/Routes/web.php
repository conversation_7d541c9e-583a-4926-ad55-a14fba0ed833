<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Modules\Review\Http\Controllers\Portal\ReviewController;




Route::middleware(['web', 'auth'])->prefix('reviews')->group(function() {
    Route::get('/', [ReviewController::class, 'index'])->middleware(['auth', 'verified'])->name('reviews.index');
    Route::get('/delete/{id}', [ReviewController::class, 'destroy'])->middleware(['auth', 'verified'])->name('levels.destroy');
});


