<?php

namespace Modules\Review\Http\Controllers\Portal;


use Illuminate\Http\Request;
use Inertia\Inertia;
use Modules\Review\Entities\Review;
use App\Http\Controllers\Controller;
use Modules\Review\Http\Requests\Api\StoreReviewRequest;
use Modules\Review\Http\Requests\Api\UpdateReviewRequest;
use Modules\Review\Http\Resources\Api\ReviewResource;
use Modules\Review\Services\ReviewService;


class ReviewController extends Controller
{

    protected $indexView = "Portal/Organizer/Reviews/Index";

    public function __construct(protected ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }


    public function index(Request $request)
    {
        $reviews = $this->reviewService->paginate(with: ['rateable', 'user']);

        return Inertia::render($this->indexView, [
            'reviews' => $reviews,
            'initialUrlParams' => [
                'filters' => $request->input('filters', []),
                'search' => $request->input('search_key', ''),
            ]
        ]);
    }
    //done worked
    public function store(StoreReviewRequest $request)
    {
        $data = $request->validated();
        $review = $this->reviewService->create($data);
        return response()->json($review, 201);
    }


    public function update(UpdateReviewRequest $request, $id)
    {
        $review = $this->reviewService->findOrFail($id);
        $this->reviewService->update($request->validated(), $review);
        return response()->json($review);
    }

    public function destroy($id)
{

    try {
        $review = $this->reviewService->findOrFail($id);
        $this->reviewService->delete($review);

        return redirect()->route('reviews.index')->with('success', 'review deleted successfully.');
    } catch (\Exception $exception) {
        return redirect()->route('reviews.index')->with('error', 'Failed to delete the review.');
    }
}

}
