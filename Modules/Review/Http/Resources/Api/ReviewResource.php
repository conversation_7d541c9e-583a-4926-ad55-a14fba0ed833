<?php

namespace Modules\Review\Http\Resources\Api;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Http\Resources\Api\GeneralEventResource;
use Modules\User\app\Http\Resources\Api\UserResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        $data = [
            'id' => $this->id,
            'rated_type'=>$this->rated_type,
            'rated_id'=>$this->rated_id,
            'ratedable' => $this->when(
                $this->relationLoaded('rateable'),
                function() {
                    return $this->getRateableResource($this->rated_type, $this->rateable);
                }
            ),
            'rate' => $this->rate,
            'average_rating' => floor(\Modules\Review\Entities\Review::AverageRating($this->rated_type, $this->rated_id) * 10) / 10,
            'is_active' => $this->is_active,
            'user' => UserResource::make($this->user),
            'comment' => $this->comment,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            ];

        return $data;

    }

    public function getRateableResource($rated_type, $rateable)
    {
        if (!$rateable) {
            return null;
        }
        switch ($rated_type) {
            case 'Modules\User\app\Models\User':
                return UserResource::make($rateable);
            case 'Modules\GeneralEvent\Entities\GeneralEvent':
                return GeneralEventResource::make($rateable);
            default:
                return [
                    'id' => $rateable->id,
                    'type' => class_basename($rated_type)
                ];
        }
    }
}
