<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Order\Enums\PaymentStatusEnum;
use Modules\Order\Enums\StatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attendee_id')->constrained('users')->cascadeOnDelete();
            $table->integer('total_count');
            $table->decimal('total_price', 10, 2);
            $table->foreignId('event_id')->constrained()->cascadeOnDelete();
            $table->enum('status', StatusEnum::values())->default(StatusEnum::PENDING->value);
            $table->enum('payment_status', PaymentStatusEnum::values())->default(PaymentStatusEnum::UNPAID->value);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
};
