@extends('theme::admin.layout.app')

@section('title', 'Order Form')

@section('content')
    <div class="card d-flex flex-row-fluid flex-center" xmlns:x-theme="http://www.w3.org/1999/html">
        <form class="card-body py-20 w-100  px-9 fv-plugins-bootstrap5 fv-plugins-framework"
              novalidate="novalidate"
              action="{{ ($method == 'PUT') ? route($action, $model->id) : route($action) }}"
              method="POST"
              id="kt_order_form">
            @csrf
            @method($method)
            <div class="current" data-kt-stepper-element="content">
                <div class="w-100">
                    <div class="pb-10 pb-lg-15">
                        <h2 class="fw-bold d-flex align-items-center text-gray-900">
                            Update Order Form
                            <span class="ms-1" data-bs-toggle="tooltip" title="Order details info">
                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6"></i>
                        </span>
                        </h2>
                        <div class="text-muted fw-semibold fs-6">
                            Please enter order information correctly.
                        </div>
                    </div>

                    <x-theme::input
                        name="attendee_id"
                        label="Attendee"
                        value="{{ old('attendee_id', $model->attendee->first_name . ' ' . $model->attendee->last_name ?? '') }}"
                        disabled
                    />

                    <x-theme::input
                        name="total_count"
                        label="Total Count"
                        value="{{ old('total_count', $model->total_count ?? '_') }}"
                        disabled
                    />

                    <x-theme::input
                        name="total_price"
                        label="Total Price"
                        value="{{ old('total_price', $model->total_price ?? '_') }}"
                        disabled
                    />

                    <x-theme::input
                        name="event_id"
                        label="Event"
                        value="{{ old('event_id', $model->event->name ?? '_') }}"
                        disabled
                    />

                    <x-theme::select
                        name="status"
                        label="Status"
                        :options="['pending' => 'Pending','completed' => 'Completed', 'cancelled' => 'Cancelled']"
                        value="{{ old('status', $model->status ?? '_') }}"
                    />

                    <x-theme::select
                        name="payment_status"
                        label="Payment Status"
                        :options="['unpaid' => 'Unpaid', 'paid' => 'Paid']"
                        value="{{ old('payment_status', $model->payment_status ?? '_') }}"
                    />
                </div>
            </div>

            <div class="d-flex flex-stack pt-10">
                <div class="mr-2">
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-lg btn-light-primary me-3">
                        <i class="ki-duotone ki-arrow-left fs-4 me-1"></i> Back
                    </a>
                </div>

                <div>
                    <button type="submit" class="btn btn-lg btn-primary flex-end">
                        {{ __("Save") }}
                    </button>
                </div>
            </div>
        </form>
    </div>
@endsection
