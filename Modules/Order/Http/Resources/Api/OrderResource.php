<?php

namespace Modules\Order\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\GeneralEvent\Http\Resources\Api\GeneralEventResource;
use Modules\User\app\Http\Resources\Api\UserResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            'attendee' => $this->whenLoaded('attendee', function () {
                return UserResource::make($this->attendee);
            }),
            "total_count" => $this->total_count,
            "total_price" => $this->total_price,
            'event' => $this->whenLoaded('event', function () {
                return GeneralEventResource::make($this->event);
            }),
            'status' => $this->status,
            'payment_status' => $this->payment_status,
            'created' => date($this->created_at),
            'items' => OrderItemResource::collection($this->items),
        ];

        return $data;
    }
}
