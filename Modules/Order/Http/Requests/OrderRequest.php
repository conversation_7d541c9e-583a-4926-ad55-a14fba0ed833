<?php

namespace Modules\Order\Http\Requests;
use Illuminate\Foundation\Http\FormRequest;

class OrderRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'order_items'           => 'required|array|min:1',
            'order_items.*.ticket_level_id' => [
                'required',
                'exists:ticket_level,id',
                function ($attribute, $value, $fail) {
                    $ticketLevel = \Modules\GeneralEvent\Entities\TicketLevel::find($value);
                    if ($ticketLevel && $ticketLevel->count <= 0) {
                        $fail(__("No tickets available for this ticket level."));
                    }
                }
            ],
            'order_items.*.note'     => 'nullable|string|max:255',
        ];
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        return array_merge($validated, [
            'user_id' => auth()->user()->id,
        ]);
    }
}
