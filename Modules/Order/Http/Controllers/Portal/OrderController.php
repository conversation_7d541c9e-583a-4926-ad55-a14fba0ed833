<?php
namespace Modules\Order\Http\Controllers\Portal;

use Illuminate\Http\Request;
use Khaleds\Shared\Http\Controllers\Inertia\ControllerAbstract;
use Modules\Order\Services\OrderService;
use Inertia\Inertia;
use Inertia\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

class OrderController extends ControllerAbstract
{

    protected $indexView = "Portal/Organizer/Tickets/Index";

    protected $redirectRoute = "ticket";

    protected $resourceName = "tickets";


    public function __construct(OrderService $service)
    {
        parent::__construct($service);
    }


    public function indexOrder(Request $request): \Inertia\Response
    {
        $tickets = $this->service->getTicketsAndOrders();
        return Inertia::render($this->indexView, [
            $this->resourceName => $tickets,
            'initialUrlParams' => [
                'filters' => $request->input('filters', []),
                'search' => $request->input('search_key', ''),
            ]
        ]);
    }

    public function exportCsv()
    {
        if (ob_get_length()) {
            ob_end_clean();
        }

        $tickets = $this->service->getAllTicketsAndOrders()->map(function ($ticket) {
            return [
                'id' => $ticket->ticket_id ?? '',
                'attendee_name' => $ticket->attendee_name ?? '',
                'ticket_name' => $ticket->ticket_name ?? '',
                'ticket_status' => $ticket->ticket_status ?? '',
                'level_name' => $ticket->level_name ?? '',
                'price' => $ticket->price ?? '',
                'event_name' => $ticket->event_name ?? '',
                'created_at' => $ticket->ordered_at ?? '',
            ];
        });

        return response()->streamDownload(function () use ($tickets) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, ['ID', 'Event ID', 'Name', 'Price', 'Created At']);

            foreach ($tickets as $ticket) {
                fputcsv($handle, $ticket);
            }

            fclose($handle);
        }, 'tickets.csv');
    }

}
