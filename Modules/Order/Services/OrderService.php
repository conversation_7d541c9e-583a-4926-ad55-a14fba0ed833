<?php
namespace Modules\Order\Services;

use Illuminate\Database\Eloquent\Model;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\GeneralEvent\Entities\TicketLevel;
use Modules\Order\Enums\StatusEnum;
use Modules\Order\Jobs\NotifyOrderConfirmation;
use Modules\GeneralEvent\Repositories\TicketLevelRepository;
use Modules\Order\Repositories\OrderRepository;

class OrderService extends ServiceAbstract
{
    public function __construct(OrderRepository $repository, protected TicketLevelRepository $ticketLevelRepository)
    {
        parent::__construct($repository);
        $this->ticketLevelRepository = $ticketLevelRepository;
    }

    public function create(array $data)
    {
        $orderItems = $this->prepareOrderItems($data['order_items']);
        $orderData = $this->prepareOrderData($orderItems , $data['user_id']);

        $data =
        [
            'order' => $orderData,
            'items' => $orderItems,
        ];

        $order = $this->repository->create($data);
        //notify order
        $this->notifyOrderConfirmation($order);
        return $order;
    }

    protected function notifyOrderConfirmation(Model $model): void
    {
        NotifyOrderConfirmation::dispatch($model);
    }

    private function prepareOrderItems(array $items): array
    {
        $ticketLevelIds = array_column($items, 'ticket_level_id');

        $ticketLevels = $this->ticketLevelRepository->getModel()
            ->whereIn('id', $ticketLevelIds)
            ->with(['ticket.event', 'eventLevel.level'])
            ->get()
            ->keyBy('id');

        return array_map(function ($item) use ($ticketLevels) {
            $ticketLevel = $ticketLevels[$item['ticket_level_id']];
            $requestedCount = $item['count'] ?? 1;

            $this->validateAndUpdateTicketLevel($ticketLevel, $requestedCount);

            return [
                'ticket_level_id' => $ticketLevel->id,
                'count'           => $requestedCount,
                'price'          => $ticketLevel->price,
                'ticket_id'      => $ticketLevel->ticket_id,
                'level_id'       => $ticketLevel->eventLevel->level->id,
                'note'           => $item['note'] ?? null,
                'event_id'       => $ticketLevel->ticket->event->id,
            ];
        }, $items);
    }

    private function validateAndUpdateTicketLevel(TicketLevel $ticketLevel, int $requestedCount): void
    {
        $ticketLevel->count -= $requestedCount;
        $ticketLevel->save();
    }

    private function prepareOrderData(array $orderItems , int $userId): array
    {
        $totalCount = array_sum(array_column($orderItems, 'count'));
        $totalPrice = array_sum(array_map(function($item) {
            return $item['count'] * $item['price'];
        }, $orderItems));

        return [
            'attendee_id' => $userId,
            'total_count' => $totalCount,
            'total_price' => $totalPrice,
            'event_id'    => $orderItems[0]['event_id'],
            'level_id'    => $orderItems[0]['level_id'],
        ];
    }

    public function getTicketsAndOrders()
    {
        return $this->repository->getTicketPurchases();
    }

    public function getAllTicketsAndOrders()
    {
        return $this->repository->getTicketPurchases(true);
    }

    public function refund(Model $model)
    {
        return $this->repository->update($model,['status'=>StatusEnum::REFUND]);
    }
}
