<?php
namespace Modules\Order\Repositories;

use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\Order\Entities\Order;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderRepository extends RepositoriesAbstract
{
    public function __construct(Order $model)
    {
        parent::__construct($model);
    }

    public function create(array $data): Order
    {
        $order = $this->model->create($data['order']);

        if (!empty($data['items'])) {
            $items = collect($data['items'])->map(function ($item) use ($order) {
                return array_merge($item, ['order_id' => $order->id]);
            })->toArray();

            $order->items()->insert($items);
        }
        return $this->model->with('items')->find($order->id);
    }

    public function getTicketPurchases(bool $allData=false)
    {
        $query =  DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('users', 'orders.attendee_id', '=', 'users.id')
            ->join('events', 'order_items.event_id', '=', 'events.id')
            ->join('levels', 'order_items.level_id', '=', 'levels.id')
            ->join('tickets', 'order_items.ticket_id', '=', 'tickets.id')
            ->select('order_items.id as ticket_id',
                'order_items.created_at as ordered_at',
                'order_items.status as ticket_status',
                'order_items.price as price',
                'levels.name as level_name',
                'events.name as event_name',
                'tickets.name as ticket_name',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name) as attendee_name")
            );
        $query = $this->applyFilters($query);
        $query = $query->where('events.organizer_id', '=', Auth::id());
        $query = $this->applyOrdering($query);
        if($allData){
            return $query->get();
        }
        return $query->paginate(request()->limit ?? 10);
    }
    protected function applyFilters(Builder $query): Builder
    {
        //handle search key
        if (request()->has('search_key') && strlen(request()->get('search_key')) > 2 ) {
            $searchTerm = '%' . strtolower(request()->get('search_key')) . '%'; // Add wildcards and convert to lowercase
            $query->where(function ($q) use ($searchTerm) {
                $q->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(events.name, "$.en"))) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(events.name, "$.ar"))) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(levels.name, "$.en"))) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(levels.name, "$.ar"))) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(CONCAT(users.first_name, " ", users.last_name)) LIKE ?', [$searchTerm]);
            });
        }
        //handle filters
        if(request()->has('filters'))
        {
            $filters = request()->get('filters');
            if (!empty($filters['status'])) {
                $query->where('order_items.status', $filters['status']);
            }
            if (!empty($filters['id'])) {
                $query->where('order_items.id', $filters['id']);
            }
            if (!empty($filters['min_price'])) {
                $query->where('order_items.price', '>=', $filters['min_price']);
            }
            if (!empty($filters['max_price'])) {
                $query->where('order_items.price', '<=', $filters['max_price']);
            }
            if (!empty($filters['from'])) {
                $query->whereDate('order_items.created_at', '>=', $filters['from']);
            }
            if (!empty($filters['to'])) {
                $query->whereDate('order_items.created_at', '<=', $filters['to']);
            }
            if (!empty($filters['ticket_name'])) {
                $query->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(tickets.name, "$.en"))) LIKE ?', ["{$filters['ticket_name']}"])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(tickets.name, "$.ar"))) LIKE ?', ["{$filters['ticket_name']}"]);
            }
            if (!empty($filters['level_name'])) {
                $query->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(levels.name, "$.en"))) LIKE ?', ["{$filters['level_name']}"])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(levels.name, "$.ar"))) LIKE ?', ["{$filters['level_name']}"]);
            }
            if (!empty($filters['event_name'])) {
                $query->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(events.name, "$.en"))) LIKE ?', ["{$filters['event_name']}"])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(events.name, "$.ar"))) LIKE ?', ["{$filters['event_name']}"]);
            }
            if (!empty($filters['attendee_name'])) {
                $searchTerm = strtolower($filters['attendee_name']); // Convert search term to lowercase
                $query->whereRaw('LOWER(CONCAT(users.first_name, " ", users.last_name)) LIKE ?', ["%{$searchTerm}%"]);
            }
        }
        return $query;
    }
    protected function applyOrdering(Builder $query): Builder
    {
        $orderBy = request()->get('order_by', 'ordered_at'); // Default column to order by
        $orderDirection = request()->get('order_direction', 'desc'); // Default order direction
        // Validate order direction to prevent SQL injection
        $orderDirection = in_array(strtolower($orderDirection), ['asc', 'desc']) ? $orderDirection : 'desc';
        // Map request columns to actual database columns
        $allowedColumns = [
            'ticket_id' => 'order_items.id',
            'ordered_at' => 'order_items.created_at',
            'ticket_status' => 'order_items.status',
            'price' => 'order_items.price',
            'level_name' => 'levels.name',
            'event_name' => 'events.name',
            'ticket_name' => 'tickets.name',
            'attendee_name' => DB::raw("CONCAT(users.first_name, ' ', users.last_name)"),
        ];
        // Apply ordering if the column is allowed
        if (array_key_exists($orderBy, $allowedColumns)) {
            $query->orderBy($allowedColumns[$orderBy], $orderDirection);
        }
        return $query;
    }
}
