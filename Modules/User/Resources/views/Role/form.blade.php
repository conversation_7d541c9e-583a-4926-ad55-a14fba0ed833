@extends('theme::admin.layout.app')

@section('title', 'Roles Form')



@section('content')

    <div class="card d-flex flex-row-fluid flex-center">
        <!--begin::Form-->
        <form class="card-body py-20 w-100  px-9 fv-plugins-bootstrap5 fv-plugins-framework"
              novalidate="novalidate"
              action="{{ ($method == "PUT") ? route($action,$model->id): route($action) }}"
              method="POST"
              id="kt_create_account_form">
            @csrf
            @method($method)
            <!--begin::Step 1-->
            <div class="current" data-kt-stepper-element="content">
                <!--begin::Wrapper-->
                <div class="w-100">


                    <x-theme::input
                        name="name"
                        label="Unique name for the role"
                        :model="$model ?? null"
                    required/>
                    <x-theme::translation-input names="translated_name" required :model="$model??null" />

                    <div class="fv-row w-100 flex-md-root fv-plugins-icon-container">
                        <!--begin::Label-->
                        <label class="required form-label">Role Gateway</label>
                        <!--end::Label-->

                        <select class="form-select" name="guard_name" data-control="select2" data-placeholder="Select an option">
                            <option selected value="web" data-select2-id="select2-data-144-i64s">admin board</option>
                            <option value="api" data-select2-id="select2-data-145-2oai">mobile users</option>
                        </select>


                        <!--begin::Description-->
                        <div class="text-muted fs-7">Choose the gateway for this roles users.</div>
                        <!--end::Description-->
                    </div>

                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Step 1-->

            <!--begin::Actions-->
            <x-theme::actions
                :showBackButton="true"
                :backUrl="route('admin.roles.index')"
                :submitText="__('Save')"
            />
            <!--end::Actions-->
        </form>
        <!--end::Form-->
    </div>


@endsection
