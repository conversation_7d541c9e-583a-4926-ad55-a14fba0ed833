<?php

namespace Modules\User\Events;

use Illuminate\Queue\SerializesModels;
use Modules\Accounts\Entities\Account;
use Modules\User\app\Models\User;

class UserRegistered
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(
        public User $model
    )
    {
        //
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
