<?php

use Carbon\Carbon;
use Illuminate\Config\Repository;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Modules\User\Enums\UserRoleEnum;

/**
 * Class FillDefaultAdminUserAndPermissions
 */
class FillDefaultAdminUserAndPermissions extends Migration
{
    /**
     * @var Repository|mixed
     */
    protected $guardName = 'admin';
    /**
     * @var mixed
     */
    protected $userClassName = \Modules\User\app\Models\User::class;
    /**
     * @var
     */
    protected $userTable = "users";

    /**
     * @var array
     */
    protected $permissions;
    /**
     * @var array
     */
    protected $roles;
    /**
     * @var array
     */
    protected $users;

    /**
     * @var string
     */
    protected $password = '1';

    /**
     * FillDefaultAdminUserAndPermissions constructor.
     */
    public function __construct()
    {

        $defaultPermissions = collect([

            'admin',
            "admin.lang",
            "admin.profile.edit",
            "admin.profile.update",
            "admin.profile.password",
            "admin.verification.notice",
            "admin.verification.verify",
            "admin.password.confirm",
            "admin.password.confirm.post",
            "admin.password.update",
            "admin.logout",

        ]);

        //Add new permissions
        $this->permissions = $defaultPermissions->map(function ($permission) {
            return [
                'name' => $permission,
                'guard_name' => $this->guardName,
            ];
        })->toArray();

        //Add new roles
        $this->roles = [
            [
                'name' => UserRoleEnum::ADMIN->value,
                'translated_name' => json_encode(['en' => 'Admin', 'ar' => 'المسؤل العام']),
                'guard_name' => $this->guardName,
            ],
        ];

        //Add new users
        $this->users = [

            "user" => [
                'first_name' => 'Khaled',
                'last_name' => 'Abodaif',
                'email' => '<EMAIL>',
                'password' => Hash::make($this->password),
                'remember_token' => null,
                'default_role' => \Modules\User\Models\Role::firstWhere(['name'=>'admin'])->id,
            ],
            'roles' =>
                [
                    'name' => 'admin',
                    'guard_name' => $this->guardName,
                ],

            'permissions' => $this->permissions

        ];
    }

    /**
     * Run the migrations.
     *
     * @return void
     * @throws Exception
     */
    public function up(): void
    {

        $role = \Spatie\Permission\Models\Role::firstOrCreate(
            ['name' => $this->users['roles']['name']],
            ['guard_name' => $this->users['roles']['guard_name'],
                'translated_name' => $this->roles[0]['translated_name'],
            ]
        );

        $user = \Modules\User\app\Models\User::updateOrCreate(['email'=>'<EMAIL>'],$this->users['user']);

        $user->assignRole($role);

        // Assign permissions to the user
        foreach ($this->users['permissions'] as $permissionData) {
            // Ensure the permission exists
            $permission = \Spatie\Permission\Models\Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'guard_name' => $permissionData['guard_name'],
                ]
            );

            // Assign the permission to the user
            $role->givePermissionTo($permission);
        }

        app()['cache']->forget(config('permission.cache.key'));
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     * @throws Exception
     */
    public function down(): void
    {

    }
}
