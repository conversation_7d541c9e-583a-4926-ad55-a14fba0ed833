<?php

namespace Modules\User\Database\seeders;

use Illuminate\Database\Seeder;
use Modules\User\Enums\UserRoleEnum;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => UserRoleEnum::ATTENDEE->value,
                'translated_name' => json_encode(UserRoleEnum::ATTENDEE->getTranslations()),
                'guard_name' => 'api',
            ],
            [
                'name' => UserRoleEnum::SPEAKER->value,
                'translated_name' => json_encode(UserRoleEnum::SPEAKER->getTranslations()),
                'guard_name' => 'web',
            ],
            [
                'name' => UserRoleEnum::ORGANIZER->value,
                'translated_name' => json_encode(UserRoleEnum::ORGANIZER->getTranslations()),
                'guard_name' => 'web',
            ],
            [
                'name' => UserRoleEnum::ADMIN->value,
                'translated_name' => json_encode(UserRoleEnum::ADMIN->getTranslations()),
                'guard_name' => 'admin',
            ],
        ];

        $existingRoles = Role::whereIn('name', array_column($roles, 'name'))->pluck('name')->toArray();

        $newRoles = array_filter($roles, fn($role) => !in_array($role['name'], $existingRoles));

        if (!empty($newRoles)) {
            Role::insert($newRoles);
        }
    }
}
