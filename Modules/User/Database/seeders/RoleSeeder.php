<?php

namespace Modules\User\Database\seeders;

use Illuminate\Database\Seeder;
use Modules\User\Enums\UserRoleEnum;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => UserRoleEnum::ATTENDEE->value,
                'translated_name' => json_encode(UserRoleEnum::ATTENDEE->getTranslations()),
                'guard_name' => 'web',
            ],
            [
                'name' => UserRoleEnum::SPEAKER->value,
                'translated_name' => json_encode(UserRoleEnum::SPEAKER->getTranslations()),
                'guard_name' => 'web',
            ],
            [
                'name' => UserRoleEnum::ORGANIZER->value,
                'translated_name' => json_encode(UserRoleEnum::ORGANIZER->getTranslations()),
                'guard_name' => 'web',
            ],
            [
                'name' => UserRoleEnum::ADMIN->value,
                'translated_name' => json_encode(UserRoleEnum::ADMIN->getTranslations()),
                'guard_name' => 'admin',
            ],
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(
                [
                    'name' => $role['name'],
                    'guard_name' => $role['guard_name']
                ],
                [
                    'translated_name' => $role['translated_name']
                ]
            );
        }

    }
}
