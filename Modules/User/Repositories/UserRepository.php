<?php

namespace Modules\User\Repositories;

use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\User\Models\BaseUser;
use Modules\User\app\Models\User;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class UserRepository extends RepositoriesAbstract
{
    protected \Illuminate\Database\Eloquent\Builder|\Khaleds\Shared\Models\BaseModel $model;

    public function __construct(BaseUser $model)
    {
        $this->model = $model;
        parent::__construct($model);

    }

    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    public function getAllByQuery(array $condition=[], array $with = [], array $select = ['*'])
    {
        return $this->model->where($condition)->select($select);
    }

    public function getFirstBy(array $condition = [], array $select = ['*'], array $with = []): Model|NULL
    {
        $this->model = User::query();
        return $this->model->with($with)->where($condition)->select($select)->first();
    }

    public function findOrFail(int $id ,array $condition = [], array $select = ['*'], array $with = []): Model
    {
        return $this->model->with($with)->select($select)->where($condition)->findOrFail($id);
    }

    public function addOrganizerMedia(User|BaseUser $user, $request): void
    {
        // Add company logo
        if ($request->hasFile('company_logo')) {
            $user->addMediaFromRequest('company_logo')
                ->toMediaCollection('company_logos');
        }

        // Add exhibition document
        if ($request->hasFile('establishment_doc')) {
            $user->addMediaFromRequest('establishment_doc')
                ->toMediaCollection('establishment_documents');
        }

        // Add previous works
        if ($request->hasFile('portfolio')) {
            $user->addMediaFromRequest('portfolio')
                ->toMediaCollection('portfolio');
        }
    }

    public function addSpeakerMedia(User $user, $request): void
    {
        // Add profile image
        if ($request->hasFile('profile_pic')) {
            $user->addMediaFromRequest('profile_pic')
                ->toMediaCollection('profile_pic');
        }

        // Add certificates
        if ($request->hasFile('experience_certificates')) {
            $user->addMediaFromRequest('experience_certificates')
                ->toMediaCollection('experience_certificates');
        }

        // Add CV
        if ($request->hasFile('cv')) {
            $user->addMediaFromRequest('cv')
                ->toMediaCollection('cv');
        }
    }

    public function assignRole(User|BaseUser $user, string $roleName): void
    {
        \Log::info('UserRepository::assignRole called', ['user_id' => $user->id, 'role_name' => $roleName]);

        // Determine the correct guard based on role (same logic as admin migration)
        $guardName = match($roleName) {
            'admin' => 'admin',
            'organizer', 'speaker' => 'web',
            'attendee' => 'web', // Use 'web' for web routes (API users can also access web)
            default => 'web'
        };

        \Log::info('Guard determined', ['guard_name' => $guardName]);

        // Use Spatie Role model directly (same as admin migration)
        $role = \Spatie\Permission\Models\Role::firstOrCreate(
            ['name' => $roleName, 'guard_name' => $guardName],
            [
                'translated_name' => json_encode([
                    'en' => ucfirst($roleName),
                    'ar' => ucfirst($roleName)
                ])
            ]
        );

        \Log::info('Role found/created', ['role_id' => $role->id, 'role_name' => $role->name, 'guard_name' => $role->guard_name]);

        // Assign the role object (same as admin migration)
        $user->assignRole($role);
        \Log::info('Role assigned to user', ['user_id' => $user->id, 'role_id' => $role->id]);

        // Update the default_role field
        $user->update(['default_role' => $role->id]);
        \Log::info('Default role updated', ['user_id' => $user->id, 'default_role' => $role->id]);

        // Clear permission cache (same as admin migration)
        app()['cache']->forget(config('permission.cache.key'));
        \Log::info('Permission cache cleared');
    }

    public function update( Model $model , array $data): bool
    {
        return $model->update($data);
    }

    public function createToken(User $user, string $tokenName = 'API TOKEN'): string
    {
        return $user->createToken($tokenName)->plainTextToken;
    }

    public function updateProfile(User $user, array $data, string $collectionName)
    {
        try {
            if (array_key_exists('image', $data)) {
                $user->clearMediaCollection($collectionName);
                $user->addMediaFromRequest('image')->toMediaCollection($collectionName);
                unset($data['image']);
            }
            return $user->update($data);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function changeEmail(User $user, array $data)
    {
        try {
            $data['email'] = $data['new_email'];
            return $user->update($data);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function changeLang($locale)
    {
        if (!in_array($locale, ['en', 'ar'])) {
            abort(400);
        }

        session(['locale' => $locale]);
        auth()->user()->update(['lang'=>$locale]);
        app()->setLocale($locale);

        return true;
    }
}
