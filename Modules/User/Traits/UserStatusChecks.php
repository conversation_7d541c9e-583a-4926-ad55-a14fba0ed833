<?php

namespace Modules\User\Traits;
use Modules\User\Enums\UserStatus;

trait UserStatusChecks
{
    /**
     * Check if user is verified
     *
     * @return bool
     */
    public function isVerified(): bool
    {
        return $this->email_verified_at !== null;
    }

    /**
     * Check if user is unverified
     *
     * @return bool
     */
    public function isUnverified(): bool
    {
        return !$this->isVerified();
    }

    /**
     * Check if user is suspended
     *
     * @return bool
     */
    public function isSuspended(): bool
    {
        return $this->is_suspended == true;
    }
    
    /**
     * Check if user is active
     *
     * @return bool
     */
    public function isActiveStatus(): bool
    {
        return $this->status == UserStatus::ACTIVE->value;
    }

    /**
     * Check if user is active (verified and not suspended)
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->isVerified() && !$this->isSuspended() && $this->isActiveStatus();
    }

    /**
     * Get status message based on user state
     *
     * @return string|null
     */
    public function getStatusMessage(): ?string
    {
        if ($this->isUnverified()) {
            return 'Your account is not verified yet';
        }

        if ($this->isSuspended()) {
            return 'Your account is suspended';
        }

        return null;
    }
}