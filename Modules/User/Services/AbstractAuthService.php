<?php

namespace Modules\User\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Request;
use Modules\User\Traits\OtpTrait;
use Modules\User\Repositories\UserRepository;
use Spatie\Permission\Models\Role;

abstract class AbstractAuthService
{
    use OtpTrait;
    public Collection $data;
    public $model;
    public string $message;
    public int $code;

    public function __construct(protected UserRepository $repository ,protected OtpService $otpService){
    }

    abstract public function register(Collection $data): bool;

    public function login(mixed $data): bool
    {
        $credentials = $data->only(['email', 'password'])->toArray();

        if (!Auth::attempt($credentials)) {
            $this->message = 'Email & Password do not match our records.';
            return false;
        }


        $user = Auth::user();


        if (!$user->isActive() || $user->isSuspended()) {
            $this->message = $user->getStatusMessage();
            $this->code = 401;
            return false;
        }
    
        $user->token = $this->repository->createToken($user);
        $this->model = $user;

        return true;
    }

    public function checkOtp(Collection $data)
    {
        $user = $this->repository->getFirstBy([
            'email' => $data->get('email'),
            'otp_code' => $data->get('otp_code')
        ]);

        return $user ?? null;
    }

    public function activateOtp(Collection $data): bool
    {
        $user = $this->checkOtp($data);

        if ($user) {
            $this->repository->update($user, [
                'otp_code' => null,
                'email_verified_at' => now(),
            ]);
            $user->token = $this->repository->createToken($user);
            $this->model = $user;
            return true;
        }
        return false;
    }

    public function sendOtp(Collection $data): string
    {
        $otp = $this->generateOtp();

        $user = $this->repository->getFirstBy(['email' => $data->only(['email'])]);

        if ($user) {
            $this->repository->update($user, [
                'otp_code' => $otp,
                'email_verified_at' => now(),
            ]);
            $this->model = $user;
            return true;
        }
        return false;
    }

    public function resetPassword(Collection $data): bool
    {
        $user = $this->checkOtp($data);

        if ($user) {
            $data['otp_code'] = null;
            return $this->repository->update($user , $data->only(['otp_code', 'password'])->toArray());
        }
        return false;
    }

    public function changePassword(array $data)
    {
        if (!Hash::check($data['old_password'], $this->model->password)) {
            return false;
        }

        $this->model->update([
            'password' => $data['password']
        ]);
        return true;
    }

    public function asignRole($role, $guard , $user)
    {
        $role = Role::findByName($role , $guard);
        $user->assignRole($role);
    }

    public function updateProfile(array $data, Model $user)
    {
        $user->update($data);
        if (array_key_exists('image' , $data)) {
            $user->clearMediaCollection('users');
            $user->addMediaFromRequest('image')->toMediaCollection('users');
        }

        return $user;
    }

    public function logout()
    {
        if (auth()->check()) {
            auth()->user()->currentAccessToken()->delete();
        }

        return true;
    }

    public function deleteProfileImage($user)
    {
        $user->clearMediaCollection('images');

        return $user;
    }

    public function deleteAccount($user, ?string $closeReason = null)
    {
        $user->close_reason = $closeReason;
        $user->save();
        $user->delete();
        return true;
    }

    public function changeEmail(Collection $data, Model $user): bool
    {
        // Check if the provided email is different from the current email
        if ($user->email === $data->get('email')) {
            return false;
        }

        // Check if the new email is already taken
        if ($this->repository->getFirstBy(['email' => $data->get('email')])) {
            return false;
        }

        // Update the user's email and reset email verification status
        $this->repository->update($user, [
            'email' => $data->get('email'),
            'email_verified_at' => null,
            'otp_code' => $this->generateOtp(),
        ]);

        // TODO notify the user on the new mail
        // $user->notify(new EmailVerificationNotification());

        return true;
    }

    public function checkPassword(string $password): bool
    {
        $this->model = auth()->user();
        return Hash::check($password, $this->model->password);
    }

    public function generateValidPassword(): string
    {
        $uppercase = chr(rand(65, 90)); // A-Z
        $lowercase = chr(rand(97, 122)); // a-z
        $number = chr(rand(48, 57)); // 0-9
        $remaining = str()->random(rand(5, 13)); // Random remaining characters

        // Shuffle and return a valid password
        return str_shuffle($uppercase . $lowercase . $number . $remaining);
    }
}
