<?php

namespace Modules\User\Services;

use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;
use Modules\User\app\Http\Requests\Api\LoginRequest;
use Modules\User\app\Models\User;
use Modules\User\app\Models\PasswordResetToken;
use Modules\User\Repositories\UserRepository;
use Modules\User\Enums\UserRoleEnum;
use Modules\User\Enums\UserStatus;
use Modules\User\Repositories\UserMetaRepository;
use Spatie\Permission\Models\Role;
use stdClass;
use Illuminate\Support\Str;
use Tests\Feature\Auth\PasswordResetTest;
use Modules\User\Notifications\WelcomeNotification;
use Illuminate\Support\Facades\Notification;

class AuthPortalService extends AbstractAuthService
{
    public string $message;
    public Collection $data;

    public function __construct(private UserRepository $userRepository , private UserMetaRepository $userMetaRepository , OtpService $otpService)
    {
        parent::__construct($userRepository, $otpService);
    }

    public function register(mixed $request): bool
    {
        try {
            DB::beginTransaction();

            // Check if a soft-deleted user exists with this email and force delete it
            $deletedUser = User::onlyTrashed()->where('email', $request->email)->first();
            if ($deletedUser) {
                $deletedUser->forceDelete();
            }

            // Create the base user using repository
            $userData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'status' => UserStatus::PENDING->value,
            ];

            // Add phone and country_code only if they exist (for organizer/speaker)
            if (isset($request->country_code)) {
                $userData['country_code'] = $request->country_code;
            }
            if (isset($request->phone)) {
                $userData['phone'] = $request->phone;
            }

            $user = $this->userRepository->create($userData);
            // Handle meta data and role assignment based on user type
            if ($request->account === UserRoleEnum::ORGANIZER->value) {
                $this->userRepository->assignRole($user, UserRoleEnum::ORGANIZER->value);
                $metaData = $this->mapUserData($request , ['first_name' , 'last_name' , 'email' , 'country_code', 'phone' , 'password' , 'password_confirmation' , 'account']);

                $this->createOrganizerMeta($user, $metaData);
            } elseif ($request->account === UserRoleEnum::SPEAKER->value) {
                $this->userRepository->assignRole($user, UserRoleEnum::SPEAKER->value);
                $metaData = $this->mapUserData($request , ['first_name' , 'last_name' , 'email' ,'country_code', 'phone' , 'password' , 'password_confirmation' , 'account']);
                $this->createSpeakerMeta($user, $metaData);
            } elseif ($request->account === UserRoleEnum::ATTENDEE->value) {
                $this->userRepository->assignRole($user, UserRoleEnum::ATTENDEE->value);
                // Attendees don't need additional meta data, just the basic user info
            }

            DB::commit();
            //fire event user registered
//            event(new Registered(User::find($user->id)));
            Notification::send(User::find($user->id), new WelcomeNotification());
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->message = $e->getMessage();
            return false;
        }
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    private function mapUserData(Request $request, array $excludedFields = []): Request
    {
        $filteredRequest = $request->duplicate();

        $filteredRequest->replace($request->except($excludedFields));
        return $filteredRequest;
    }


    private function createOrganizerMeta($user, $request): void
    {
        $this->userRepository->addOrganizerMedia($user, $request);

        // Create meta entries using repository
        $metaData = [
            ['key' => 'company_name', 'value' => $request['company_name']],
            ['key' => 'website', 'value' => $request['website']],
            ['key' => 'commercial_number', 'value' => $request['commercial_number']],
            ['key' => 'tax_number', 'value' => $request['tax_number']],
            ['key' => 'about_company', 'value' => $request['about_company']],
        ];

        $this->userMetaRepository->createMeta($user, $metaData);
    }

    private function createSpeakerMeta($user, $request): void
    {
        $this->userRepository->addSpeakerMedia($user, $request);

        // Create meta entries using repository
        $metaData = [
            ['key' => 'display_name', 'value' => $request['display_name']],
            ['key' => 'website', 'value' => $request['website']],
            ['key' => 'about_you', 'value' => $request['about_you']],
        ];

        $this->userMetaRepository->createMeta($user, $metaData);
    }

        /**
     * Get login view data.
     */
    public function getLoginViewData(): array
    {
        return [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ];
    }

        /**
     * Handle user login.
     */
    public function loginPortal(LoginRequest $request): bool
    {
        try {
            $request->authenticate();
            $request->session()->regenerate();

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }


        /**
     * Handle user logout.
     */
    public function logoutPortal(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function getUserByEmail($email)
    {
        try {
            // First get the speaker role ID
            $speakerRoleId = Role::where('name', UserRoleEnum::SPEAKER->value)->value('id');

            // Then get user with that default_role
            $user = $this->userRepository->getFirstBy(
                [
                    'email' => $email,
                    'default_role' => $speakerRoleId
                ],
                ['id', 'first_name', 'last_name', 'email', 'phone']
            );

            if (!$user) {
                $this->message = 'No speaker found with this email';
                return null;
            }

            return $user;

        } catch (\Exception $e) {
            $this->message = 'Error finding speaker';
            return null;
        }
    }
    public function quickAddSpeaker(array $data): array
    {
        try {
            DB::beginTransaction();

            // Create the base user
            $user = $this->userRepository->create([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'password' => Hash::make(Str::random(10)),
                'email_verified_at' => now(),
            ]);

            // Get or create the speaker role
            $role = Role::firstOrCreate(
                ['name' => 'speaker'],
                ['guard_name' => 'web']
            );

            // Assign speaker role and set default_role
            $user->assignRole($role);
            $user->update(['default_role' => $role->id]);

            // Create basic speaker meta
            $metaData = [
                ['key' => 'display_name', 'value' => $data['first_name'] . ' ' . $data['last_name']],
                ['key' => 'about_you', 'value' => ''],
                ['key' => 'website', 'value' => ''],
            ];

            $this->userMetaRepository->createMeta($user, $metaData);

            DB::commit();

            return [
                'success' => true,
                'user' => $user->only(['id', 'first_name', 'last_name', 'email', 'phone'])
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function checkResetPasswordTokenOfProvidedEmail($email, $token): bool
    {
        $tokenObject = PasswordResetToken::where(['email' => $email])->first();
        if ($tokenObject && Hash::check($token, $tokenObject->token)) {
            if ($tokenObject->created_at->toDateTimeString() > now()->subMinutes(config('auth.passwords.users.expire'))->toDateTimeString()) {
                return true;
            }
        }
        return false;
    }

    public function invalidateToken( $email,  $token): void
    {
        $tokenObject = PasswordResetToken::where('email', $email)->first();
        if ($tokenObject) {
            $tokenObject->delete(); // This will remove from the database
        }
    }

}
