<?php

namespace Modules\User\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Modules\User\app\Models\User;
use Modules\User\Enums\UserRoleEnum;
use Modules\User\Events\UserRegistered;
use Modules\User\Mail\OtpNotification;
use Modules\User\Repositories\UserRepository;
use Modules\User\Traits\otpTrait;
use Spatie\Permission\Models\Role;

class AuthApiService extends AbstractAuthService
{
    use otpTrait;
    public string $message;
    public $model;

    public function __construct(public UserRepository $repository){
    }

    public function register(Collection $data): bool
    {
        try {
            $otp = $this->generateOtp();
            $data->put('otp_code', $otp);

            // Check if a soft-deleted user exists with this email and force delete it
            $deletedUser = User::onlyTrashed()->where('email', $data->get('email'))->first();
            if ($deletedUser) {
                $deletedUser->forceDelete();
            }

            DB::beginTransaction();

            $user = $this->repository->create($data->toArray());

            // Get the role ID for ATTENDEE
            $roleId = Role::where('name', UserRoleEnum::ATTENDEE->value)->value('id');

            // Assign role
            $this->asignRole(UserRoleEnum::ATTENDEE->value, 'api', $user);
            $this->repository->update($user, ['default_role' => $roleId]);
            $user = User::find($user->id);
            $user->token = $this->repository->createToken($user, "API TOKEN");

            // Send OTP via email with registration type
            try {
                Mail::to($user->email)->send(new OtpNotification($user, $otp, 'registration'));
            } catch (\Exception $e) {
                // Log the error but continue with registration
                Log::error('Failed to send registration OTP email: ' . $e->getMessage());
            }

            event(new UserRegistered($user));

            $this->model = $user;

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->message = $e->getMessage();
            return false;
        }
    }
}
