<?php

namespace Modules\User\Services;

use Modules\User\Enums\UserRoleEnum;
use Modules\User\Repositories\UserRepository;

class ProfileService
{
    public function __construct(protected UserRepository $repository){
    }

    public function getPortalUser(int $id)
    {
        $user = $this->repository->getFirstBy(['id' => $id], ['*'], ['meta']);
        if (!$user || $user->hasRole([UserRoleEnum::ATTENDEE->value])) {
            throw new \Exception(__('Undefined user with given id'));
        }

        return $user;
    }
}
