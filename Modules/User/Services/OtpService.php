<?php

namespace Modules\User\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Modules\User\app\Models\User;
use Modules\User\Enums\UserStatus;
use Modules\User\Mail\OtpNotification;
use Modules\User\Repositories\UserRepository;
use Modules\User\Traits\otpTrait;

class OtpService
{
    use otpTrait;
    public $model;
    public function __construct(protected UserRepository $repository){
        $this->repository = $repository;
    }

    public function checkOtp(Collection $data)
    {
        return $this->repository->getFirstBy([
            'email' => $data->get('email'),
            'otp_code' => $data->get('otp_code')
        ]);
    }

    public function activateOtp(Collection $data): bool
    {
        $user = $this->checkOtp($data);

        if ($user) {
            $this->repository->update($user, [
                'otp_code' => null,
                'email_verified_at' => now(),
                'status'=>UserStatus::ACTIVE->value
            ]);
            $user->token = $this->repository->createToken($user);
            $this->model = $user;
            return true;
        }
        return false;
    }

    public function sendOtp(Collection $data): bool
    {
        $otp = $this->generateOtp();

        $user = $this->repository->getFirstBy(['email' => $data->get('email')]);

        if ($user) {
            $this->repository->update($user, [
                'otp_code' => $otp,
                'email_verified_at' => now(),
                'status'=>UserStatus::ACTIVE->value
            ]);
            $this->model = $user;

            // Send OTP via email (default is reset type)
            try {
                Mail::to($user->email)->send(new OtpNotification(User::find($user->id), $otp, 'reset'));
            } catch (\Exception $e) {
                // Log the error but continue with the process
                Log::error('Failed to send OTP email: ' . $e->getMessage());
            }

            return true;
        }
        return false;
    }
}
