<?php

use Illuminate\Support\Facades\Route;
use Modules\User\app\Http\Controllers\Api\AuthController;
use Modules\User\app\Http\Controllers\Api\ProfileController;
use Modules\User\app\Http\Controllers\Portal\RegisterController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('attendee')->group(function () {

    Route::post('register', [AuthController::class, 'register'])->name('user.register');
    Route::post('checkOtpAndActivate', [AuthController::class, 'checkOtpAndActivate'])->name('user.checkOtpAndActivate');

    Route::middleware(['throttle:5,1'])->group(function () {
        Route::post('sendOtp', [AuthController::class, 'sendOtp'])->name('user.sendOtp');
        Route::post('checkOtp', [AuthController::class, 'optCheck'])->name('user.checkOtp');
        Route::post('resetPassword', [AuthController::class, 'resetPassword'])->name('user.resetPassword');
    });

    Route::middleware(['throttle:5,1'])->group(function () {
        Route::post('login', [AuthController::class, 'login'])->middleware(['isCustomerActive'])->name('customer.login');
    });

    Route::middleware(['auth:sanctum'])->group(function () {
        Route::post('changePassword', [ProfileController::class, 'changePassword'])->name('user.changePassword');
        Route::post('logout', [AuthController::class, 'logout'])->name('user.logout');
        Route::delete('deleteAccount', [ProfileController::class, 'deleteAccount'])->name('user.delete');
        Route::put('changeLanguage', [ProfileController::class, 'changeLanguage'])->name('user.changeLanguage');
        Route::delete('deleteImage', [ProfileController::class, 'deleteImage'])->name('user.deleteImage');
        Route::post('checkpassword', [ProfileController::class, 'checkPassword'])->name('user.deleteImage');
    });
    Route::middleware(['auth:sanctum', 'isCustomerActive'])->group(function () {
        Route::get('profile', [ProfileController::class, 'profile'])->name('user.profile');
        Route::post('update', [ProfileController::class, 'updateProfile'])->name('customer.profile.update');
        Route::post('changeEmail', [ProfileController::class, 'changeEmail'])->name('customer.profile.changeEmail');
        Route::get('get-portal-user/{id}', [ProfileController::class, 'getPortalUserProfile'])->name('portalUser.profile');
    });

});
