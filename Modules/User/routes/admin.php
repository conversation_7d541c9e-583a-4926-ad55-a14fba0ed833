<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;
use Modules\User\app\Http\Controllers\Admin\AuthController;

//TODO move the code logic to the controller
Route::get('/', [\App\Http\Controllers\DashboardController::class,'index'])->name('admin');

Route::get('admin/lang/{locale}', [\Modules\User\app\Http\Controllers\Admin\UserController::class,'changeLang'])->name('change.language');
// todo : menu icon that has dropdown has different size with other menu icon , translation make it work through cookies
Route::get('logout', [AuthController::class, 'destroy'])->name('auth.logout');
Route::resource('roles', \Modules\User\app\Http\Controllers\Admin\RoleController::class);
Route::resource('users', \Modules\User\app\Http\Controllers\Admin\UserController::class);
Route::get('users/active/{user_id}', [\Modules\User\app\Http\Controllers\Admin\UserController::class,'ActiveAccount'])->name('user.status');
