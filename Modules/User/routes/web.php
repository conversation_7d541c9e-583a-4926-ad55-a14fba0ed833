<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Modules\User\app\Http\Controllers\Portal\ValidateRegisterStepsController;
use Modules\GeneralEvent\Http\Controllers\Portal\EventSessionController;

Route::middleware('guest')->group(function () {

    Route::get('admin/login', [\Modules\User\app\Http\Controllers\Admin\AuthController::class, 'create'])->name('admin.login');
    Route::post('/admin/login/store', [\Modules\User\app\Http\Controllers\Admin\AuthController::class, 'store'])->name('admin.auth.login.store');

});


Route::post('register/stepOne', [ValidateRegisterStepsController::class, 'registerStepOne']);
Route::post('register/stepTwoOranizer', [ValidateRegisterStepsController::class, 'organizerStepTwo']);
Route::post('register/stepTwoSpeaker', [ValidateRegisterStepsController::class, 'speakerStepTwo']);



Route::middleware(['auth', 'verified'])->group(function () {

    // Event Details Route
    Route::get('/portal/events/{id}', function ($id) {
        $event = [
            'id' => 1,
            'title' => 'التفكير التصميمي لرواد الأعمال',
            'date' => '22 فبراير, 2025 - 05:15 م',
            'location' => 'هيلتون جدة',
            'speaker' => 'احمد مصطفى',
            'status' => 'قادمة',
            'category' => 'ادارة اعمال',
            'rating' => 4.5,
            'duration' => '40 ساعة',
            'attendanceType' => 'عن بعد',
            'certificateType' => 'شهادة معتمدة',
            'speakerImage' => '/assets/images/Artboard.png',
            'eventImage' => '/assets/images/Artboard.png',
            'certificateImage' => '/path/to/certificate.jpg'
        ];

        return Inertia::render('Portal/Speaker/Events/EventDetails', [
            'event' => $event
        ]);
    })->name('event.details');

    Route::get('/portal/events', function () {
        return Inertia::render('Portal/Speaker/Events/Index');
    })->middleware(['auth', 'verified'])->name('events.list');
    Route::post('/quickAddSpeaker', [EventSessionController::class, 'quickAddSpeaker'])
        ->name('quickAddSpeaker');

    Route::get('/portal/event/create', function () {
        return Inertia::render('Portal/Organizer/Events/Create');
    })->middleware(['auth', 'verified'])->name('events.list');

    Route::get('/getUserByEmail', [EventSessionController::class, 'getUserByEmail'])->name('getUserByEmail')->middleware(['auth', 'verified']);


});

Route::middleware('auth')->group(function () {
    Route::get('/profile/{id}', [ProfileController::class, 'show']);

    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});


