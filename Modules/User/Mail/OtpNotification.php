<?php

namespace Modules\User\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Modules\User\app\Models\User;

class OtpNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        protected User $user,
        protected string $otp,
        protected string $type = 'reset' // Default to reset, can also be 'registration'
    ) {}

    /**
     * Build the message.
     */
    public function build()
    {
        $subject = $this->type === 'registration' 
            ? 'Account Verification Code' 
            : 'Your OTP Code';
            
        return $this->subject($subject)
            ->view('emails.otp-notification', [
                'user' => $this->user,
                'otp' => $this->otp,
                'type' => $this->type
            ]);
    }
}
