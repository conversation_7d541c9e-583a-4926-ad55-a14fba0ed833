<?php

namespace Modules\User\app\Http\Controllers\Admin;


use Modules\User\app\Http\Requests\Admin\Auth\LoginRequest;

class AuthController
{
    public function create()
    {
        return view('user::Auth.login');

    }

    public function store(LoginRequest $request)
    {
        $request->authenticate();
        $request->session()->regenerate();
        return redirect()->intended(route('admin.admin'));
    }

    public function destroy()
    {
        auth()->logout();
        return redirect()->route('admin.login');
    }

}
