<?php

namespace Modules\User\app\Http\Controllers\Api;

use Modules\User\app\Http\Resources\Api\PortalUserResource;
use Modules\User\app\Models\User;
use Illuminate\Http\Request;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\User\app\Http\Requests\Api\changeEmailRequest;
use Modules\User\app\Http\Requests\Api\ChangePasswordRequest;
use Modules\User\app\Http\Requests\Api\ResetPasswordRequest;
use Modules\User\app\Http\Resources\Api\UserResource;
use Modules\User\app\Http\Requests\Api\UpdateProfileRequest;
use Modules\User\Services\AuthApiService;
use Modules\User\Services\OtpService;
use Modules\User\Services\ProfileService;

class ProfileController
{
    public $model = User::class;

    public function __construct(private AuthApiService $userService, protected ProfileService $profileService)
    {
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $result = $this->userService->resetPassword(collect($request->validated()));
            if ($result)
            {
                return ApiResponse::success('done');
            }

            return ApiResponse::errors('This Otp Not Valid.', 400);
    }

    public function updateProfile(UpdateProfileRequest $request)
    {
        $user = $request->user();

        $updatedUser = $this->userService->updateProfile($request->validated(), $user);

        return ApiResponse::data(new UserResource($updatedUser));

    }

    public function changePassword(ChangePasswordRequest $request)
    {
        $this->userService->model = $request->user();


        $result = $this->userService->changePassword($request->validated());

        if ($result)
            return ApiResponse::success();


        return ApiResponse::errors('Your old password not correct');

    }

    public function deleteProfileImage(Request $request)
    {
        $user = $this->userService->deleteProfileImage($request->user());

        return ApiResponse::data(new UserResource($user));
    }

    public function deleteAccount(Request $request)
    {
        $user = $request->user();
        $this->userService->deleteAccount($user, $request->input('close_reason'));
        return ApiResponse::success();
    }

    public function deleteImage(Request $request)
    {
        auth()->user()->clearMediaCollection('profile');

        return ApiResponse::success();
    }

    public function changeLanguage()
    {
        $user = auth()->user();
        $languages = ["ar" => "en", "en" => "ar"];
        $user->lang = $languages[$user->lang];
        $user->save();

        return ApiResponse::data(new UserResource($user));
    }

    public function changeEmail(changeEmailRequest $request)
    {
        /** @var \Modules\User\app\Models\User|null $user */
        $user = auth()->user();

        if (!$user) {
            return ApiResponse::error('User not authenticated', 401);
        }

        $updated = $this->userService->changeEmail(collect($request->only('email')), $user);

        if (!$updated) {
            return ApiResponse::error('Unable to update email. Email may already exist or is the same as the current email');
        }

        return ApiResponse::success();
    }

    public function checkPassword(Request $request)
    {
        $result = $this->userService->checkPassword($request['password']);
        return ApiResponse::data($result);
    }

    public function profile()
    {
        $user = auth()->user();
        return ApiResponse::data(new UserResource($user));
    }

    public function getPortalUserProfile(int $id): \Illuminate\Http\JsonResponse
    {
        try {
            $user = $this->profileService->getPortalUser($id);
            return ApiResponse::data(PortalUserResource::make($user));
        } catch (\Exception $e) {
            return ApiResponse::error($e->getMessage());
        }

    }
}
