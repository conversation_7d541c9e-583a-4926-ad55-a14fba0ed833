<?php

namespace Modules\User\app\Http\Controllers\Api;

use Modules\User\app\Models\User;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\User\app\Http\Requests\Api\LoginRequest;
use Modules\User\app\Http\Requests\Api\OtpCheckRequest;
use Modules\User\app\Http\Requests\Api\RegisterRequest;
use Modules\User\app\Http\Requests\Api\ResetPasswordRequest;
use Modules\User\app\Http\Requests\Api\SendOtpRequest;
use Modules\User\app\Http\Resources\Api\UserResource;
use Modules\User\Services\AuthApiService;
use Modules\User\Services\OtpService;

class AuthController
{
    public $model = User::class;
    const EMAIL_NOTIFICATION_CLASS = "EmailOtp";


    public function __construct(private AuthApiService $userService , private OtpService $otpService)
    {
    }

    public function register(RegisterRequest $request)
    {
        $result = $this->userService->register(collect($request->validated()));

        if ($result){
            return ApiResponse::data(new UserResource($this->userService->model));
        }

        else
            return ApiResponse::errors($this->userService->message);
    }

    public function checkOtpAndActivate(OtpCheckRequest $request)
    {
        $result = $this->otpService->activateOtp(collect($request->validated()));
        
        if ($result) {
            return ApiResponse::data(
                [
                    'customer' => new UserResource($this->otpService->model),
                    'token' => $this->otpService->model->token

                ]
            );
        } else
            return ApiResponse::errors('This Otp Not Valid.');
    }

    public function login(LoginRequest $request)
    {
        $result = $this->userService->login(collect($request->validated()));
        
        if ($result) {
                // if ($request->has('device_token'))
                //TODO add event here
                // $this->notificationService->store($request->get('device_token'), $this->userService->model->id);
            
            return ApiResponse::data(
                [
                    'customer' => new UserResource($this->userService->model),
                    'token' => $this->userService->model->token

                ]
            );
        } else
        return ApiResponse::errors($this->userService->message ?? 'Email & Password do not match with our record.' , $this->userService->code ?? 400);
    }

    public function sendOtp(SendOtpRequest $request)
    {
        $result = $this->otpService->sendOtp(collect($request->validated()));
        return ApiResponse::data(['otp_code' => $this->otpService->model->otp_code]);
    }

    public function optCheck(OtpCheckRequest $request)
    {
        $result = $this->otpService->checkOtp(collect($request->validated()));

        if ($result)
            return ApiResponse::success();
        else
            return ApiResponse::errors('This Otp Not Valid.');

    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $result = $this->userService->resetPassword(collect($request->validated()));
            if ($result)
            {
                return ApiResponse::success('done');
            }

            return ApiResponse::errors('This Otp Not Valid.', 400);
    }

    public function logout()
    {
        $this->userService->logout();
    
        return ApiResponse::success();
    }
}
