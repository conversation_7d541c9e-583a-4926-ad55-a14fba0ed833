<?php

namespace Modules\User\app\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use Modules\User\app\Http\Requests\Portal\Register\OrganizerRegisterSteps\OrganizerStepThreeRequest;
use Modules\User\app\Http\Requests\Portal\Register\OrganizerRegisterSteps\OrganizerStepTwoRequest;
use Modules\User\app\Http\Requests\Portal\Register\SpeakerRegisterSteps\SpeakerStepThreeRequest;
use Modules\User\app\Http\Requests\Portal\Register\SpeakerRegisterSteps\SpeakerStepTwoRequest;
use Modules\User\app\Http\Requests\Portal\Register\Shared\RegisterStepOneRequest;

class ValidateRegisterStepsController extends Controller
{

    public function registerStepOne(RegisterStepOneRequest $request)
    {
    }

    public function speakerStepTwo(SpeakerStepTwoRequest $request)
    {
    }

    public function organizerStepTwo(OrganizerStepTwoRequest $request)
    {
    }
}
