<?php

namespace Modules\User\app\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use Modules\User\app\Http\Requests\Auth\LoginRequest;
use Modules\User\app\Models\User;
use Modules\User\Services\AuthPortalService;

class LoginController extends Controller
{
    public function __construct(protected AuthPortalService $authService)
    {

    }

    /**
     * Display the login view.
     */
    public function create(): Response
    {

        if(request()->has('id') && request()->has('verified')){
            $user = User::findOrFail(request('id'));

            if(!$user){
                return Inertia::render('Auth/Login', ['message' => 'error ']);
            }
            if(is_null($user->email_verified_at)){
                $user->update(['email_verified_at'=>now()]);
            }

            return Inertia::render('Auth/Login', ['message' => 'account verified ']);
        }
        return Inertia::render('Auth/Login', $this->authService->getLoginViewData());
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        // Attempt to authenticate the user using the provided credentials
        if (Auth::attempt($request->only('email', 'password'))) {
            // Check if the user is active
             if (!$request->user()->isActive()) {
                 // Log the user out if inactive
                 auth()->logout();
                 // Return back to the login page with an error message using Inertia
                 return Redirect::route('login')->withErrors(['email' => 'Your account is not active.']);
             }

            $request->session()->regenerate();

            return redirect()->intended(RouteServiceProvider::HOME);
        }

        // If authentication fails, return back to the login page with an error message using Inertia
        return Redirect::route('login')->withErrors(['email' => 'Invalid credentials.']);
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        return $this->authService->logoutPortal($request);
    }
}
