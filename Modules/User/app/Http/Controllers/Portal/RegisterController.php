<?php

namespace Modules\User\app\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Modules\User\app\Http\Requests\Portal\Register\RegisterRequest;
use Modules\User\Services\AuthPortalService;

class RegisterController extends Controller
{
    public function __construct(protected AuthPortalService $authPortalService)
    {
    }
    /**
     * Display the registration view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Register');
    }
    
    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(RegisterRequest $request): RedirectResponse
    {
        try {
            $registrationSuccess = $this->authPortalService->register($request);

            if (!$registrationSuccess) {
                return redirect()->back()
                    ->withErrors(['registration' => $this->authPortalService->getMessage()])
                    ->withInput();
            }

            return redirect()->back();

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'An error occurred during registration.'])
                ->withInput();
        }
    }
}
