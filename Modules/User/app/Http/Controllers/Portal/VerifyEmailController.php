<?php

namespace Modules\User\app\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\RedirectResponse;
use Modules\User\app\Http\Requests\Auth\EmailVerificationRequest;

class VerifyEmailController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     */
    public function __invoke(EmailVerificationRequest $request): RedirectResponse
    {
        $request->fulfill();
        return redirect('/login');
//        if ($request->user()->hasVerifiedEmail()) {
//            return redirect()->intended(RouteServiceProvider::LOGIN.'?verified=1');
////            return redirect()->intended(route('login').'?verified=1');
//        }
//
//        if ($request->user()->markEmailAsVerified()) {
//            event(new Verified($request->user()));
//        }
//        return redirect()->intended(RouteServiceProvider::LOGIN.'?verified=1');
//        return redirect()->intended(route('login').'?verified=1');
    }
}
