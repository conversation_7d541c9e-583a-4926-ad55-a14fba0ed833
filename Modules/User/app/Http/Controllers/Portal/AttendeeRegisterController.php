<?php

namespace Modules\User\app\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Modules\User\app\Http\Requests\Portal\Register\AttendeeRegister\AttendeeRegisterRequest;
use Modules\User\Services\AuthPortalService;

class AttendeeRegisterController extends Controller
{
    public function __construct(protected AuthPortalService $authPortalService)
    {
    }

    /**
     * Display the attendee registration view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/AttendeeRegister');
    }

    /**
     * Handle an incoming attendee registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(AttendeeRegisterRequest $request): RedirectResponse
    {
        try {
            // Add account type to request for the service
            $request->merge(['account' => 'attendee']);

            $registrationSuccess = $this->authPortalService->register($request);

            if (!$registrationSuccess) {
                return redirect()->back()
                    ->withErrors(['registration' => $this->authPortalService->getMessage()])
                    ->withInput();
            }

            // Return success response for Inertia to handle in frontend
            return redirect()->back();

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'An error occurred during registration.'])
                ->withInput();
        }
    }
}
