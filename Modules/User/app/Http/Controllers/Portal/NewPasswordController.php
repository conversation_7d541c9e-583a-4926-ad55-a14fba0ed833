<?php

namespace Modules\User\app\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;
use Modules\User\app\Http\Requests\Portal\ResetPasswordRequest;
use Modules\User\Services\AuthPortalService;

class NewPasswordController extends Controller
{
    /**
     * Display the password reset view.
     */
    public function create(ResetPasswordRequest $request): Response|RedirectResponse
    {
        $checkToken = app(AuthPortalService::class)->checkResetPasswordTokenOfProvidedEmail($request->email, $request->token);

        if (!$checkToken) {
            return redirect()->route('password.request')->with('error', __('Invalid token'));
        }

        return Inertia::render('Auth/ResetPassword', [
            'email' => $request->email,
        ]);
    }

    /**
     * Handle an incoming new password request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'password' => [
                'required',
                'string',
                'min:8',
                'max:16',
                'confirmed',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,16}$/',
            ],
        ]);

        $user = \Modules\User\app\Models\User::where('email', $validated['email'])->first();

        if (!$user) {
            throw \Illuminate\Validation\ValidationException::withMessages([
                'email' => [__('User not found.')],
            ]);
        }

        $user->update([
            'password' => Hash::make($validated['password']),
        ]);


        // Invalidate the token after password reset
        app(AuthPortalService::class)->invalidateToken($validated['email'], $request->token);

        return redirect()->route('login')->with('status', __('Password has been reset successfully.'));
    }
}
