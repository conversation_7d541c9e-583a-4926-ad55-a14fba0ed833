<?php

namespace Modules\User\app\Http\Requests\Portal;

use Illuminate\Foundation\Http\FormRequest;

class QuickAddSpeakerRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|unique:users|string|max:20',
        ];
    }
}
