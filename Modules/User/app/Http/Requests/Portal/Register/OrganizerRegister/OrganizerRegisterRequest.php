<?php

namespace Modules\User\app\Http\Requests\Portal\Register\OrganizerRegister;

use Illuminate\Foundation\Http\FormRequest;
use Modules\User\app\Http\Requests\Portal\Register\OrganizerRegisterSteps\OrganizerStepThreeRequest;
use Modules\User\app\Http\Requests\Portal\Register\OrganizerRegisterSteps\OrganizerStepTwoRequest;
use Modules\User\app\Http\Requests\Portal\Register\Shared\RegisterStepOneRequest;

class OrganizerRegisterRequest extends FormRequest
{
    public function rules(): array
    {
        return array_merge(
        (new RegisterStepOneRequest())->rules(),
        (new OrganizerStepTwoRequest())->rules(),
        (new OrganizerStepThreeRequest())->rules()
        );
    }

    public function authorize(): bool
    {
        return true;
    }
}