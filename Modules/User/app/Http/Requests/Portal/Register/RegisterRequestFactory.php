<?php

namespace Modules\User\app\Http\Requests\Portal\Register;

use Modules\User\app\Http\Requests\Portal\Register\AttendeeRegister\AttendeeRegisterRequest;
use Modules\User\app\Http\Requests\Portal\Register\OrganizerRegister\OrganizerRegisterRequest;
use Modules\User\app\Http\Requests\Portal\Register\SpeakerRegister\SpeakerRegisterRequest;
use Modules\User\Enums\UserRoleEnum;

class RegisterRequestFactory
{
    public static function make(string $userType)
    {
        return match ($userType) {
            UserRoleEnum::ORGANIZER->value => new OrganizerRegisterRequest(),
            UserRoleEnum::SPEAKER->value => new SpeakerRegisterRequest(),
            UserRoleEnum::ATTENDEE->value => new AttendeeRegisterRequest(),
        };
    }
}