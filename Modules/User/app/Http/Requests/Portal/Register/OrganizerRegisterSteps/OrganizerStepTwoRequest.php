<?php

namespace Modules\User\app\Http\Requests\Portal\Register\OrganizerRegisterSteps;

use Illuminate\Foundation\Http\FormRequest;

class OrganizerStepTwoRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'company_name' => 'required|max:255',
            'website' => 'required|url|max:255',
            'commercial_number' => 'required|max:255',
            'tax_number' => 'required|max:255',
            'about_company' => 'required|max:255',
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
