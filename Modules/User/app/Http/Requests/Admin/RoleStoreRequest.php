<?php

namespace Modules\User\app\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RoleStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "name"=>"required|string|unique:roles,name|max:200",
            "translated_name"=>"required|array",
            "translated_name.ar"=>"required|max:200",
            "translated_name.en"=>"required|max:200",
            "guard_name"=>"required|in:web,api",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
