<?php

namespace Modules\User\app\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RoleUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "translated_name"=>"sometimes|array",
            "translated_name.ar"=>"sometimes|max:200",
            "translated_name.en"=>"sometimes|max:200",
            "guard_name"=>"sometimes|in:web,api",
            "name"=>"sometimes",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
