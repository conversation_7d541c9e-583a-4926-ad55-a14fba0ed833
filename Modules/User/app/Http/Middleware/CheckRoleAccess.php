<?php

namespace Modules\User\app\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\User\Enums\UserRoleEnum;
use Khaleds\Shared\Helpers\ApiResponse;


class CheckRoleAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth()->user(); // Use the authenticated user

        if ($user) {
            if ($request->is('api/attendee/*') && !$user->hasRole(UserRoleEnum::ATTENDEE->value)) {
                // If accessing /api/attendee/* and user is not an attendee, return JSON response
                return ApiResponse::error('Access denied. Only attendees can access API.', 403);
            } elseif ($request->is('dashboard/*') && !$user->hasRole(UserRoleEnum::ADMIN->value)) {
                // If accessing /dashboard/* and user is not an admin, redirect back with an error
                return redirect()->back()->withErrors(['Access denied. Only Admin can access web interface.']);
            } elseif (!$request->is('api/attendee/*') && !$request->is('dashboard/*') &&
                      !$user->hasRole([UserRoleEnum::ORGANIZER->value, UserRoleEnum::SPEAKER->value])) {
                // If accessing anything else except dashboard/* and user is not an organizer or speaker, redirect back with an error
                return redirect()->back()->withErrors(['Access denied. Only organizers or speakers can access this interface.']);
            }
        }

        return $next($request);
    }

}
