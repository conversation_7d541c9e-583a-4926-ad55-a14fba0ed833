<?php

namespace Modules\User\app\Http\Resources\Api;

use App\Enums\MediaEnum;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Enums\UserRoleEnum;

class PortalUserResource extends JsonResource
{
    protected array $userMeta;
    public function __construct($resource)
    {
        parent::__construct($resource);
        $this->userMeta = $this->meta->pluck('value', 'key')->toArray();
    }

    public function toArray($request)
    {
        return [
            "id" => $this->id ?? "",
            "name" => $this->fullName ?? null,
            "first_name" => $this->first_name ?? null,
            "last_name" => $this->last_name ?? null,
            "email" => $this->email ?? null,
            "meta" => $this->userMeta,
            "description" => $this->userMeta['about_you'] ?? ($this->userMeta['about_company'] ?? null),
            "lang" => $this->lang ?? 'en',
            "role" => $this->defaultRole?->name ?? null ,
            "phone" => $this->phone ?? null,
            "birth_date" => $this->birthdate ?? null,
            "image" => $this->getMediaImage(MediaEnum::PROFILE_PIC->value),
            "cv" => $this->when($this->hasRole(UserRoleEnum::SPEAKER->value), fn() => $this->getMedia(MediaEnum::CV->value)->first()),
            "certificates" => $this->when($this->hasRole(UserRoleEnum::SPEAKER->value), fn() => $this->getMediaImages(MediaEnum::CERTIFICATES->value)),
            "portfolio" => $this->when($this->hasRole(UserRoleEnum::ORGANIZER->value), fn() => $this->getMedia(MediaEnum::PORTFOLIO->value)->first()),
            "company_logo" => $this->when($this->hasRole(UserRoleEnum::ORGANIZER->value), fn() => $this->getMediaImage(MediaEnum::COMPANY_LOGO->value)),
            "establishment_documents" => $this->when($this->hasRole(UserRoleEnum::ORGANIZER->value), fn() => $this->getMediaImages(MediaEnum::ESTABLISHMENT_DOCUMENTS->value)),
        ];
    }
}
