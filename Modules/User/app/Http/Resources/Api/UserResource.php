<?php

namespace Modules\User\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{

    public function toArray($request)
    {
        return [
            "id" => $this->id ?? "",
            "name" => $this->fullName ?? null,
            "first_name" => $this->first_name ?? null,
            "last_name" => $this->last_name ?? null,
            "email" => $this->email ?? null,
            "lang" => $this->lang ?? 'en',
            "role" => $this->defaultRole?->name ?? null ,
            "otp" => (app()->environment(['production'])) ? "" : (string)$this->otp_code,
            "phone" => $this->phone ?? null,
            "birth_date" => $this->birthdate ?? null,
            "image" => $this->getMediaImage('users'),
        ];


    }
}
