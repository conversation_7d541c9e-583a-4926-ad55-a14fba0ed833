<?php

namespace Modules\User\app\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Notification;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Helpers\HandleMediaCollection;
use Modules\GeneralEvent\Entities\Category;
use Modules\User\Enums\UserStatus;
use Modules\User\Notifications\PasswordChangedNotification;
use Modules\User\Notifications\UserActivated;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Hash;
use Modules\GeneralEvent\Entities\Level;
use Modules\User\Database\factories\UserFactory;
use Modules\User\Traits\UserStatusChecks;

/**
 * @property integer $id
 * @property string $name
 * @property string $gender
 * @property string $block_reason
 * @property string $lat
 * @property string $lng
 * @property string $email
 * @property string $country_code
 * @property string $phone
 * @property string $password
 * @property string $otp_code
 * @property string $otp_activated_at
 * @property string $last_login
 * @property boolean $is_first_login
 * @property boolean $is_active
 * @property boolean $is_blocked
 * @property string $user_agent
 * @property string $user_ip
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property AccountMeta[] $accountMetas
 *
 */
class  User extends Authenticatable implements HasMedia, CanResetPassword, MustVerifyEmail
{

    use HasFactory;
    use HasApiTokens;
    use InteractsWithMedia;
    use HandleMediaCollection;
    use SoftDeletes;
    use Notifiable;
    use HasRoles;
    use UserStatusChecks;

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    protected $guard_name = 'web';

    /**
     * @var array
     */
    protected $guarded = ['id'];

    protected $hidden = ['password'];

    protected static function newFactory()
    {
        return UserFactory::new();
    }

    protected function password(): Attribute
    {
        return Attribute::make(
            set: fn ($value) => Hash::needsRehash($value) ? Hash::make($value) : $value
        );
    }

    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn () => "{$this->first_name} {$this->last_name}"
        );
    }

        /**
     * Register media collections.
     *
     * @return void
     */

    public function registerMediaCollections(): void
    {
        // Pass an array of collection names
        $this->registerMediaCollection(['profile_pictures', 'cover_photos']);
    }

    public function defaultRole()
    {
        return $this->belongsTo(Role::class, 'default_role');
    }

    public function meta()
    {
        return $this->hasMany(UserMeta::class);
    }

        /**
     * Get the levels created by the user.
     */
    public function levels(): HasMany
    {
        return $this->hasMany(Level::class);
    }

    public function categoryPreferences(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'user_category_preferences','user_id', 'category_id')->withTimestamps();
    }

    protected static function booted()
    {
        static::updating(function ($user) {
            // Check if the password attribute was changed
            if ($user->isDirty('password')) {
                // Notify the user after the update
                static::updated(function ($updatedUser) {
                    $updatedUser->notify(new PasswordChangedNotification());
                });
            }
        });
    }
}
