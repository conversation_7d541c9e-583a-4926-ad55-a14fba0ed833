<?php

namespace Modules\User\tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Modules\User\app\Models\User;
use Modules\User\Enums\UserRoleEnum;
use Spatie\Permission\Models\Role;

class RegisterControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Role $organizerRole;
    protected Role $speakerRole;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
        Event::fake();

        // Create roles if they don't exist and store them as properties
        $this->organizerRole = Role::create(['name' => UserRoleEnum::ORGANIZER->value, 'guard_name' => 'web']);
        $this->speakerRole = Role::create(['name' => UserRoleEnum::SPEAKER->value, 'guard_name' => 'web']);
    }

    /** @test */
    public function it_can_register_an_organizer()
    {
        // Arrange
        $organizerData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            'company_name' => 'Test Company',
            'website' => 'https://example.com',
            'commercial_registeration_number' => '*********',
            'tax_number' => '*********',
            'about' => 'About the company',
            'company_logo' => UploadedFile::fake()->image('logo.jpg', 100, 100),
            'exhibition_holding_document' => UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf'),
            'previous_works' => UploadedFile::fake()->create('works.pdf', 1000, 'application/pdf'),
        ];

        // Act
        $response = $this->post('/register', $organizerData);

        // Assert
        // $response->assertStatus(302);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'default_role' => $this->organizerRole->id,
        ]);
        
        $this->assertDatabaseHas('users_meta', [
            'user_id' => $user->id,
            'key' => 'company_name',
            'value' => 'Test Company'
        ]);

        $this->assertTrue($user->hasRole($this->organizerRole->name));
        $this->assertEquals($this->organizerRole->id, $user->default_role);
        Event::assertDispatched(Registered::class);
        $this->assertAuthenticated();
    }

    /** @test */
    public function it_can_register_a_speaker()
    {
        // Arrange
        $speakerData = [
            'first_name' => 'Jane',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::SPEAKER->value,
            'viewed_name' => 'Dr. Jane Doe',
            'website' => 'https://speaker-example.com',
            'about' => 'About the speaker',
            'profile_image' => UploadedFile::fake()->image('profile.jpg', 100, 100),
            'certificates' => UploadedFile::fake()->create('certificates.pdf', 1000, 'application/pdf'),
            'cv' => UploadedFile::fake()->create('cv.pdf', 1000, 'application/pdf'),
        ];

        // Act
        $response = $this->post('/register', $speakerData);

        // Assert
        $response->assertStatus(302); // Check for redirect

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'default_role' => $this->speakerRole->id,
        ]);
        
        $this->assertDatabaseHas('users_meta', [
            'user_id' => $user->id,
            'key' => 'viewed_name',
            'value' => 'Dr. Jane Doe'
        ]);

        $this->assertTrue($user->hasRole($this->speakerRole->name));
        $this->assertEquals($this->speakerRole->id, $user->default_role);
        Event::assertDispatched(Registered::class);
        $this->assertAuthenticated();
    }

    /** @test */
    public function it_validates_organizer_required_fields()
    {
        // Arrange
        $invalidData = [
            'email' => 'invalid-email',
            'user_type' => UserRoleEnum::ORGANIZER->value,
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'first_name',
                'last_name',
                'email',
                'password',
                'company_name',
                'website',
                'commercial_registeration_number',
                'tax_number',
                'about',
                'company_logo',
                'exhibition_holding_document',
                'previous_works',
            ]);
    }

    /** @test */
    public function it_validates_speaker_required_fields()
    {
        // Arrange
        $invalidData = [
            'email' => 'invalid-email',
            'user_type' => UserRoleEnum::SPEAKER->value,
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'first_name',
                'last_name',
                'email',
                'password',
                'viewed_name',
                'website',
                'about',
                'profile_image',
                'certificates',
                'cv',
            ]);
    }

    /** @test */
    public function it_prevents_duplicate_email_registration()
    {
        // Arrange
        $existingUser = User::create([
            'first_name' => 'Existing',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'default_role' => $this->organizerRole->id,
        ]);
        $existingUser->assignRole($this->organizerRole);

        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            'company_name' => 'Test Company',
            'website' => 'https://example.com',
            'commercial_registeration_number' => '*********',
            'tax_number' => '*********',
            'about' => 'About the company',
            'company_logo' => UploadedFile::fake()->image('logo.jpg', 100, 100),
            'exhibition_holding_document' => UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf'),
            'previous_works' => UploadedFile::fake()->create('works.pdf', 1000, 'application/pdf'),
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function it_validates_password_requirements()
    {
        // Arrange
        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'weak',
            'password_confirmation' => 'weak',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            'company_name' => 'Test Company',
            'website' => 'https://example.com',
            'commercial_registeration_number' => '*********',
            'tax_number' => '*********',
            'about' => 'About the company',
            'company_logo' => UploadedFile::fake()->image('logo.jpg', 100, 100),
            'exhibition_holding_document' => UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf'),
            'previous_works' => UploadedFile::fake()->create('works.pdf', 1000, 'application/pdf'),
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    /** @test */
    public function it_validates_file_uploads()
    {
        // Arrange
        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            'company_name' => 'Test Company',
            'website' => 'https://example.com',
            'commercial_registeration_number' => '*********',
            'tax_number' => '*********',
            'about' => 'About the company',
            'company_logo' => UploadedFile::fake()->create('document.txt', 500),
            'exhibition_holding_document' => UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf'),
            'previous_works' => UploadedFile::fake()->create('works.pdf', 1000, 'application/pdf'),
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['company_logo']);
    }

    /** @test */
    public function it_validates_file_size_limits()
    {
        // Arrange
        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            'company_name' => 'Test Company',
            'website' => 'https://example.com',
            'commercial_registeration_number' => '*********',
            'tax_number' => '*********',
            'about' => 'About the company',
            'company_logo' => UploadedFile::fake()->create('large.jpg', 1000),
            'exhibition_holding_document' => UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf'),
            'previous_works' => UploadedFile::fake()->create('works.pdf', 1000, 'application/pdf'),
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['company_logo']);
    }

    /** @test */
    public function it_handles_registration_with_missing_meta_fields()
    {
        // Arrange
        $incompleteData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            // Missing meta fields
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $incompleteData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'company_name',
                'website',
                'commercial_registeration_number',
                'tax_number',
                'about',
                'company_logo',
                'exhibition_holding_document',
                'previous_works',
            ]);
    }

    /** @test */
    public function it_handles_invalid_file_types()
    {
        // Arrange
        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123',
            'password_confirmation' => 'Password123',
            'user_type' => UserRoleEnum::ORGANIZER->value,
            'company_name' => 'Test Company',
            'website' => 'https://example.com',
            'commercial_registeration_number' => '*********',
            'tax_number' => '*********',
            'about' => 'About the company',
            'company_logo' => UploadedFile::fake()->create('logo.gif', 100),
            'exhibition_holding_document' => UploadedFile::fake()->create('document.doc', 1000),
            'previous_works' => UploadedFile::fake()->create('works.txt', 1000),
        ];

        // Act
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->post('/register', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'company_logo',
                'exhibition_holding_document',
                'previous_works'
            ]);
    }
}