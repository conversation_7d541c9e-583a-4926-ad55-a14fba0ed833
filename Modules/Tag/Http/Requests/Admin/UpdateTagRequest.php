<?php

namespace Modules\Tag\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTagRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name.ar' => ['sometimes', 'string', 'max:255'],
            'name.en' => ['sometimes', 'string', 'max:255'],
            'slug.ar' => ['nullable', 'string', 'max:255'],
            'slug.en' => ['nullable', 'string', 'max:255'],
            'type' => 'nullable|string|max:255',
            'order_column' => 'nullable|integer',
        ];
    }
}
