<?php
namespace Modules\Tag\Http\Controllers\Admin;


use Khaleds\Shared\Http\Controllers\Admin\ControllerAbstract;
use Modules\Tag\Http\Requests\Admin\StoreTagRequest;
use Modules\Tag\Http\Requests\Admin\UpdateTagRequest;
use Modules\Tag\DataTables\TagDataTable;
use Modules\Tag\Services\TagService;

class TagController extends ControllerAbstract {

    protected string $storeRequest =StoreTagRequest::class;
    protected string $updateRequest =UpdateTagRequest::class;
    protected string $viewPath = "tag::tag";
    protected string $routeName = "admin.tag";
    protected string $title = 'tag';

    protected string $dataTable = TagDataTable::class;


    public function __construct(TagService $service) {
        parent::__construct($service);
    }



}
