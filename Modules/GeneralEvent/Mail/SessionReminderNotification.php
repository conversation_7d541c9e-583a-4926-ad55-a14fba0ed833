<?php

namespace Modules\GeneralEvent\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Modules\GeneralEvent\Entities\EventSession;

class SessionReminderNotification extends Mailable
{
    use Queueable, SerializesModels;

    public EventSession $session;
    public string $userType;
    public string $userName;

    public function __construct($session, $userName, $userType)
    {
        $this->session = $session;
        $this->userType = $userType;
        $this->userName = $userName;
    }

    public function build()
    {
        return $this->subject(__('emails.session_reminder_subject', ['session_name' => $this->session->name]))
            ->markdown('generalevent::emails.session.session-reminder-notification')
            ->with([
                'session' => $this->session,
                'userName' => $this->userName,
                'userType' => $this->userType,
                'metadata' => json_decode($this->session->metadata, true),
            ]);
    }
}
