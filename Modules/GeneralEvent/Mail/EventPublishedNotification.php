<?php

namespace Modules\GeneralEvent\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EventPublishedNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $event;

    public function __construct($event)
    {
        $this->event = $event;
    }

    public function build()
    {
        return $this->subject(__('emails.event_published_subject', ['event_name' => $this->event->name]))
            ->markdown('generalevent::emails.event.publish-notification')
            ->with([
                'event' => $this->event
            ]);
    }
}
