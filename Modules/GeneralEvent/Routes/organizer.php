<?php

/*
|--------------------------------------------------------------------------
| Organizer Routes
|--------------------------------------------------------------------------
|
| Here are the routes for event management that require organizer or admin role.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web", "auth", and "role:admin|organizer" middleware.
|
*/

use Illuminate\Support\Facades\Route;

use Modules\GeneralEvent\Http\Controllers\Portal\EventSessionController;
use Modules\GeneralEvent\Http\Controllers\Portal\GeneralEventController;
use Modules\GeneralEvent\Http\Controllers\Portal\LevelController;
use Modules\GeneralEvent\Http\Controllers\Portal\TicketController;
use Modules\GeneralEvent\Http\Controllers\Portal\EventLevelController;


Route::prefix('events')->group(function() {
    // Event Management Routes (Create, Edit, Update, Delete)
    Route::get('/create', [GeneralEventController::class,'create'])->name('generalEvent.create');
    Route::post('/', [GeneralEventController::class, 'store'])->name('generalEvent.store');
    Route::get('/{id}/edit', [GeneralEventController::class, 'edit'])->name('events.edit');
    Route::post('/{id}/update', [GeneralEventController::class, 'update'])
        ->name('generalEvent.update');
    Route::post('/{id}/deleteEvent', [GeneralEventController::class, 'deleteEvent'])
        ->name('generalEvent.deleteEvent');

    // Event Status Management
    Route::post('/{id}/publish', [GeneralEventController::class, 'publish'])
        ->name('generalEvent.publish');
    Route::post('/{id}/cancel', [GeneralEventController::class, 'cancelEvent'])
        ->name('generalEvent.cancelEvent');

    // Session Management Routes
    Route::get('/{id}/sessions/create', [EventSessionController::class, 'create'])->middleware(['auth', 'verified'])->name('events.sessions.create');
    Route::get('/{id}/sessions/edit', [EventSessionController::class, 'edit'])->name('events.sessions.edit');
    Route::post('/{id}/sessions/update', [EventSessionController::class, 'update'])->name('events.sessions.update');
    Route::post('/{id}/sessions/delete', [EventSessionController::class, 'deleteSession'])->name('events.sessions.delete');
    Route::post('/{id}/sessions/cancel', [EventSessionController::class, 'cancel'])->name('events.sessions.cancel');
    Route::post('/event-sessions-validated', [EventSessionController::class, 'validated'])->middleware(['auth', 'verified'])->name('eventSession.validated');
    Route::post('/event-sessions-store', [EventSessionController::class, 'store'])->middleware(['auth', 'verified'])->name('eventSession.store');

    // Ticketing Management Routes
    Route::get('/{id}/ticketing/create',[TicketController::class,'create'])
        ->middleware(['auth', 'verified'])->name('event.tickets.create');
    Route::post('/eventLevels', [EventLevelController::class, 'store'])
        ->middleware(['auth', 'verified'])->name('events.levels.store');
    Route::post('/tickets', [TicketController::class, 'storeTicket'])
        ->middleware(['auth', 'verified'])->name('events.ticket.store');
    Route::post('/ticket-levels', [TicketController::class, 'storeTicketLevel'])
        ->middleware(['auth', 'verified'])->name('events.ticketLevels.store');
});

// Level Management Routes
Route::prefix('events')->group(function() {
    Route::get('/levels', [LevelController::class, 'index'])->middleware(['auth', 'verified'])->name('levels.index');
    Route::post('/levels', [LevelController::class, 'store'])->middleware(['auth', 'verified'])->name('levels.store');
    Route::get('/levels/create', [LevelController::class, 'create'])->middleware(['auth', 'verified'])->name('levels.create');
    Route::put('/levels/{id}', [LevelController::class, 'update'])->middleware(['auth', 'verified'])->name('levels.update');
    Route::get('/levels/delete/{id}', [LevelController::class, 'destroy'])->middleware(['auth', 'verified'])->name('levels.destroy');
});

// Organizer Calendar
Route::get('/calendar', [GeneralEventController::class,'organizerCalender'])->name('generalEvent.organizerCalender');
