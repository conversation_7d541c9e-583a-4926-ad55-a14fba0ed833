<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
use Illuminate\Support\Facades\Route;

use Modules\GeneralEvent\Http\Controllers\Portal\EventSessionController;
use Modules\GeneralEvent\Http\Controllers\Portal\GeneralEventController;
use Modules\GeneralEvent\Http\Controllers\Portal\LevelController;
use Modules\GeneralEvent\Http\Controllers\Portal\TicketController;
use Modules\GeneralEvent\Http\Controllers\Portal\EventLevelController;



Route::middleware(['web', 'auth','verified','role:admin|organizer'])->prefix('events')->group(function() {
    Route::get('/create', [GeneralEventController::class,'create'])->name('generalEvent.create');
    Route::post('/', [GeneralEventController::class, 'store'])->name('generalEvent.store');


    Route::get('/{id}/edit', [GeneralEventController::class, 'edit'])->name('events.edit');
    Route::post('/{id}/update', [GeneralEventController::class, 'update'])
        ->name('generalEvent.update');

    Route::post('/{id}/deleteEvent', [GeneralEventController::class, 'deleteEvent'])
        ->name('generalEvent.deleteEvent');

    Route::get('/{id}/sessions/create', [EventSessionController::class, 'create'])->name('events.sessions.create');
    Route::get('/{id}/sessions/edit', [EventSessionController::class, 'edit'])->name('events.sessions.edit');
    Route::post('/{id}/sessions/update', [EventSessionController::class, 'update'])->name('events.sessions.update');
    Route::post('/{id}/sessions/delete', [EventSessionController::class, 'deleteSession'])->name('events.sessions.delete');
    Route::post('/{id}/sessions/cancel', [EventSessionController::class, 'cancel'])->name('events.sessions.cancel');

    Route::get('/levels', [LevelController::class, 'index'])->name('levels.index');
    Route::post('/levels', [LevelController::class, 'store'])->name('levels.store');
    Route::get('/levels/create', [LevelController::class, 'create'])->name('levels.create');
    Route::put('/levels/{id}', [LevelController::class, 'update'])->name('levels.update');
    Route::get('/levels/delete/{id}', [LevelController::class, 'destroy'])->name('levels.destroy');

    // Publish Event
    Route::post('/{id}/publish', [GeneralEventController::class, 'publish'])
        ->name('generalEvent.publish');

    Route::post('/{id}/cancel', [GeneralEventController::class, 'cancelEvent'])
        ->name('generalEvent.cancelEvent');


    Route::post('/event-sessions-validated', [EventSessionController::class, 'validated'])->name('eventSession.validated');
    Route::post('/event-sessions-store', [EventSessionController::class, 'store'])->name('eventSession.store');

    Route::get('/{id}/ticketing/create',[TicketController::class,'create'])
        ->name('event.tickets.create');
    Route::post('/eventLevels', [EventLevelController::class, 'store'])
        ->name('events.levels.store');
    Route::post('/tickets', [TicketController::class, 'storeTicket'])
        ->name('events.ticket.store');
    Route::post('/ticket-levels', [TicketController::class, 'storeTicketLevel'])
        ->name('events.ticketLevels.store');

});

Route::middleware(['web', 'auth'])->prefix('events')->group(function() {
    Route::get('/', [GeneralEventController::class,'index'])->name('generalEvent.index');

    Route::get('/{id}', [GeneralEventController::class, 'show'])->name('events.show');

    Route::get('/{id}/{section}', [GeneralEventController::class, 'show'])
        ->where('section', 'details|sessions')
        ->name('events.section');

});

// Debug route to check user roles (REMOVE THIS AFTER TESTING)
Route::middleware(['web', 'auth'])->get('/debug-roles', function() {
    $user = auth()->user();
    return response()->json([
        'user_id' => $user->id,
        'user_name' => $user->name ?? $user->first_name . ' ' . $user->last_name,
        'default_role_id' => $user->default_role,
        'default_role' => $user->defaultRole ? $user->defaultRole->name : 'No default role',
        'spatie_roles' => $user->roles->pluck('name')->toArray(),
        'spatie_roles_with_guard' => $user->roles->map(function($role) {
            return ['name' => $role->name, 'guard_name' => $role->guard_name];
        })->toArray(),
        'has_organizer_role' => $user->hasRole('organizer'),
        'has_admin_role' => $user->hasRole('admin'),
        'guard_name' => $user->guard_name,
        'current_guard' => auth()->getDefaultDriver(),
    ]);
});

// Fix current user's roles (REMOVE THIS AFTER TESTING)
Route::middleware(['web', 'auth'])->get('/fix-current-user-role', function() {
    $user = auth()->user();
    $userRepository = app(\Modules\User\Repositories\UserRepository::class);

    try {
        // Get the user's default role name
        $defaultRoleName = $user->defaultRole ? $user->defaultRole->name : null;

        if ($defaultRoleName) {
            // Re-assign the role using our fixed method
            $userRepository->assignRole($user, $defaultRoleName);

            // Refresh the user
            $user->refresh();

            return response()->json([
                'message' => 'Role fixed successfully',
                'user_id' => $user->id,
                'default_role' => $defaultRoleName,
                'spatie_roles_after_fix' => $user->roles->pluck('name')->toArray(),
                'spatie_roles_with_guard' => $user->roles->map(function($role) {
                    return ['name' => $role->name, 'guard_name' => $role->guard_name];
                })->toArray(),
                'has_role_now' => $user->hasRole($defaultRoleName),
                'success' => $user->hasRole($defaultRoleName)
            ]);
        } else {
            return response()->json([
                'error' => 'No default role found for user'
            ]);
        }
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Fix existing organizer users (REMOVE THIS AFTER FIXING)
Route::middleware(['web', 'auth'])->get('/fix-organizer-roles', function() {
    $userRepository = app(\Modules\User\Repositories\UserRepository::class);

    // Get all users who have default_role as organizer but no Spatie role
    $users = \Modules\User\app\Models\User::whereHas('defaultRole', function($query) {
        $query->where('name', 'organizer');
    })->whereDoesntHave('roles', function($query) {
        $query->where('name', 'organizer');
    })->get();

    $fixed = [];
    foreach($users as $user) {
        $userRepository->assignRole($user, 'organizer');
        $fixed[] = [
            'user_id' => $user->id,
            'name' => $user->first_name . ' ' . $user->last_name,
            'email' => $user->email
        ];
    }

    return response()->json([
        'message' => 'Fixed organizer roles',
        'fixed_users' => $fixed,
        'count' => count($fixed)
    ]);
});

// Test all user types registration and middleware access (REMOVE THIS AFTER TESTING)
Route::middleware(['web'])->get('/test-all-user-types', function() {
    $userRepository = app(\Modules\User\Repositories\UserRepository::class);
    $results = [];

    $userTypes = ['organizer', 'speaker', 'attendee'];

    foreach ($userTypes as $userType) {
        try {
            // Create a test user
            $testUser = \Modules\User\app\Models\User::create([
                'first_name' => 'Test',
                'last_name' => ucfirst($userType),
                'email' => 'test-' . $userType . '-' . time() . '@example.com',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);

            // Assign role using the unified method
            $userRepository->assignRole($testUser, $userType);

            // Check the result
            $testUser->refresh();

            $results[$userType] = [
                'user_id' => $testUser->id,
                'email' => $testUser->email,
                'default_role_id' => $testUser->default_role,
                'default_role' => $testUser->defaultRole ? $testUser->defaultRole->name : 'No default role',
                'spatie_roles' => $testUser->roles->pluck('name')->toArray(),
                'spatie_roles_with_guard' => $testUser->roles->map(function($role) {
                    return ['name' => $role->name, 'guard_name' => $role->guard_name];
                })->toArray(),
                'has_role' => $testUser->hasRole($userType),
                'can_access_organizer_routes' => $testUser->hasRole(['admin', 'organizer']),
                'middleware_compatible' => $testUser->hasRole($userType),
                'success' => $testUser->hasRole($userType) && $testUser->defaultRole && $testUser->defaultRole->name === $userType
            ];
        } catch (\Exception $e) {
            $results[$userType] = [
                'error' => $e->getMessage()
            ];
        }
    }

    return response()->json([
        'message' => 'Test all user types completed',
        'results' => $results,
        'middleware_info' => [
            'role_middleware_registered' => class_exists(\Spatie\Permission\Middleware\RoleMiddleware::class),
            'permission_config' => config('permission.models.role'),
            'cache_key' => config('permission.cache.key')
        ]
    ]);
});

// Test middleware access for current user (REMOVE THIS AFTER TESTING)
Route::middleware(['web', 'auth'])->get('/test-middleware-access', function() {
    $user = auth()->user();

    return response()->json([
        'user_id' => $user->id,
        'user_name' => $user->first_name . ' ' . $user->last_name,
        'spatie_roles' => $user->roles->pluck('name')->toArray(),
        'has_admin_role' => $user->hasRole('admin'),
        'has_organizer_role' => $user->hasRole('organizer'),
        'has_speaker_role' => $user->hasRole('speaker'),
        'has_attendee_role' => $user->hasRole('attendee'),
        'can_access_admin_routes' => $user->hasRole('admin'),
        'can_access_organizer_routes' => $user->hasRole(['admin', 'organizer']),
        'middleware_will_allow_organizer_routes' => $user->hasRole(['admin', 'organizer'])
    ]);
});

Route::middleware(['web', 'auth', 'role:admin|organizer'])->group(function() {
    Route::get('/calendar', [GeneralEventController::class,'organizerCalender'])->name('generalEvent.organizerCalender');
});

