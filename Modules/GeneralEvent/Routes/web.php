<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
use Illuminate\Support\Facades\Route;

use Modules\GeneralEvent\Http\Controllers\Portal\EventSessionController;
use Modules\GeneralEvent\Http\Controllers\Portal\GeneralEventController;
use Modules\GeneralEvent\Http\Controllers\Portal\LevelController;
use Modules\GeneralEvent\Http\Controllers\Portal\TicketController;
use Modules\GeneralEvent\Http\Controllers\Portal\EventLevelController;



Route::middleware(['web', 'auth','verified','role:admin|organizer'])->prefix('events')->group(function() {
    Route::get('/create', [GeneralEventController::class,'create'])->name('generalEvent.create');
    Route::post('/', [GeneralEventController::class, 'store'])->name('generalEvent.store');


    Route::get('/{id}/edit', [GeneralEventController::class, 'edit'])->name('events.edit');
    Route::post('/{id}/update', [GeneralEventController::class, 'update'])
        ->name('generalEvent.update');

    Route::post('/{id}/deleteEvent', [GeneralEventController::class, 'deleteEvent'])
        ->name('generalEvent.deleteEvent');

    Route::get('/{id}/sessions/create', [EventSessionController::class, 'create'])->name('events.sessions.create');
    Route::get('/{id}/sessions/edit', [EventSessionController::class, 'edit'])->name('events.sessions.edit');
    Route::post('/{id}/sessions/update', [EventSessionController::class, 'update'])->name('events.sessions.update');
    Route::post('/{id}/sessions/delete', [EventSessionController::class, 'deleteSession'])->name('events.sessions.delete');
    Route::post('/{id}/sessions/cancel', [EventSessionController::class, 'cancel'])->name('events.sessions.cancel');

    Route::get('/levels', [LevelController::class, 'index'])->name('levels.index');
    Route::post('/levels', [LevelController::class, 'store'])->name('levels.store');
    Route::get('/levels/create', [LevelController::class, 'create'])->name('levels.create');
    Route::put('/levels/{id}', [LevelController::class, 'update'])->name('levels.update');
    Route::get('/levels/delete/{id}', [LevelController::class, 'destroy'])->name('levels.destroy');

    // Publish Event
    Route::post('/{id}/publish', [GeneralEventController::class, 'publish'])
        ->name('generalEvent.publish');

    Route::post('/{id}/cancel', [GeneralEventController::class, 'cancelEvent'])
        ->name('generalEvent.cancelEvent');


    Route::post('/event-sessions-validated', [EventSessionController::class, 'validated'])->name('eventSession.validated');
    Route::post('/event-sessions-store', [EventSessionController::class, 'store'])->name('eventSession.store');

    Route::get('/{id}/ticketing/create',[TicketController::class,'create'])
        ->name('event.tickets.create');
    Route::post('/eventLevels', [EventLevelController::class, 'store'])
        ->name('events.levels.store');
    Route::post('/tickets', [TicketController::class, 'storeTicket'])
        ->name('events.ticket.store');
    Route::post('/ticket-levels', [TicketController::class, 'storeTicketLevel'])
        ->name('events.ticketLevels.store');

});

Route::middleware(['web', 'auth'])->prefix('events')->group(function() {
    Route::get('/', [GeneralEventController::class,'index'])->name('generalEvent.index');

    Route::get('/{id}', [GeneralEventController::class, 'show'])->name('events.show');

    Route::get('/{id}/{section}', [GeneralEventController::class, 'show'])
        ->where('section', 'details|sessions')
        ->name('events.section');

});

// Debug route to check user roles (REMOVE THIS AFTER TESTING)
Route::middleware(['web', 'auth'])->get('/debug-roles', function() {
    $user = auth()->user();
    return response()->json([
        'user_id' => $user->id,
        'user_name' => $user->name ?? $user->first_name . ' ' . $user->last_name,
        'default_role_id' => $user->default_role,
        'default_role' => $user->defaultRole ? $user->defaultRole->name : 'No default role',
        'spatie_roles' => $user->roles->pluck('name')->toArray(),
        'spatie_roles_with_guard' => $user->roles->map(function($role) {
            return ['name' => $role->name, 'guard_name' => $role->guard_name];
        })->toArray(),
        'has_organizer_role' => $user->hasRole('organizer'),
        'has_admin_role' => $user->hasRole('admin'),
        'guard_name' => $user->guard_name,
        'current_guard' => auth()->getDefaultDriver(),
    ]);
});

Route::middleware(['web', 'auth', 'role:admin|organizer'])->group(function() {
    Route::get('/calendar', [GeneralEventController::class,'organizerCalender'])->name('generalEvent.organizerCalender');
});

