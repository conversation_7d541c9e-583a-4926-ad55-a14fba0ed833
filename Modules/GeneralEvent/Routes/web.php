<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
use Illuminate\Support\Facades\Route;

use Modules\GeneralEvent\Http\Controllers\Portal\EventSessionController;
use Modules\GeneralEvent\Http\Controllers\Portal\GeneralEventController;
use Modules\GeneralEvent\Http\Controllers\Portal\LevelController;
use Modules\GeneralEvent\Http\Controllers\Portal\TicketController;
use Modules\GeneralEvent\Http\Controllers\Portal\EventLevelController;


Route::middleware(['web', 'auth'])->prefix('events')->group(function() {
    Route::get('/', [GeneralEventController::class,'index'])->name('generalEvent.index');
    Route::get('/create', [GeneralEventController::class,'create'])->name('generalEvent.create');
    Route::post('/', [GeneralEventController::class, 'store'])->name('generalEvent.store');

    Route::get('/levels', [LevelController::class, 'index'])->middleware(['auth', 'verified'])->name('levels.index');
    Route::post('/levels', [LevelController::class, 'store'])->middleware(['auth', 'verified'])->name('levels.store');
    Route::get('/levels/create', [LevelController::class, 'create'])->middleware(['auth', 'verified'])->name('levels.create');
    Route::put('/levels/{id}', [LevelController::class, 'update'])->middleware(['auth', 'verified'])->name('levels.update');
    Route::get('/levels/delete/{id}', [LevelController::class, 'destroy'])->middleware(['auth', 'verified'])->name('levels.destroy');

    Route::get('/{id}', [GeneralEventController::class, 'show'])->name('events.show');

//    Route::get('/{id}/{section}', [GeneralEventController::class, 'show'])->name('events.section');

    Route::get('/{id}/{section}', [GeneralEventController::class, 'show'])
        ->where('section', 'details|sessions')
        ->name('events.section');

    Route::get('/{id}/edit', [GeneralEventController::class, 'edit'])->name('events.edit');
    Route::post('/{id}/update', [GeneralEventController::class, 'update'])
        ->name('generalEvent.update');

    Route::post('/{id}/deleteEvent', [GeneralEventController::class, 'deleteEvent'])
        ->name('generalEvent.deleteEvent');

    Route::get('/{id}/sessions/create', [EventSessionController::class, 'create'])->middleware(['auth', 'verified'])->name('events.sessions.create');
    Route::get('/{id}/sessions/edit', [EventSessionController::class, 'edit'])->name('events.sessions.edit');
    Route::post('/{id}/sessions/update', [EventSessionController::class, 'update'])->name('events.sessions.update');
    Route::post('/{id}/sessions/delete', [EventSessionController::class, 'deleteSession'])->name('events.sessions.delete');
    Route::post('/{id}/sessions/cancel', [EventSessionController::class, 'cancel'])->name('events.sessions.cancel');

    Route::post('/event-sessions-validated', [EventSessionController::class, 'validated'])->middleware(['auth', 'verified'])->name('eventSession.validated');
    Route::post('/event-sessions-store', [EventSessionController::class, 'store'])->middleware(['auth', 'verified'])->name('eventSession.store');

    Route::get('/{id}/ticketing/create',[TicketController::class,'create'])
    ->middleware(['auth', 'verified'])->name('event.tickets.create');
    Route::post('/eventLevels', [EventLevelController::class, 'store'])
    ->middleware(['auth', 'verified'])->name('events.levels.store');
    Route::post('/tickets', [TicketController::class, 'storeTicket'])
    ->middleware(['auth', 'verified'])->name('events.ticket.store');
    Route::post('/ticket-levels', [TicketController::class, 'storeTicketLevel'])
    ->middleware(['auth', 'verified'])->name('events.ticketLevels.store');

//    // Level Routes - Fixed the duplicate routes
//    Route::get('/levels/index', [LevelController::class, 'index'])->name('levels.index');
////    Route::get('/levels/create', [LevelController::class, 'create'])->name('levels.create');
//    Route::post('/levels', [LevelController::class, 'store'])->name('levels.store');
//    Route::put('/{id}', [LevelController::class, 'update']);
//    Route::get('/levels/delete/{id}', [LevelController::class, 'destroy'])->name('levels.destroy');

    // Publish Event
Route::post('/{id}/publish', [GeneralEventController::class, 'publish'])
        ->name('generalEvent.publish');

Route::post('/{id}/cancel', [GeneralEventController::class, 'cancelEvent'])
        ->name('generalEvent.cancelEvent');
});

Route::middleware(['web', 'auth'])->group(function() {
    Route::get('/calendar', [GeneralEventController::class,'organizerCalender'])->name('generalEvent.organizerCalender');
});

