<?php
namespace Modules\GeneralEvent\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Enums\StatusEnum;
use \Spatie\Tags\Tag;
use Illuminate\Support\Arr;

class GeneralEventRepository extends RepositoriesAbstract
{
    public function __construct(GeneralEvent $model)
    {
        parent::__construct($model);
    }

    public function getAllByUserCategoryPreferences(array $categoryIDs): LengthAwarePaginator
    {
        return $this->model
            ->orderByDesc('id')
            ->where('status',StatusEnum::PUBLISHED)
            ->whereIn('category_id', $categoryIDs)
            ->paginate(request()->limit ?? 10);
    }

    public function getMinMaxEventHours(): array
    {
        return $this->model
            ->selectRaw('MIN(event_hours) as min_hours, MAX(event_hours) as max_hours')
            ->first()
            ->toArray();
    }

    public function syncLevelsToEvent(int $eventId, array $levelData): void
    {
        $event = $this->model->findOrFail($eventId);
        $event->levels()->detach();
        $event->levels()->attach($levelData);
    }

    public function retrieveCreateTicketData(int $eventId)
    {
        $event = $this->model->with(['levels','tickets'])->findOrFail($eventId);
        return $event;
    }

    public function getMinMaxPriceForPublishedEvents(): array
    {
        return (array) DB::table('ticket_level')
            ->join('event_level', 'ticket_level.event_level_id', '=', 'event_level.id')
            ->join('events', 'event_level.event_id', '=', 'events.id')
            ->where('event_level.start_date', '<=', now())
            ->where('event_level.end_date', '>=', now())
            ->where('events.status', '=', StatusEnum::PUBLISHED->value)
            ->select(
                DB::raw('MIN(ticket_level.price) as min_price'),
                DB::raw('MAX(ticket_level.price) as max_price')
            )
            ->first();
    }

    /**
     * Sync both new tags (with translations) and existing tag IDs.
     *
     * @param array $tags
     */
    public function syncEventTags(GeneralEvent $event, array $tags, $type): void
    {
        $tagModels = [];

        $tagIds = array_filter(array_column($tags, 'id'));
        // Fetch all existing tags in one query
        $existingTags = [];
        if (!empty($tagIds)) {
            $existingTags = Tag::whereIn('id', $tagIds)->get()->keyBy('id')->all();
        }

        foreach ($tags as $tagArr) {
            if (isset($tagArr['id']) && isset($existingTags[$tagArr['id']])) {
                $tagModels[] = $existingTags[$tagArr['id']];
            } else {
                $tagName = $tagArr['name'];
                $tag = Tag::findOrCreate(Arr::get($tagName, 'en'), $type);
                // Set the translations
                $tag->setTranslation('name', 'ar', $tagName['ar']); // Set Arabic translation
                $tag->save();
                $tagModels[] = $tag;
            }
        }

        $tagIds = collect($tagModels)->pluck('id')->toArray();
        $event->tags()->sync($tagIds);
    }
}
