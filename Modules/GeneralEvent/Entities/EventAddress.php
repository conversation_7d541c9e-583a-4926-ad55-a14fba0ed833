<?php
namespace Modules\GeneralEvent\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\GeneralEvent\Database\factories\EventAddressFactory;

/**
 * @property integer $id
 * @property string $title
 * @property double $lat
 * @property double $long
 * @property string $city
 * @property string $district
 * @property string $region
 * @property string $street
 * @property string $building_number
 * @property string $venue_name
 * @property integer $event_id
 */
class EventAddress extends BaseModel
{
    use HasFactory;

    protected $guarded = ['id'];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return EventAddressFactory::new();
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(GeneralEvent::class, 'event_id');
    }
}
