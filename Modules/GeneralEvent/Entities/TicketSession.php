<?php
namespace Modules\GeneralEvent\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


/**
 * @property integer $id
 * @property string $name
 * @property string $description
 *
 */

class TicketSession extends BaseModel
{
    use HasFactory;
    protected $guarded = ["id"];


    protected static function newFactory()
    {
        return \Modules\GeneralEvent\Database\factories\TicketSessionFactory::new();
    }

    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }
}
