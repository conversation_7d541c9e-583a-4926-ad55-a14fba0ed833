<?php
namespace Modules\GeneralEvent\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\GeneralEvent\Services\TicketService;

/**
 * @property integer $id
 * @property string $name
 * @property string $description
 *
 */

class Ticket extends BaseModel
{
    use HasFactory;
    protected $guarded = ["id"];


    protected static function newFactory()
    {
        return \Modules\GeneralEvent\Database\factories\TicketFactory::new();
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(GeneralEvent::class);
    }

    public function ticketLevels(): HasMany
    {
        return $this->hasMany(TicketLevel::class);
    }

    public function ticketSessions()
    {
        return $this->belongsToMany(EventSession::class, 'ticket_sessions', 'ticket_id', 'event_session_id')
                    ->withTimestamps();
    }
}
