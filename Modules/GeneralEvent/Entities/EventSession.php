<?php
namespace Modules\GeneralEvent\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Khaleds\Shared\Models\BaseModel;
use Modules\GeneralEvent\Enums\SessionStatusEnum;
use Modules\GeneralEvent\Enums\TypesEnum;
use Modules\User\app\Models\User;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;

/**
 * @property integer $id
 * @property string $name
 * @property string $description
 * @property string $type
 * @property string $status
 *
 */

class EventSession extends BaseModel
{
    use HasFactory;

    protected $guarded = ['id'];
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'status' => SessionStatusEnum::class,
        'type' => TypesEnum::class,
        'session_date' => 'date'
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(GeneralEvent::class, 'event_id');
    }

    public function speakers()
    {
        return $this->belongsToMany(User::class, 'session_speaker', 'event_session_id', 'speaker_id');
    }

    protected static function newFactory()
    {
        return \Modules\GeneralEvent\Database\factories\EventSessionFactory::new();
    }

    public function tickets()
    {
        return $this->belongsToMany(Ticket::class, 'ticket_sessions', 'event_session_id', 'ticket_id')
                    ->withTimestamps();
    }

     public function Type(): BelongsTo
    {
        return $this->belongsTo(Type::class, 'type_id');
    }


    public function getAllowedFilters(): array
    {
        return [
            // Search in translated fields
            AllowedFilter::callback('search', function ($query, $value) {
                $query->where(function ($query) use ($value) {
                    $value = strtolower($value); //to make it non-sensitive
                    $query->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(name, "$.en"))) LIKE ?', ["%{$value}%"])
                        ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(name, "$.ar"))) LIKE ?', ["%{$value}%"]);
                });
            }),
        ];
    }

    public function getAllowedSorts(): array
    {
        return [
            AllowedSort::field('session_date'),
            AllowedSort::field('type'),
            AllowedSort::field('type_id'),
        ];
    }

}
