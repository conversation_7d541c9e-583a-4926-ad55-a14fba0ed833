<?php

namespace Modules\GeneralEvent\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Models\BaseModel;

class Type extends BaseModel
{
    use HasFactory;

    /**
     * @var array
     */
    protected $guarded = ['id'];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
    ];
    protected static function newFactory()
    {
        return \Modules\GeneralEvent\Database\factories\TypeFactory::new();
    }
}
