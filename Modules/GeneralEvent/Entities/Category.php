<?php
namespace Modules\GeneralEvent\Entities;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Modules\User\app\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\Tags\Tag;

/**
 * @property integer $id
 * @property string $name
 * @property string $description
 * @property bool $active
 *
 */

class Category extends BaseModel
{
    use HasFactory;

    protected $guarded = ['id'];
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'active' => 'boolean',
    ];
    protected static function newFactory()
    {
        return \Modules\GeneralEvent\Database\factories\CategoryFactory::new();
    }
    public function events(): HasMany
    {
        return $this->hasMany(GeneralEvent::class, 'category_id');
    }
    public function isInUserPreferences(int $user_id): bool
    {
        return $this->userPreferences()
            ->where('user_id', $user_id)
            ->exists();
    }
    public function userPreferences(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_category_preferences', 'category_id', 'user_id')
            ->withTimestamps();
    }
    public function matchedTags(): HasMany
    {
        return $this->hasMany(Tag::class, 'type', 'id');
    }

    public function getAllowedFilters(): array
    {
        return [
            AllowedFilter::exact('id'),

            AllowedFilter::callback('in_user_preferences', function ($query, $value) {
                if (Auth::check()) {
                    $query->whereHas('userPreferences', function ($query) use ($value) {
                        $query->where('user_id', $value);
                    });
                }
            }),
        ];
    }
}
