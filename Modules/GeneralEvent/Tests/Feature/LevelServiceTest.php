<?php

namespace Modules\GeneralEvent\Tests\Feature;

use Tests\TestCase;
use Modules\GeneralEvent\Entities\Level;
use Modules\GeneralEvent\Services\LevelService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Modules\User\app\Models\User;

class LevelServiceTest extends TestCase
{
    use WithFaker;

    protected User $user;
    protected LevelService $levelService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
        
        // Instantiate the service
        $this->levelService = app(LevelService::class);
    }

    /** @test */
    public function it_can_create_level_with_translations()
    {
        // Arrange
        $data = [
            'name' => [
                'ar' => 'تابتابلت',
                'en' => 'test',
            ],
            'user_id' => $this->user->id,
        ];

        // Act
        $result = $this->levelService->create($data);

        // Assert
        $this->assertInstanceOf(Level::class, $result);
        $this->assertEquals('test', $result->getTranslation('name', 'en'));
        $this->assertEquals('تابتابلت', $result->getTranslation('name', 'ar'));
        $this->assertEquals($this->user->id, $result->user_id);
    }

    /** @test */
     public function it_stores_translations_in_json_format()
     {
         // Arrange
         $data = [
             'name' => [
                 'ar' => 'تابتابلت',
                 'en' => 'test',
             ],
             'user_id' => $this->user->id,
         ];

         // Act
         $result = $this->levelService->create($data);

         // Assert
         $this->assertDatabaseHas('levels', [
            'id' => $result->id,
            'user_id' => $this->user->id,
         ]);

        // Get raw data from database to check JSON structure
         $rawLevel = DB::table('levels')->where('id', $result->id)->first();
         $nameArray = json_decode($rawLevel->name, true);
        
         $this->assertIsArray($nameArray);
         $this->assertEquals('test', $nameArray['en']);
         $this->assertEquals('تابتابلت', $nameArray['ar']);
     }

    /** @test */
    public function it_creates_level_with_correct_user_id()
    {
        // Arrange
        $data = [
            'name' => [
                'ar' => 'تابتابلت',
                'en' => 'test',
            ],
            'user_id' => $this->user->id,
        ];

        // Act
        $result = $this->levelService->create($data);

        // Assert
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertDatabaseHas('levels', [
            'id' => $result->id,
            'user_id' => $this->user->id,
        ]);
    }

    /** @test */
    public function it_returns_correct_model_instance()
    {
        // Arrange
        $data = [
            'name' => [
                'ar' => 'تابتابلت',
                'en' => 'test',
            ],
            'user_id' => $this->user->id,
        ];

        // Act
        $result = $this->levelService->create($data);

        // Assert
        $this->assertInstanceOf(Level::class, $result);
        $this->assertNotNull($result->id);
        $this->assertTrue($result->exists);
    }

    /** @test */
    public function it_creates_level_with_default_values()
    {
        // Arrange
        $data = [
            'name' => [
                'ar' => 'تابتابلت',
                'en' => 'test',
            ],
            'user_id' => $this->user->id,
        ];

        // Act
        $result = $this->levelService->create($data);

        // Assert
        $this->assertDatabaseHas('levels', [
            'id' => $result->id,
            'user_id' => $this->user->id,
        ]);
    }

    /** @test */
    public function it_can_handle_additional_attributes()
    {
        // Arrange
        $data = [
            'name' => [
                'ar' => 'تابتابلت',
                'en' => 'test',
            ],
            'user_id' => $this->user->id,
            'is_active' => false, // Additional attribute
        ];

        // Act
        $result = $this->levelService->create($data);

        // Assert
        $this->assertFalse($result->is_active);
        $this->assertDatabaseHas('levels', [
            'id' => $result->id,
            'is_active' => false,
        ]);
    }
}