<?php

namespace Modules\Tests\Feature;

use Modules\GeneralEvent\Entities\EventSession;
use Tests\TestCase;
use Modules\GeneralEvent\Services\TicketService;

class TicketServiceTest extends TestCase
{
    protected TicketService $ticketService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->ticketService = app(TicketService::class);
    }

    /** @test */
    public function it_can_attach_levels_to_an_event()
    {
        // Create 2 event sessions and get their IDs
        $event_sessions = EventSession::factory()->count(2)->create();
        $sessionIds = $event_sessions->pluck('id')->toArray(); // Extract IDs into an array
    
        $data = [
            'event_id' => $event_sessions->first()->event->id,
            'name' => 'name',
            'description' => 'desc',
            'main_price' => 12000, // Remove the comma to avoid syntax error
            'seats_count' => 120,
            'start_date' => now()->addDays(2)->format('Y-m-d'),
            'end_date' => now()->addDays(5)->format('Y-m-d'),
            'sessions' => $sessionIds, // Use dynamically retrieved IDs
        ];
    
        // Create a ticket
        $ticket = $this->ticketService->create($data);
    
        // Sync the sessions to the ticket
        $this->ticketService->syncSessionToTicket($ticket->id, $data['sessions']);
    
        // Assert that the ticket sessions were attached successfully
        $this->assertDatabaseHas('ticket_sessions', [
            'ticket_id' => $ticket->id,
            'event_session_id' => $sessionIds[0], // Ensure the first session is attached
        ]);
    }
}
