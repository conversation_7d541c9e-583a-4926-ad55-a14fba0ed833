<?php

namespace Modules\GeneralEvent\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Enums\StatusEnum;

class PublishScheduledEventsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:publish-scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish events that have reached their scheduled publish date';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $this->info('Looking for events to publish...');
            Log::info('PublishScheduledEvents: Looking for events to publish...');

            // Find events that:
            // 1. Are in Draft status
            // 2. Have a publish_date that has been reached
            $events = GeneralEvent::where('status', StatusEnum::DRAFT)
                ->whereNotNull('publish_date')
                ->where('publish_date', '<=', now())
                ->get();

            $count = $events->count();
            
            if ($count === 0) {
                $this->info('No scheduled events to publish at this time.');
                Log::info('PublishScheduledEvents: No scheduled events to publish at this time.');
                return Command::SUCCESS;
            }

            Log::info("PublishScheduledEvents: Found {$count} events to publish.");

            foreach ($events as $event) {
                // Update the event status to published
                $event->status = StatusEnum::PUBLISHED;
                $event->save();
                
                // Safely extract the event name for display
                $eventName = "Unknown";
                if (!empty($event->name)) {
                    $decodedName = json_decode($event->name);
                    if (is_object($decodedName) && isset($decodedName->en)) {
                        $eventName = $decodedName->en;
                    } elseif (is_string($event->name)) {
                        $eventName = $event->name;
                    }
                }
                
                $this->info("Published event: {$event->id} - {$eventName}");
                Log::info("PublishScheduledEvents: Published event: {$event->id} - {$eventName}");
            }

            $this->info("Successfully published {$count} events.");
            Log::info("PublishScheduledEvents: Successfully published {$count} events.");
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred while publishing events: {$e->getMessage()}");
            Log::error("PublishScheduledEvents: Error: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
