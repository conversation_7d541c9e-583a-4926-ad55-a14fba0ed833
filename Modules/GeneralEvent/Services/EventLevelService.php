<?php
namespace Modules\GeneralEvent\Services;

use Khaleds\Shared\Services\ServiceAbstract;
use Modules\GeneralEvent\Repositories\EventLevelRepository;
use Modules\GeneralEvent\Repositories\GeneralEventRepository;

class EventLevelService extends ServiceAbstract
{
    public function __construct(EventLevelRepository $repository , private GeneralEventRepository $eventRepository)
    {
        parent::__construct($repository);
    }

    /**
     * Sync levels for an event.
     *
     * @param int $eventId
     * @param array $levelData
     * @return bool
     */

    

    public function syncLevelsToEvent(int $eventId , array $levelData)
    {
        $data = $this->eventRepository->syncLevelsToEvent($eventId, $levelData);
        return $data;
    }
}
