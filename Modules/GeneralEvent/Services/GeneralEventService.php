<?php
namespace Modules\GeneralEvent\Services;

use App\Enums\MediaEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Enums\StatusChangedByEnum;
use Modules\GeneralEvent\Enums\StatusEnum;
use Modules\GeneralEvent\Jobs\NotifyEventPublished;
use Modules\GeneralEvent\Repositories\EventLevelRepository;
use Modules\GeneralEvent\Repositories\EventSessionRepository;
use Modules\GeneralEvent\Repositories\GeneralEventRepository;
use Modules\GeneralEvent\Repositories\TicketRepository;
use Modules\Order\Repositories\OrderRepository;
use Carbon\Carbon;
use Spatie\QueryBuilder\QueryBuilder;

class GeneralEventService extends ServiceAbstract
{
    public function __construct(GeneralEventRepository $repository , private EventSessionRepository $eventSessionRepository , private TicketRepository $ticketRepository , private EventLevelRepository $eventLevelRepository , public OrderRepository $orderRepository)
    {
        parent::__construct($repository);
    }

    public function paginateAllEventsByUserCategoryPreferences(array $categoryIDs){

        return $this->repository->getAllByUserCategoryPreferences($categoryIDs);
    }

    public function getMinMaxEventHours(): array
    {
        return $this->repository->getMinMaxEventHours();
    }

    public function getMinMaxPriceForPublishedEvents(): array
    {
        return $this->repository->getMinMaxPriceForPublishedEvents();
    }

    public function create(array $data): Model
    {
        // Create event
        $model = parent::create($data);
        // Create address if any address field is filled
        $model->address()->create(collect($data)->only([
            'title', 'venue_name' ,'district', 'city', 'region', 'street',
            'building_number', 'lat', 'long'
        ])->toArray());
        //sync tags
        if (isset($data['tags'])) {
            $this->syncEventTags($model, $data['tags']);
        }
        if(array_key_exists('cover_image',$data)) {
            $model->addMediaFromRequest('cover_image')->toMediaCollection(MediaEnum::EVENT_COLLECTION->value);
        }
        return $model;
    }

    /**
     * @throws \Exception
     */
    public function update(array $data, Model $model): bool
    {
        try {
            DB::beginTransaction();
            if ($model->status != StatusEnum::DRAFT) { //edit in name & description only
                $updatableData = collect($data)->only(['name', 'description'])->toArray();
                parent::update($updatableData, $model);
            } else {
                parent::update($data, $model);
                // update address if any address field is filled
                $addressData = collect($data)->only([
                    'title', 'venue_name','district', 'city', 'region', 'street',
                    'building_number', 'lat', 'long'
                ]);
                if ($addressData->isNotEmpty()) {
                    $model->address->update($addressData->toArray());
                }
                //sync tags
                if (isset($data['tags'])) {
                    $this->syncEventTags($model, $data['tags']);
                }
                //update media
                if(isset($data['cover_image'])) {
                    $model->clearMediaCollection(MediaEnum::EVENT_COLLECTION->value);
                    $model->addMediaFromRequest('cover_image')->toMediaCollection(MediaEnum::EVENT_COLLECTION->value);
                }
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public function deleteEvent(int $eventId): bool
    {
        $event = $this->repository->findOrFail($eventId);
        if ($event->status != StatusEnum::DRAFT) {
            throw new \Exception(__('This event can not be deleted!'));
        }
        return $this->repository->delete($event);
    }

    public function publish(Model $model): bool
    {
        if (!$this->isPublishable($model)) {
            throw new \Exception(__('Event is not publishable, verify the event data such as tickets and sessions are completed'));
        }

        // Use the user-defined publish_date or current date if not set
        $publishDate = $model->publish_date ?? now();

        // If publish date is in the future, keep status as DRAFT
        if ($publishDate->isFuture()) {
            $publishDate = now();
        }

        // Otherwise publish immediately
        $data = ['status' => StatusEnum::PUBLISHED->value, 'publish_date' => $publishDate->toDateTimeString()];
        if(parent::update($data, $model)) {
            $this->notifyPublished($model);
            return true;
        }
        return false;
    }

    public function isPublishable(Model $model): bool
    {
        $event = $model->load('tickets', 'sessions');
        if ($event->tickets->count() == 0 || $event->sessions->count() == 0)
        {
            return false;
        }
        return true;
    }

    public function notifyPublished(Model $model): void
    {
        NotifyEventPublished::dispatch($model);
    }

    public function getEventTicketsWithLevels($eventId)
    {
        $event = $this->repository->getAllByQuery(['id' => $eventId] , ['tickets' , 'levels'])->first();
        $tickets = $event->tickets;
        $eventLevels = $event->eventLevels;

        return $tickets->map(function ($ticket) use ($eventLevels) {
            return [
                'ticket_id' => $ticket->id,
                'ticket_name' => $ticket->name,
                'ticket_description' => $ticket->description,
                'main_price' => $ticket->main_price,
                'seats_count' => $ticket->seats_count,
                'levels' => $eventLevels->map(function ($eventLevel) {
                    return [
                        'id' => $eventLevel->id,
                        'name' => $eventLevel->level->getTranslations('name'),
                        'level_id' => $eventLevel->level_id,
                        'start_date' => $eventLevel->start_date,
                        'end_date' => $eventLevel->end_date
                    ];
                })->toArray()
            ];
        })->toArray();
    }

    public function getLevelsWithTickets($eventId)
    {
        $event = $this->repository->getAllByQuery(['id' => $eventId], [
            'eventLevels.ticketLevels.ticket.ticketSessions', // Load ticketSessions for each ticket
            'sessions' // Load all event sessions
        ])->first();

        $eventLevels = $event->eventLevels; // Get event levels
        $eventSessions = $event->sessions; // Get all sessions related to the event

        return $eventLevels->map(function ($eventLevel) use ($eventSessions) {
            $ticketLevels = $eventLevel->ticketLevels; // Get related ticket levels for the event level

            return [
                'id' => $eventLevel->id,
                'name' => $eventLevel->level->getTranslations('name'),
                'level_id' => $eventLevel->level_id,
                'start_date' => $eventLevel->start_date,
                'end_date' => $eventLevel->end_date,
                'tickets' => $ticketLevels->map(function ($ticketLevel) use ($eventSessions) {
                    $name = $ticketLevel->ticket->getTranslations('name');
                    $description = $ticketLevel->ticket->getTranslations('description');
                    $mainPrice = $ticketLevel->ticket->main_price;
                    $price = $ticketLevel->price;

                    // Calculate discount percentage
                    $discountPercentage = ($ticketLevel->price >= $ticketLevel->ticket->main_price) ? null :
                        (int)round((($ticketLevel->ticket->main_price - $ticketLevel->price) / $ticketLevel->ticket->main_price) * 100);
                    // Get sessions related to the ticket
                    $ticketSessions = $ticketLevel->ticket->ticketSessions->pluck('id')->toArray();

                    return [
                        'ticket_level_id' => $ticketLevel->id,
                        'ticket_id' => $ticketLevel->ticket_id,
                        'count' => $ticketLevel->count,
                        'main_price' => $mainPrice,
                        'name' => $name,
                        'description' => $description,
                        'price' => $price,
                        'discount_percentage' => $discountPercentage,
                        'created_at' => $ticketLevel->created_at,
                        'updated_at' => $ticketLevel->updated_at,
                        'sessions' => $eventSessions->map(function ($session) use ($ticketSessions) {
                            return [
                                'session_id' => $session->id,
                                'session_name' => $session->name,
                                'start_time' => $session->start_time,
                                'end_time' => $session->end_time,
                                'is_available' => in_array($session->id, $ticketSessions),
                            ];
                        })->toArray()
                    ];
                })->toArray()
            ];
        })->toArray();
    }

    public function getEventSessions($event_id)
    {
        return $this->eventSessionRepository->getAllBy(['event_id' => $event_id]);
    }

    public function retrieveCreateTicketData(int $event_id)
    {
        $event = $this->repository->retrieveCreateTicketData($event_id);
        $existingLevels = $event->levels->map(function ($level) {
            return [
                'level_id' => [
                    'value' => $level->id,
                    'label' => $level->getTranslations('name'),
                ],
                'start_date' => $level->pivot->start_date,
                'end_date' => $level->pivot->end_date,
            ];
        });

        $existingTickets = $event->tickets->map(function ($ticket) {
            return [
                'name' => $ticket->getTranslations('name'),
                'seats_count' => $ticket->seats_count,
                'main_price' => $ticket->main_price,
                'description' => $ticket->getTranslations('description'),
                'sessions' => $ticket->ticketSessions->pluck('id')->toArray()
            ];
        });
        $ticketLevelsData = $event->eventLevels->map(function ($eventLevel) {
            return [
                'id' => $eventLevel->id,
                'event_id' => $eventLevel->event_id,
                'level' => [
                    'id' => $eventLevel->level->id,
                    'name' => $eventLevel->level->getTranslations('name')
                ],
                'start_date' => $eventLevel->start_date,
                'end_date' => $eventLevel->end_date,
                'ticket_levels' => $eventLevel->ticketLevels->map(function ($ticketLevel) use ($eventLevel) {
                    return [
                        'ticket_level_id' => $ticketLevel->id,
                        'ticket' => [
                            'id' => $ticketLevel->ticket->id,
                            'name' => $ticketLevel->ticket->getTranslations('name'),
                            'main_price' => $ticketLevel->ticket->main_price
                        ],
                        'discount_percentage' => ($ticketLevel->price >= $ticketLevel->ticket->main_price) ? null :
                        (int)round((($ticketLevel->ticket->main_price - $ticketLevel->price) / $ticketLevel->ticket->main_price) * 100),
                        'count' => $ticketLevel->count,
                        'main_price' => $ticketLevel->ticket->main_price,
                        'discount_price' => $ticketLevel->price,
                        'sessions' => $eventLevel->event->sessions->map(function($session) use ($ticketLevel) {
                            $isAvailable = $ticketLevel->ticket->ticketSessions
                                ->where('id', $session->id)
                                ->first() !== null;

                            return [
                                'id' => $session->id,
                                'name' => $session->getTranslations('name'),
                                'description' => $session->description,
                                'type' => $session->type,
                                'status' => $session->status,
                                'session_date' => date($session->session_date),
                                'start_time' => Carbon::parse($session->start_time)->format('Y-m-d H:i:s'),
                                'end_time' => Carbon::parse($session->end_time)->format('Y-m-d H:i:s'),
                                'link' => $session->link,
                                'type' => [
                                    'id' => $session->Type->id,
                                    'name' => $session->Type->getTranslations('name')
                                ],
                                'speakers' => $session->speakers->map(function($speaker) {
                                    return [
                                        'id' => $speaker->id,
                                        'name' => $speaker->name
                                    ];
                                }),
                                'is_available' => $isAvailable
                            ];
                        })
                    ];
                })
            ];
        });
        return [
            'event' => $event,
            'existingLevels' => $existingLevels,
            'existingTickets' => $existingTickets,
            'ticketLevelsData' => $ticketLevelsData
        ];
    }

    public function getAttendeeCalender()
    {
        $orders = $this->orderRepository->getAllBy(['attendee_id' => auth()->user()->id] , ['event']);
        return $orders;
    }

    public function getAttendeeEvents(int $attendeeID)
    {
        $query = GeneralEvent::where('status','!=', StatusEnum::DRAFT->value)
            ->whereHas('orders', fn($query) => $query->where('attendee_id', $attendeeID));

        return QueryBuilder::for($query)
            ->allowedFilters((new GeneralEvent())->getAllowedFilters())
            ->get();
    }

    public function organizerCalender($user)
    {
       return $this->repository->getAllByQuery(['organizer_id' => $user->id] , ['sessions'])->get();
    }

    public function getEvent ($eventId)
    {
        return $this->repository->getAllByQuery(['id' => $eventId],[],['start_date','name'])->first();
    }

    public function syncEventTags(GeneralEvent $event, array $tags): void
    {
        $tagType = $event->category->id; //as tag type
        $this->repository->syncEventTags($event, $tags, $tagType);
    }

    public function cancel(Model $model): array
    {
        $matchedEnum = null;

        if($model->status != StatusEnum::PUBLISHED){
            return ['state'=>false,'message'=>__('Only published events can be canceled.')];
        }
        if(in_array(auth()->user()->defaultRole->name,StatusChangedByEnum::values())){
            $matchedEnum = StatusChangedByEnum::tryFrom(auth()->user()->defaultRole->name)->value;
        }
        // Otherwise publish immediately
        $data = ['status' => StatusEnum::CANCELED->value,'status_changed_by'=>$matchedEnum];
        if(parent::update($data, $model)) {
            return ['state'=>true,'message'=>__('Event canceled successfully!')];
        }
        return ['state'=>false,'message'=>__('Failed to cancel event.')];
    }

    public function findOrFail(int $id ,array $filter = [],array $with=[]){
        
        return $this->repository->findOrFail($id , [],['*'],$with);

    }
}
