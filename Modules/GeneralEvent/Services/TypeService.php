<?php
namespace Modules\GeneralEvent\Services;

use Khaleds\Shared\Services\ServiceAbstract;
use Modules\GeneralEvent\Repositories\TypeRepository;

class TypeService extends ServiceAbstract
{
    public function __construct(TypeRepository $repository)
    {
        parent::__construct($repository);
    }

    public function getAllTypes()
    {
        $data = $this->repository->getAllByQuery()->get();
        return $data;
    }
}
