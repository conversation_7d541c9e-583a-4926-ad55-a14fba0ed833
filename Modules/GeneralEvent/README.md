# General Event Module

A Laravel module for managing events, categories, and user preferences using a standardized CRUD API approach.

## Overview

This module provides functionality for:
- Managing general events (CRUD operations)
- Event categories
- Event addresses
- Event sessions
- User event preferences

## Architecture

The module follows a service-Repository-based architecture with:
- Abstract Controller for standardized CRUD operations
- Service layer for business logic
- Repository layer for database queries
- API Resources for data transformation
- Request validation classes

### Core Components
- Controllers
- Services
- Repositories
- Unit Test
- Feature Test

## Frontend Guide (Inertia.js)
### Basic Setup

The controller automatically handles standard CRUD operations with these standard pages:
```
YourResource/
├── Index.vue // List view
├── Create.vue // Create form
├── Edit.vue // Edit form
└── Show.vue // Details view
```

### Quick Examples

#### List Page (Index.vue)
```vue
<template>
  <div>
    <!-- Data from controller -->
    <div v-for="item in resources.data" :key="item.id">
      {{ item.name }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    resources: Object // Automatically provided by controller
  }
}
</script>
```

### Common Routes
- List: /your-resource
- Create: /your-resource/create
- Edit: /your-resource/{id}/edit
- Show: /your-resource/{id}
