<?php

namespace Modules\GeneralEvent\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Mail\EventPublishedNotification;

class NotifyEventPublished implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected GeneralEvent $event;

    public function __construct(GeneralEvent $event)
    {
        $this->event = $event;
    }

    public function handle(): void
    {
        $participantCollections = collect($this->getEventParticipants($this->event));
        if ($participantCollections->isNotEmpty()) {
            foreach ($participantCollections as $participantCollection) {
                Mail::to($participantCollection['email'])->locale($participantCollection['lang'] ?? 'en')->send(new EventPublishedNotification($this->event));
            }
        }
    }

    public function failed(\Throwable $exception)
    {
        Log::error('Failed to send event is published notifications', [
            'event' => $this->event->id,
            'error' => $exception->getMessage()
        ]);
    }

    protected function getEventParticipants($event): array
    {
        $eventSessionSpeakers = $event->load('sessions.speakers');
        $speakers = $eventSessionSpeakers->sessions->pluck('speakers')->flatten();
        $participants = $speakers->push($event->organizer);
        return $participants->unique('id')->values()->toArray();
    }
}
