<?php

namespace Modules\GeneralEvent\DataTables;

use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Modules\Review\Entities\Review;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Services\DataTable;

use Modules\GeneralEvent\Entities\GeneralEvent;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Khaleds\Shared\Helpers\Datatables\ActionButton;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Khaleds\Shared\Helpers\Datatables\DatatableConfiguration;

class eventsReviewDataTable extends DataTable
{

    use DatatableConfiguration;

    public string $tableId = 'eventsReview';

    protected $eventId;

    public function setEventId($eventId)
    {
        $this->eventId = $eventId;
        return $this;
    }

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {

        return (new EloquentDataTable($query))
            ->editColumn('user_id', function ($model) {
                return $model->user->fullName;
            })
            ->filterColumn('user_id', function($query, $keyword) {
                $query->whereHas('user', function($q) use ($keyword) {
                    $q->where('first_name', 'like', "%{$keyword}%")
                      ->orWhere('last_name', 'like', "%{$keyword}%");
                });
            })
            ->orderColumn('user_id', function($query, $order) {
                $query->join('users', 'users.id', '=', 'reviews.user_id')
                ->orderBy('users.first_name', $order)
                ->orderBy('users.last_name', $order);

            })
            ->editColumn('is_active', function ($model) {
                if ($model->is_active) {
                    return '<i class="ki-outline ki-check-circle h2 text-success"></i>';
                }
                return '<i class="ki-outline ki-cross-circle h2 text-danger"></i>';
            })
            
            
        ->setRowId('id')
        ->rawColumns(['is_active']);
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(Review $model): QueryBuilder
    {
        $query = $model->newQuery();
        
        if ($this->eventId) {
            $query->where(
                [
                    ['rated_type','=',GeneralEvent::class],
                    ['rated_id','=',$this->eventId]
                ]
            );
        }

        
        return $query;
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->config();
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')
                ->title(__('generalevent::messages.view.table.id')),
            Column::make('user_id')
                ->title(__('generalevent::messages.view.table.userName')),
            Column::make('rate')
                ->title(__('generalevent::messages.view.table.rate')),
            Column::make('is_active')
                ->title(__('generalevent::messages.view.table.active')),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'events_' . date('YmdHis');
    }
}
