<?php

namespace Modules\GeneralEvent\DataTables;

use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Khaleds\Shared\Helpers\Datatables\ActionButton;
use Khaleds\Shared\Helpers\Datatables\DatatableConfiguration;
use Modules\GeneralEvent\Entities\Category;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class categoriesDataTable extends DataTable
{

    use DatatableConfiguration;

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', 'categories.action')
            ->setRowId('id')
            ->addColumn('name', function ($model) {
                return $model->name;
            })
            ->addColumn('description', function ($model) {
                return strip_tags($model->description ?? '_');
            })
            ->editColumn('active', function ($model) {
                if ($model->active) {
                    return '<i class="ki-outline ki-check-circle h2 text-success"></i>';
                }
                return '<i class="ki-outline ki-cross-circle h2 text-danger"></i>';
            })
            ->editColumn('created_at', function ($model) {
                return $model->created_at?->format('Y-m-d H:i');
            })
            ->editColumn('updated_at', function ($model) {
                return $model->updated_at?->format('Y-m-d H:i');
            })
        ->addColumn('actions', function ($model) {
        return view('theme::admin.components.datatable.actions')
            ->with([
                "actions" => [
                    ActionButton::make('Edit')
                        ->url(route('admin.categories.edit', $model->id)),

                    ActionButton::make('View')
                        ->url(route('admin.categories.show', $model->id)),
                ]
            ])
            ->render();
    })
        ->rawColumns(['checkbox','actions','active'])
        ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(Category $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->config();
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [

            Column::make('id')
                ->title(__('user::messages.model.ID')),
            Column::make('name')
                ->title(__('generalevent::messages.model.Name'))
                ->searchable(true),
            Column::make('description')
                ->title(__('generalevent::messages.model.Description'))
                ->searchable(true),
            Column::make('active')
                ->title(__('user::messages.model.Status')),
            Column::make('created_at')
                ->title(__('general.Created At')),
            Column::make('updated_at')
                ->title(__('general.Updated At')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(120)
                ->addClass('text-center')
                ->title(__('general.Actions'))
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'categories' . date('YmdHis');
    }
}
