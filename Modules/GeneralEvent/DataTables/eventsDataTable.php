<?php

namespace Modules\GeneralEvent\DataTables;

use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Support\Str;
use Khaleds\Shared\Helpers\Datatables\ActionButton;
use Khaleds\Shared\Helpers\Datatables\DatatableConfiguration;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class eventsDataTable extends DataTable
{

    use DatatableConfiguration;

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {

        return (new EloquentDataTable($query))
            ->addColumn('checkbox', function ($model) {
                return view('theme::admin.components.datatable.checkbox', ['id' => $model->id])->render();
            })

            ->setRowId('id')
            ->addColumn('action', 'events.action')
            ->setRowId('id')
            ->addColumn('name', function ($model) {
                return $model->name;
            })
            ->addColumn('description', function ($model) {
                $description = strip_tags($model->description ?? '_');
                return Str::limit($description, 25, '...');
            })
            ->addColumn('type', function ($model) {
                return $model->type;
            })
            ->addColumn('status', function ($model) {
                return $model->status;
            })
            ->addColumn('organizer', function ($model) {
                return $model->organizer?->first_name ?? '-';
            })
            ->addColumn('category', function ($model) {
                return $model->category->name;
            })
            ->editColumn('start_date', function ($model) {
                return $model->start_date?->format('Y-m-d H:i');
            })
            ->editColumn('end_date', function ($model) {
                return $model->end_date?->format('Y-m-d H:i');
            })
            ->editColumn('created_at', function ($model) {
                return $model->created_at?->format('Y-m-d H:i');
            })
            ->editColumn('updated_at', function ($model) {
                return $model->updated_at?->format('Y-m-d H:i');
            })

        ->addColumn('actions', function ($model) {
        return view('theme::admin.components.datatable.actions')
            ->with([
                "actions" => [
                    ActionButton::make('Edit')
                        ->url(route('admin.events.edit', $model->id)),

                    ActionButton::make('View')
                        ->url(route('admin.events.show', $model->id)),
                ]
            ])
            ->render();
    })
        ->rawColumns(['checkbox','actions'])
        ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(GeneralEvent $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->config();
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::computed('checkbox')
                ->exportable(false)
                ->printable(false)
                ->width(20)
                ->addClass('text-center')
                ->title('<div class="form-check form-check-sm form-check-custom form-check-solid me-3">
              <input class="form-check-input header-checkbox" type="checkbox" value="1" />
            </div>'),
            Column::make('id')
                ->title(__('user::messages.model.ID')),
            Column::make('name')
                ->title(__('generalevent::messages.model.Name'))
                ->searchable(true),
            Column::make('description')
                ->title(__('generalevent::messages.model.Description'))
                ->searchable(true),
            Column::make('type')->title(__('generalevent::messages.view.table.type')),
            Column::make('status')->title(__('generalevent::messages.view.table.status')),
            Column::make('organizer')->title(__('generalevent::messages.view.organizer')),
            Column::make('category')->title(__('generalevent::messages.view.category')),
            Column::make('start_date')->title(__('generalevent::messages.view.table.startDate')),
            Column::make('end_date')->title(__('generalevent::messages.view.table.endDate')),
            Column::make('created_at')->title(__('general.Created At')),
            Column::make('updated_at')->title(__('general.Updated At')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(120)
                ->addClass('text-center')
                ->title(__('general.Actions'))
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'events_' . date('YmdHis');
    }
}
