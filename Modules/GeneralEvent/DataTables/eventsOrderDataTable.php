<?php

namespace Modules\GeneralEvent\DataTables;

use Illuminate\Support\Str;
use Modules\Order\Entities\Order;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Khaleds\Shared\Helpers\Datatables\ActionButton;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Khaleds\Shared\Helpers\Datatables\DatatableConfiguration;

class eventsOrderDataTable extends DataTable
{

    use DatatableConfiguration;

    public string $tableId = 'eventOrder';

    protected $eventId;

    public function setEventId($eventId)
    {
        $this->eventId = $eventId;
        return $this;
    }

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {

        return (new EloquentDataTable($query))
            ->editColumn('attendee_id', function ($model) {
                return $model->attendee->fullName;
            })
            ->filterColumn('attendee_id', function($query, $keyword) {
                $query->whereHas('attendee', function($q) use ($keyword) {
                    $q->where('first_name', 'like', "%{$keyword}%")
                      ->orWhere('last_name', 'like', "%{$keyword}%");
                });
            })
            ->orderColumn('attendee_id', function($query, $order) {
                $query->join('users', 'users.id', '=', 'orders.attendee_id')
                ->orderBy('users.first_name', $order)
                ->orderBy('users.last_name', $order);

            })
        ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(Order $model): QueryBuilder
    {
        $query = $model->newQuery();
        
        if ($this->eventId) {
            $query->where('event_id', $this->eventId);
        }
        
        return $query;
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->config();
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')
                ->title(__('generalevent::messages.view.table.id')),
            Column::make('attendee_id')
                ->title(__('generalevent::messages.view.table.userName')),
            Column::make('total_price')
                ->title(__('generalevent::messages.view.table.totalPrice')),
            Column::make('status')
                ->title(__('generalevent::messages.view.table.status')),
            Column::make('payment_status')
                ->title(__('generalevent::messages.view.table.paymentStatus')),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'events_' . date('YmdHis');
    }
}
