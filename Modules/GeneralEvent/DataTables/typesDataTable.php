<?php

namespace Modules\GeneralEvent\DataTables;

use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Khaleds\Shared\Helpers\Datatables\ActionButton;
use Khaleds\Shared\Helpers\Datatables\DatatableConfiguration;
use Modules\GeneralEvent\Entities\Type;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class typesDataTable extends DataTable
{

    use DatatableConfiguration;

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', 'types.action')
            ->setRowId('id')
            ->addColumn('name', function ($model) {
                return $model->name;
            })
            ->addColumn('description', function ($model) {
                return strip_tags($model->description ?? '_');
            })
            ->editColumn('created_at', function ($model) {
                return $model->created_at?->format('Y-m-d H:i');
            })
            ->editColumn('updated_at', function ($model) {
                return $model->updated_at?->format('Y-m-d H:i');
            })

        ->addColumn('actions', function ($model) {
        return view('theme::admin.components.datatable.actions')
            ->with([
                "actions" => [
                    ActionButton::make('Edit')
                        ->url(route('admin.types.edit', $model->id)),

                    ActionButton::make('View')
                        ->url(route('admin.types.show', $model->id)),
                ]
            ])
            ->render();
    })
        ->rawColumns(['checkbox','actions'])
        ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(Type $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->config();
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [

            Column::make('id')
                ->title(__('user::messages.model.ID')),
            Column::make('name')
                ->title(__('generalevent::messages.model.Name')),
            Column::make('description')
                ->title(__('generalevent::messages.model.Description')),
            Column::make('created_at')
                ->title(__('general.Created At')),
            Column::make('updated_at')
                ->title(__('general.Updated At')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(120)
                ->addClass('text-center')
                ->title(__('general.Actions'))
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'types_' . date('YmdHis');
    }
}
