<?php

namespace Modules\GeneralEvent\Enums;

enum SessionStatusEnum: string
{
    case PENDING = 'pending';
    case ACTIVE = 'active';
    case ENDED = 'ended';

    case CANCELED = 'canceled';

    public function label(): string
    {
        return match($this) {
            self::PENDING => __('Pending'),
            self::ACTIVE => __('Active'),
            self::ENDED => __('Ended'),
            self::CANCELED => __('Canceled'),
        };
    }

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }
}
