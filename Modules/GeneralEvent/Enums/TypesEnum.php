<?php

namespace Modules\GeneralEvent\Enums;

enum TypesEnum: string
{
    case OFFLINE = 'offline';
    case ONLINE = 'online';
    case HYBRID = 'hybrid';

    public function label(): string
    {
        return match($this) {
            self::OFFLINE => __('Offline'),
            self::ONLINE => __('Online'),
            self::HYBRID => __('Hybrid'),
        };
    }

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }
}
