<?php
namespace Modules\GeneralEvent\Http\Resources\Api;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\app\Http\Resources\Api\UserResource;

class EventSessionResource extends JsonResource
{
    public function __construct($resource)
    {
        parent::__construct($resource);
    }


    public function toArray($request)
    {

        $data = [
            "id" => $this->id,
            "name" => $this->name,
            "description" => $this->description,
            "type" => $this->type,
            "status" => $this->status,
            "session_date" => date($this->session_date),
            "start_time" => Carbon::parse($this->start_time)->format('Y-m-d H:i:s'),
            "end_time" => Carbon::parse($this->end_time)->format('Y-m-d H:i:s'),
            "link" => $this->link,
            "metaData" => json_decode($this->metadata, true),
            "category" => TypeResource::make($this->Type),
            "speakers" => UserResource::collection($this->speakers),
            "is_available" => $this->whenHas('is_available', function () {
                return $this->is_available;
            }),
        ];

        return $data;
    }
}
