<?php
namespace Modules\GeneralEvent\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class EventAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "title" => $this->title,
            "lat" => $this->lat,
            "long" => $this->long,
            "city" => $this->city,
            "district" => $this->district,
            "region" => $this->region,
            "street" => $this->street,
            "building_number" => $this->building_number,
            "event_id" => $this->event_id,
        ];

        return $data;
    }
}
