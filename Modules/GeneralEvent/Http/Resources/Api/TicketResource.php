<?php

namespace Modules\GeneralEvent\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        
        $data = [
            "id"            =>   $this->id,
            "name"          =>   $this->name,
            "description"   =>   $this->description,
            "main_price"    =>   $this->main_price,
            "seats_count"   =>   $this->seats_count,
            'created_at'    =>   date($this->created_at),
            'end_date'      =>   $this->event_level_end_date
        ];

        return $data;
    }
}
