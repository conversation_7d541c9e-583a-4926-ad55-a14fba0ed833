<?php
namespace Modules\GeneralEvent\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketLevelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Get the event level end date
        $eventLevelEndDate = $this->eventLevel->end_date ?? null;

        $data = [
            "ticket_level_id" => $this->id,
            "ticket" => new TicketResource($this->ticket->setAttribute('event_level_end_date', $eventLevelEndDate)),
            "discount_percentage" => $this->price >= $this->ticket->main_price ? 0 : (int)round(($this->price / $this->ticket->main_price) * 100) ,
            "count"  => $this->count,
            "main_price"  => $this->ticket->main_price,
            "discount_price"  => $this->price >= $this->ticket->main_price ? 0 : $this->price,
            "sessions"  => EventSessionResource::collection($this->eventLevel->event->sessions->map(function($session){
                    $isAvailable = false;
                    if ($this->ticket) {
                        $isAvailable = $this->ticket->ticketSessions
                                ->where('id', $session->id)
                                ->first() !== null;
                    }
                    $session->is_available = $isAvailable;
                    return $session;
            })),
        ];

        return $data;
    }
}
