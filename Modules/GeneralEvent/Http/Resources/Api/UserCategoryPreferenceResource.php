<?php

namespace Modules\GeneralEvent\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;


class UserCategoryPreferenceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "user_id" => $this->user_id,
            'category' => new CategoryResource($this->category),
        ];

        return $data;
    }
}
