<?php

namespace Modules\GeneralEvent\Http\Requests\Admin\Event;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEventRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'description' => 'nullable|string',
            'type' => 'required|in:offline,online,hybrid',
            'status' => 'required|in:draft,published,ended',
            'organizer_id' => 'required|exists:organizers,id',
            'category_id' => 'required|exists:categories,id',
            'link' => 'nullable|url|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'publish_date' => 'nullable|date',
            'has_certificate' => 'boolean',
            'event_hours' => 'nullable|numeric|between:0,99.99',
            'max_attendees' => 'nullable|integer|min:1'
        ];
    }
}
