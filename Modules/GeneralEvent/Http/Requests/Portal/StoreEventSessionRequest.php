<?php
namespace Modules\GeneralEvent\Http\Requests\Portal;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\GeneralEvent\Enums\StatusEnum;
use Modules\GeneralEvent\Enums\TypesEnum;

class StoreEventSessionRequest extends FormRequest
{

    public function authorize() {
        return true;
    }

    public function rules(): array
    {
        return [
            'event_id' => [
                'required','integer',Rule::exists('events', 'id')
            ],
            'name.ar' => ['required', 'string', 'min:1', 'max:255'],
            'name.en' => ['required', 'string', 'min:1', 'max:255'],
            'session_date' => ['required', 'date'],
            'description.ar' => 'max:255',
            'description.en' => 'max:255',
            'type'        => ['required', Rule::in(TypesEnum::cases())],
            'status'      => [Rule::in(StatusEnum::cases())],
            'start_time'  => 'required|date_format:H:i:s',
            'end_time'    => 'required|date_format:H:i:s|after:start_time',
            'link'        =>  [
                Rule::when(
                    fn() => in_array($this->type, [TypesEnum::ONLINE->value, TypesEnum::HYBRID->value]),
                    ['required', 'url', 'max:255'],
                    ['nullable']
                ),
            ],
            'hall_name'   =>  [
                Rule::when(
                    fn() => in_array($this->type, [TypesEnum::OFFLINE->value, TypesEnum::HYBRID->value]),
                    ['required', 'string','max:255'],
                    ['nullable']
                ),
            ],
            'type_id'     => 'required',Rule::exists('types', 'id')
        ];
    }
}
