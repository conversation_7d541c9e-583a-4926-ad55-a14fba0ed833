<?php
namespace Modules\GeneralEvent\Http\Requests\Portal;
use Illuminate\Foundation\Http\FormRequest;
use Modules\GeneralEvent\Enums\StatusEnum;
use Modules\GeneralEvent\Enums\TypesEnum;

class UpdateEventRequest extends FormRequest
{

    public function authorize() {
        return true;
    }

    public function rules()
    {
        $rules = [
            'name' => 'required|array',
            'name.en' => 'required|string|min:5|max:255',
            'name.ar' => 'required|string|min:5|max:255',
            'description' => 'required|array',
            'description.en' => 'required|string|min:5',
            'description.ar' => 'required|string|min:5',
            'type' => 'required|string|in:'.implode(',', TypesEnum::values()),
            'category_id' => 'required|integer|exists:categories,id',
            'status' => 'sometimes|string|in:'.implode(',', StatusEnum::values()),
            'start_date' => 'nullable|date|before_or_equal:end_date',
            'publish_date'=> 'nullable|date|before_or_equal:end_date',
            'end_date' => 'nullable|date|after_or_equal:start_date',  //Y-m-d H:i:s
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'event_hours' => 'sometimes|numeric',
            'max_attendees' => 'sometimes|integer',
            // Address fields
            'title' => 'required|string|max:255',
            'venue_name' => 'required|string|max:255',
            'lat' => 'nullable|string',
            'long' => 'nullable|string',
            'district' => 'nullable|string',
            'city' => 'nullable|string',
            'region' => 'nullable|string',
            'street' => 'nullable|string',
            'building_number' => 'nullable|string',
            //tags
            'tags' => 'nullable|array',
            'tags.*.name' => 'required|array',
            'tags.*.name.en' => 'required|string|max:255',
            'tags.*.name.ar' => 'required|string|max:255',
            'tags.*.id' => 'sometimes|exists:tags,id',
        ];

        $type = $this->input('type');
        // make venue name nullable on type online
        if ($type === TypesEnum::ONLINE->value) {
            $rules['title'] = ['nullable', 'string', 'max:255'];
            $rules['venue_name'] = ['nullable', 'string', 'max:255'];
        }

        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['organizer_id'] = auth()->id();
        return $validated;
    }

    public function messages()
    {
        return [
            // Name validations
            'name.required' => __('Name translations are required'),
            'name.array' => __('Name must be an array of translations'),
            'name.min' => __('Name must have both English and Arabic translations'),
            'name.en.required' => __('Name in English is required'),
            'name.ar.required' => __('Name in Arabic is required'),
            'name.*.min' => __('Name must be at least 5 characters'),
            'name.*.max' => __('Name cannot exceed 255 characters'),

            // Description validations
            'description.required' => __('Description translations are required'),
            'description.array' => __('Description must be an array of translations'),
            'description.min' => __('Description must have both English and Arabic translations'),
            'description.en.required' => __('Description in English is required'),
            'description.ar.required' => __('Description in Arabic is required'),
            'description.*.min' => __('Description must be at least 3 characters'),
            'description.*.max' => __('Description cannot exceed 255 characters'),

            // Other fields
            'type.required' => __('Event type is required'),
            'type.in' => __('Invalid event type'),
            'category_id.required' => __('Category is required'),
            'category_id.exists' => __('Selected category does not exist'),
            'status.in' => __('Invalid status'),
            'start_date.before_or_equal' => __('Start date must be before or equal to end date'),
            'end_date.after_or_equal' => __('End date must be after or equal to start date'),
            'image.image' => __('File must be an image'),
            'max_attendees.integer' => __('Maximum attendees must be a number')
        ];
    }
}
