<?php
namespace Modules\GeneralEvent\Http\Requests\Portal;
use Illuminate\Foundation\Http\FormRequest;
use Modules\GeneralEvent\Enums\StatusEnum;
use Modules\GeneralEvent\Enums\TypesEnum;

class UpdateEventRequest extends FormRequest
{

    public function authorize() {
        return true;
    }

    public function rules()
    {
        $rules = [
            'name' => 'required|array',
            'is_active' => 'required|boolean'
        ];
        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['user_id'] = auth()->id();
        return $validated;
    }
}
