<?php
namespace Modules\GeneralEvent\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\GeneralEvent\Entities\EventSession;
use Modules\GeneralEvent\Entities\GeneralEvent;
use Modules\GeneralEvent\Enums\StatusEnum;
use Modules\GeneralEvent\Http\Resources\Api\EventSessionResource;
use Modules\GeneralEvent\Http\Resources\Api\GeneralEventResource;
use Modules\GeneralEvent\Services\GeneralEventService;

class GeneralEventController extends ControllerAbstract
{
    protected string $jsonResourceClass = GeneralEventResource::class;
    public function __construct(GeneralEventService $service)
    {
        parent::__construct($service);
        $this->filter = ['status' => StatusEnum::PUBLISHED];
    }

    public function getAllByUserCategoryPreferences(Request $request)
    {
        $preferredCategoryIDs = auth()->user()->categoryPreferences()->pluck('category_id')->toArray();
        return ApiResponse::data($this->jsonResourceClass::collection($this->service->paginateAllEventsByUserCategoryPreferences($preferredCategoryIDs)));
    }

    public function getMinMaxEventHours()
    {
        $eventHoursRange = $this->service->getMinMaxEventHours();
        return ApiResponse::data($eventHoursRange);
    }
    public function getMinMaxPriceForPublishedEvents(): \Illuminate\Http\JsonResponse
    {
        $eventPriceRange = $this->service->getMinMaxPriceForPublishedEvents();
        return ApiResponse::data($eventPriceRange);
    }

    public function attendeeCalender()
    {
        $orders = $this->service->getAttendeeCalender(auth()->user());

        $orders->setCollection($orders->getCollection()->map->event);
        return ApiResponse::data(GeneralEventResource::collection($orders));
    }

    public function getEventSessions(int $id)
    {
        $data = $this->service->getEventSessions($id);
        return ApiResponse::data(EventSessionResource::collection($data));
    }

    public function getMyEventList(): \Illuminate\Http\JsonResponse
    {
        $data = $this->service->getAttendeeEvents(auth()->id());
        return ApiResponse::data(GeneralEventResource::collection($data));
    }
    public function getEventReviews($id)
    {
        $event = GeneralEvent::with(['reviews' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }])->findOrFail($id);

        return ApiResponse::data(new GeneralEventResource($event));
    }

}
