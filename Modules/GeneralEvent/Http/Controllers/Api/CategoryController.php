<?php
namespace Modules\GeneralEvent\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\GeneralEvent\Http\Resources\Api\CategoryResource;
use Modules\GeneralEvent\Services\CategoryService;

class CategoryController extends ControllerAbstract
{
    protected string $jsonResourceClass = CategoryResource::class;

    public function __construct(CategoryService $service)
    {
        parent::__construct($service);
        $this->filter['active'] = true;
    }
}
