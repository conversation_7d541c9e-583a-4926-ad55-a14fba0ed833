<?php
namespace Modules\GeneralEvent\Http\Controllers\Portal;

use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\GeneralEvent\Services\LevelService;
use Modules\GeneralEvent\Services\TicketService;
use Modules\GeneralEvent\Services\GeneralEventService;
use Modules\GeneralEvent\Services\TicketLevelService;
use Modules\GeneralEvent\Http\Requests\Portal\StoreTicketRequest;

class TicketController extends Controller
{
    public function __construct(private LevelService $levelService, private TicketService $ticketService,private GeneralEventService $generalEventService , private TicketLevelService $ticketLevelService)
    {

    }

    public function create($event_id)
    {
        $levels = $this->levelService->getAllLevels(auth()->user());
        $retriveData = $this->generalEventService->retrieveCreateTicketData($event_id);
        $ticketsWithLevels = $this->generalEventService->getEventTicketsWithLevels($event_id);
        $eventData = $this->generalEventService->getEvent($event_id);
        $eventSessions = $this->generalEventService->getEventSessions($event_id);

        return Inertia::render('Portal/Organizer/Events/Ticketing/Create', [
            'levels' => $levels,
            'event_id' => $event_id,
            'eventData' => $eventData,
            'eventSessions'=>$eventSessions,
            'max_attendees' => $retriveData['event']->max_attendees,
            'ticketingData' => [
                'existingLevels' => $retriveData['existingLevels'],
                'existingTickets' => $retriveData['existingTickets'],
            ],
            'ticketLevelsData'=> $retriveData['ticketLevelsData'],
            'ticketsWithLevels'=>$ticketsWithLevels
        ]);
    }

    public function storeTicket(StoreTicketRequest $request)
    {
        $validated = $request->validated();
        $this->ticketService->create($validated);
    }

    public function storeTicketLevel(Request $request)
    {
        $this->ticketLevelService->create($request->all());
    }
}
