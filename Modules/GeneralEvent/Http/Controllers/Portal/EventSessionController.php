<?php
namespace Modules\GeneralEvent\Http\Controllers\Portal;

use Inertia\Inertia;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\GeneralEvent\Enums\SessionStatusEnum;
use Modules\GeneralEvent\Http\Requests\Portal\UpdateEventSessionRequest;
use Modules\GeneralEvent\Services\TypeService;
use Modules\GeneralEvent\Services\EventSessionService;
use Modules\User\app\Http\Requests\Portal\AddSpeakerRequest;
use Modules\GeneralEvent\Http\Requests\Portal\StoreEventSessionRequest;
use Modules\GeneralEvent\Services\GeneralEventService;
use Modules\User\app\Http\Requests\Portal\QuickAddSpeakerRequest;
use Modules\User\Services\AuthPortalService;

class EventSessionController extends Controller
{
    public function __construct(private EventSessionService $eventSessionService, private TypeService $typeService, private GeneralEventService $generalEventService, private AuthPortalService $authPortalService)
    {

    }

    public function addSpeaker(AddSpeakerRequest $request)
    {
        $this->eventSessionService->addSpeaker($request->validated);
    }

    public function create($event_id)
    {
        $sessionType = $this->typeService->getAllTypes();
        $eventData = $this->generalEventService->getEvent($event_id);
        return Inertia::render('Portal/Organizer/Events/Sessions/Create', [
            'event_id' => $event_id,
            'sessionType' => $sessionType,
            'eventData' => $eventData,
        ]);
    }

    public function edit($session_id)
    {
        $sessionType = $this->typeService->getAllTypes();
        $resource = $this->eventSessionService->findOrFail(id: $session_id,with: ['speakers','Type']);
        return Inertia::render('Portal/Organizer/Events/Sessions/Edit', [
            'sessionType' => $sessionType,
            'sessionData' => $resource,
        ]);
    }

    public function update(UpdateEventSessionRequest $request, $session_id)
    {
        try {
            // Find the session
            $session = $this->eventSessionService->findOrFail(id: $session_id);

            // Get validated data
            $validatedData = $request->validated();

            // Extract speakers from the validated data
            $speakers = $validatedData['speakers'] ?? [];

            // Update the session
            $this->eventSessionService->update($validatedData, $session);

            // Sync speakers if they are provided
            if (!empty($speakers)) {
                $session->speakers()->sync($speakers);
            }

            return redirect()->route('events.section', [
                'id' => $session->event_id,
                'section' => 'sessions'
            ])->with('success', __('Session updated successfully!'));
        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'error' => $e->getMessage()
            ]);
        }
    }

    public function deleteSession(int $id)
    {
        $session = $this->eventSessionService->findOrFail(id: $id);

        try {
            if($session->status != SessionStatusEnum::PENDING){
                return redirect()->back()->withErrors([
                    'error' => __('cant delete this session')
                ]);
            }

            $this->eventSessionService->delete($session);

            return redirect()->route('events.section', [
                'id' => $session->event_id,
                'section' => 'sessions'
            ])->with('success', __('Session Deleted successfully!'));

        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'error' => $e->getMessage()
            ]);
        }
    }

    public function cancel($session_id)
    {
        $session = $this->eventSessionService->findOrFail(id: $session_id);

        try {
            $this->eventSessionService->update(['status'=>SessionStatusEnum::CANCELED], $session);

            return redirect()->route('events.section', [
                'id' => $session->event_id,
                'section' => 'sessions'
            ])->with('success', __('Session Canceled successfully!'));

        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'error' => $e->getMessage()
            ]);
        }
    }
    public function validated(StoreEventSessionRequest $request)
    {
        $request->validated();
    }

    public function store (StoreEventSessionRequest $request)
    {
        $this->eventSessionService->create($request->all());
    }

    public function getUserByEmail (Request $request)
    {
        return $this->authPortalService->getUserByEmail($request['email']);
    }

    public function quickAddSpeaker(Request $request)
    {
        $result = $this->authPortalService->quickAddSpeaker($request->all());

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'user' => $result['user']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message']
        ], 422);
    }
}
