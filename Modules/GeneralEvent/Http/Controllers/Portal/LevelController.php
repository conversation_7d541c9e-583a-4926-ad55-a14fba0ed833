<?php
namespace Modules\GeneralEvent\Http\Controllers\Portal;

use App\Http\Controllers\Controller;
use App\Http\Resources\UpdateLevelRequest;
use Illuminate\Support\Facades\Log;
use Modules\GeneralEvent\Entities\Level;
use Modules\GeneralEvent\Http\Requests\Portal\StoreLevelRequest;
use Modules\GeneralEvent\Services\LevelService;
use Illuminate\Http\Request; // Correct import for Request class
use Inertia\Inertia;

class LevelController extends  Controller
{

    protected $indexView = "Portal/Organizer/Events/Levels/Index";
    protected $createView = "Portal/Organizer/Events/Levels/Create";

    public function __construct(private LevelService $service)
    {

    }

    public function index(Request $request): \Inertia\Response
    {
        $levels = $this->service->paginate(filter: ['user_id'=> auth()->id()]);
        return Inertia::render($this->indexView, [
            'levels' => $levels,
            'initialUrlParams' => [
                'filters' => $request->input('filters', []),
                'search' => $request->input('search_key', ''),
            ]
        ]);
    }

    public function update(UpdateLevelRequest $request, $id)
    {
        $level = $this->service->findOrFail($id);
        $this->service->update($request->validated(), $level);
        dd('Level Updated Successfully');
    }
    public function destroy($id)
    {
        try {
            $level = $this->service->findOrFail($id);
            $this->service->delete($level);

            return redirect()->route('levels.index')->with('success', 'Level deleted successfully.');
        } catch (\Exception $exception) {
            return redirect()->route('levels.index')->with('error', 'Failed to delete the level.');
        }
    }
   public function store(StoreLevelRequest $request)
   {

       $validated = $request->validated();
       $level = $this->service->create($validated);

       return redirect()
           ->route('levels.index') // your index route
           ->with('success', 'Level created successfully');

   }

    public function create()
    {
        return Inertia::render($this->createView);
    }

}
