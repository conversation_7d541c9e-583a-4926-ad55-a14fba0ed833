<?php

namespace Modules\GeneralEvent\Http\Controllers\Portal;

use Illuminate\Support\Str;
use Inertia\Inertia;
use Khaleds\Shared\Http\Controllers\Inertia\ControllerAbstract;
use Modules\GeneralEvent\Http\Requests\Portal\StoreEventRequest;
use Modules\GeneralEvent\Http\Requests\Portal\UpdateEventRequest;
use Modules\GeneralEvent\Services\GeneralEventService;
use Modules\GeneralEvent\Entities\Category;
use Illuminate\Http\Request;
use Inertia\Response;

class GeneralEventController extends ControllerAbstract
{

    protected $indexView = "Portal/Organizer/Events/Index";
    protected $createView = "Portal/Organizer/Events/Create";

    protected $showView = "Portal/Organizer/Events/Show";

    protected $editView = "Portal/Organizer/Events/Edit";

    protected $redirectRoute = "generalEvent";

    protected $resourceName = "events";
    protected array $with = ['category', 'address', 'organizer','media', 'sessions.speakers', 'tags'];

    protected $calendarView = "Portal/Organizer/Calendar/Index";

    public function __construct(GeneralEventService $service)
    {

        parent::__construct($service);
        $this->storeRequestClass = StoreEventRequest::class;
        $this->updateRequestClass = UpdateEventRequest::class;
    }


    public function index(): \Inertia\Response
    {
        $this->filter['id']=auth()->user()->id;
        return parent::index();
    }

    public function create(): \Inertia\Response
    {
        $categories = Category::with('matchedTags')->select('id', 'name')->get();
        return Inertia::render($this->createView, [
            'categories' => $categories,
        ]);
    }

    public function edit($id): \Inertia\Response
    {
        $resource = $this->service->findOrFail(id: $id, filter: $this->filter, with: $this->with);
        $resource->speakers = $resource->sessions->pluck('speakers')->flatten();

        $categories = Category::with('matchedTags')->select('id', 'name')->get();

        return Inertia::render($this->editView, array_merge([
            Str::singular($this->resourceName) => $resource,
            'categories' => $categories
        ], $this->withAdditionalData()));

    }

    public function show(int $id, ?string $section = 'details'): \Inertia\Response
    {
        $resource = $this->service->findOrFail(id: $id, filter: $this->filter, with: $this->with);
        $resource->speakers = $resource->sessions->pluck('speakers')->flatten();
        $levelWithTickets = $this->service->getLevelsWithTickets($id);
        $reviews = $resource->reviews()->with('user')->latest()->paginate(10);

        return Inertia::render($this->showView, array_merge([
            Str::singular($this->resourceName) => $resource,
            'section' => $section,
            'tickets' => $levelWithTickets,
            'reviews' => $reviews,
        ], $this->withAdditionalData()));
    }


    public function publish(int $id)
    {
        $resource = $this->service->findOrFail($id);

        try {
            $this->service->publish($resource);

            return redirect()->back()->with([
                'success' => __('Event published successfully!'),
                'event' => $resource->fresh() // Return updated resource if needed
            ]);

        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'error' => $e->getMessage()
            ]);
        }
    }

    public function deleteEvent(int $id)
    {
        $resource = $this->service->findOrFail(id: $id, filter: $this->filter, with: $this->with);
        try {

            $this->service->deleteEvent($resource->id);
            return redirect()->route('generalEvent.index')->with('success', __('Event Deleted successfully!'));

        } catch (\Exception $e) {
            return Inertia::render($this->showView, array_merge([
                $this->resourceName => $resource,
                'errors' => ['error' => $e->getMessage()]
            ], $this->withAdditionalData()));
        }

    }

    public function cancelEvent(int $id)
    {
        $resource = $this->service->findOrFail($id);

        try {
            $service = $this->service->cancel($resource);

            if (!$service['state']) {
                return redirect()->back()->withErrors(['error' => $service['message']]);
            }

            return redirect()->back()->with([
                'success' => __('Event canceled successfully!'),
                'event' => $resource->fresh() // Return updated resource if needed
            ]);

        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'error' => $e->getMessage()
            ]);
        }
    }

    public function organizerCalender(): \Inertia\Response
    {
        $data = $this->service->organizerCalender(auth()->user());
        //as a front end please return intertia with session ['sessions' => $data] and remove the return $data
        return Inertia::render($this->calendarView, [
            'sessions' => $data,
        ]);
    }

}
