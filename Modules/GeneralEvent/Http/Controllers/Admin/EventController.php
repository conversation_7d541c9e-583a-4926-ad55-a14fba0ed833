<?php

namespace Modules\GeneralEvent\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Khaleds\Shared\Http\Controllers\Admin\ControllerAbstract;
use Modules\GeneralEvent\DataTables\eventsDataTable;
use Modules\GeneralEvent\Http\Requests\Admin\Event\StoreEventRequest;
use Modules\GeneralEvent\Http\Requests\Admin\Event\UpdateEventRequest;
use Modules\GeneralEvent\Services\CategoryService;
use Modules\GeneralEvent\Services\GeneralEventService;
use Modules\User\Enums\UserRoleEnum;
use Modules\User\Services\UserService;
use Modules\GeneralEvent\DataTables\eventsSessionDataTable;
use Modules\GeneralEvent\DataTables\eventsLevelDataTable;
use Modules\GeneralEvent\DataTables\eventsTicketsDataTable;
use Modules\GeneralEvent\DataTables\eventsOrderDataTable;
use Modules\GeneralEvent\DataTables\eventsReviewDataTable;


class EventController extends ControllerAbstract
{
    /*
     * breadcrumb
     * menu -- class - hooks
     * struct for views or crumbs
     * */
    protected string $storeRequest = StoreEventRequest::class;

    protected string $updateRequest = UpdateEventRequest::class;

    protected string $group = 'Event Management';

    protected string $dataTable = eventsDataTable::class;
    protected string $viewPath = 'generalevent::Event';
    protected string $routeName="admin.events";
    protected string $title = 'events';


    public function __construct(GeneralEventService $service, public UserService $userService , public CategoryService $categoryService)
    {

        parent::__construct($service);
    }

    public function create(): View
    {
        return view($this->viewPath . '.form', [
            'title' => $this->label,
            'action' => $this->routeName.'.store',
            'method' => 'POST',
            'data' => ['organizers'=>$this->userService->getOrginzerUser(),'categories'=>$this->categoryService->getCategories(['active'=>1])->get()],

        ]);
    }

    public function edit($id): View
    {
        $model = $this->service->findOrFail($id);
        return view($this->viewPath . '.form', [
            'title' => $this->label,
            'model' => $model,
            'action' => $this->routeName.'.update',
            'method' => 'PUT',
            'data' => ['organizers'=>$this->userService->getOrginzerUser(),'categories'=>$this->categoryService->getCategories(['active'=>1])->get()],
        ]);
    }

    public function show($id)
    {
        $model = $this->service->findOrFail($id,with:['category','organizer']);
        $sessionsDataTable = (new eventsSessionDataTable())->setEventId($id);
        $levelsDataTable = (new eventsLevelDataTable())->setEventId($id);
        $ticketLevelDataTable = (new eventsTicketsDataTable())->setEventId($id);
        $ticketOrderDataTable = (new eventsOrderDataTable())->setEventId($id);
        $eventsReviewDataTable = (new eventsReviewDataTable())->setEventId($id);
        if (request()->ajax()) {
            if (request()->has('table') && request()->get('table') === 'levels') {
                return $levelsDataTable->ajax();
            }elseif (request()->has('table') && request()->get('table') === 'tickets') {
                return $ticketLevelDataTable->ajax();
            }elseif (request()->has('table') && request()->get('table') === 'orders') {
                return $ticketOrderDataTable->ajax();
            }elseif (request()->has('table') && request()->get('table') === 'reviews') {
                return $eventsReviewDataTable->ajax();
            }
            return $sessionsDataTable->ajax();
        }
        
        return view($this->viewPath . '.view', [
            'title' => $this->label,
            'model' => $model,
            'sessionsDataTable' => $sessionsDataTable->html(),
            'levelsDataTable' => $levelsDataTable->html(),
            'ticketLevelDataTable' => $ticketLevelDataTable->html(),
            'ticketOrderDataTable' => $ticketOrderDataTable->html(),
            'eventsReviewDataTable' => $eventsReviewDataTable->html(),
        ]);
    }

}
