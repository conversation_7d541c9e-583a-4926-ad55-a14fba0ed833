@extends('theme::admin.layout.app')

@section('title', 'Category Details')

@section('meta_description', 'Category details view for application')
@section('meta_keywords', 'category, view, management, details')

@section('content')

    @php
        $category = $model;
    @endphp

        <!--begin::Content container-->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h3 class="card-title fw-bold">Category Information</h3>
            <div class="card-toolbar">
                <a href="{{ route('admin.categories.edit', $category->id) }}" class="btn btn-sm btn-primary me-2">
                    <i class="ki-duotone ki-pencil fs-2"><span class="path1"></span><span class="path2"></span></i>
                    Edit
                </a>
                <form method="POST" action="{{ route('admin.categories.destroy', $category->id) }}" style="display:inline;" class="me-2">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                        <i class="ki-duotone ki-trash fs-2"><span class="path1"></span><span class="path2"></span></i>
                        Delete
                    </button>
                </form>
                <a href="{{ route('admin.categories.index') }}" class="btn btn-sm btn-secondary">
                    <i class="ki-duotone ki-arrow-left fs-2"><span class="path1"></span><span class="path2"></span></i>
                    Back
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row g-5">
                <!-- Left Column -->
                <div class="col-xl-6">
                    <!-- Basic Information Card -->
                    <div class="card shadow-sm mb-5">
                        <div class="card-header">
                            <h3 class="card-title fw-bold">Basic Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-column gap-5">
                                <div class="d-flex flex-row">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 w-125px">ID:</div>
                                    <div class="fs-6 fw-bold text-gray-800">{{ $category->id }}</div>
                                </div>

                                <div class="d-flex flex-row">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 w-125px">Name (AR):</div>
                                    <div class="fs-6 fw-bold text-gray-800">{{ $category->name['ar'] ?? '-' }}</div>
                                </div>

                                <div class="d-flex flex-row">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 w-125px">Name (EN):</div>
                                    <div class="fs-6 fw-bold text-gray-800">{{ $category->name['en'] ?? '-' }}</div>
                                </div>

                                <div class="d-flex flex-row">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 w-125px">Status:</div>
                                    <div>
                                        <span class="badge {{ $category->is_active ? 'badge-light-success' : 'badge-light-danger' }} fs-7 fw-bold">
                                            {{ $category->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-xl-6">
                    <!-- Description Card -->
                    <div class="card shadow-sm mb-5">
                        <div class="card-header">
                            <h3 class="card-title fw-bold">Description</h3>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-column gap-5">
                                <div class="d-flex flex-column">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 mb-2">Description (EN):</div>
                                    <div class="fs-6 text-gray-800">{{ $category->description['en'] ?? '-' }}</div>
                                </div>

                                <div class="d-flex flex-column">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 mb-2">Description (AR):</div>
                                    <div class="fs-6 text-gray-800">{{ $category->description['ar'] ?? '-' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Information Card -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h3 class="card-title fw-bold">System Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-column gap-5">
                                <div class="d-flex flex-row">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 w-125px">Created At:</div>
                                    <div class="fs-6 text-gray-800">{{ $category->created_at?->format('Y-m-d H:i') }}</div>
                                </div>

                                <div class="d-flex flex-row">
                                    <div class="flex-shrink-0 fw-semibold text-gray-600 w-125px">Updated At:</div>
                                    <div class="fs-6 text-gray-800">{{ $category->updated_at?->format('Y-m-d H:i') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end::Content container-->

@endsection
