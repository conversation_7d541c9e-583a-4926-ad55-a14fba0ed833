@extends('theme::admin.layout.app')

@section('title', 'Update Category')

@section('content')

    <div class="card d-flex flex-row-fluid flex-center" xmlns:x-theme="http://www.w3.org/1999/html">
        <form class="card-body py-20 w-100  px-9 fv-plugins-bootstrap5 fv-plugins-framework"
              novalidate="novalidate"
              action="{{ ($method == 'PUT') ? route($action, $model->id) : route($action) }}"
              method="POST"
              id="kt_event_form">
            @csrf
            @method($method)
            <div class="current" data-kt-stepper-element="content">
                <div class="w-100">
                    <div class="pb-10 pb-lg-15">
                        <h2 class="fw-bold d-flex align-items-center text-gray-900">
                            {{ ($method == 'PUT') ? 'Edit' : 'Create' }} Category
                            <span class="ms-1" data-bs-toggle="tooltip" title="Category information">
                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6"></i>
                        </span>
                        </h2>

                        <div class="text-muted fw-semibold fs-6">
                            Please fill in the category information
                        </div>
                    </div>

                    <!-- Name Input -->
                    <x-theme::translation-input names="name" required :model="$model??null" />

                    <!-- description Input -->
                    <x-theme::translation-editor
                        name="description"
                        label="Description"
                        toolbar="basic"
                        minHeight="150px"
                        :model="$model??null"
                    />

                    <!-- Active Toggle -->
                    <x-theme::toggle
                        name="is_active"
                        label="Active Status"
                        :checked="$model->is_active ?? true"
                    />

                    <div class="d-flex flex-stack pt-10">
                        <div class="mr-2">
                            <a href="{{ route('admin.categories.index') }}" class="btn btn-lg btn-light-primary me-3">
                                <i class="ki-duotone ki-arrow-left fs-4 me-1"></i> Back
                            </a>
                        </div>

                        <div>
                            <button type="submit" class="btn btn-lg btn-primary flex-end">
                                {{ __("Save") }}
                            </button>
                        </div>
                    </div>
        </form>
    </div>
@endsection
