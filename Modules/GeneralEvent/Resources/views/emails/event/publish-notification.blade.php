<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Published Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #007BFF;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            margin: 10px 0;
        }
        .event-details {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .event-details p {
            margin: 5px 0;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007BFF;
            color: #fff;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>{{ __('emails.event_published_subject', ['event_name' => $event->name]) }}</h1>
    <p>{{ __('emails.greeting') }}</p>
    <p>{{ __('emails.event_published_message', ['event_name' => $event->name]) }}</p>

    <div class="event-details">
        <h2>{{ __('emails.event_details') }}</h2>
        <p><strong>{{ __('emails.event_name') }}:</strong> {{ $event->name }}</p>
        <p><strong>{{ __('emails.event_date') }}:</strong> {{ $event->start_date }}</p>
        <p><strong>{{ __('emails.event_description') }}:</strong> {{ $event->description }}</p>
    </div>

    <p>{{ __('emails.dont_miss_out') }}</p>

    <p>{{ __('emails.contact_us') }}</p>
    <p>{{ __('emails.looking_forward') }}</p>

    <div class="footer">
        {{ __('emails.best_regards') }}<br>
        <strong>{{ config('app.name') }}</strong>
    </div>
</div>
</body>
</html>
