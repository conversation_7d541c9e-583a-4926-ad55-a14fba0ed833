<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #007BFF;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            margin: 10px 0;
        }
        .details {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .details p {
            margin: 5px 0;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>{{ __('emails.session_reminder_subject', ['session_name' => $session->name]) }}</h1>
    <p>{{ __('emails.greeting_with_name', ['name' => $userName]) }}</p>

    @if($userType === \Modules\User\Enums\UserRoleEnum::ORGANIZER->value)
        <p>{!! __('emails.organizer_message', ['session_name' => $session->name]) !!}</p>
    @elseif($userType === \Modules\User\Enums\UserRoleEnum::SPEAKER->value)
        <p>{!! __('emails.speaker_message', ['session_name' => $session->name]) !!}</p>
    @elseif($userType === \Modules\User\Enums\UserRoleEnum::ATTENDEE->value)
        <p>{!! __('emails.attendee_message', ['session_name' => $session->name]) !!}</p>
    @endif

    <div class="details">
        <h2>{{ __('emails.session_details') }}</h2>
        <p><strong>{{ __('emails.session_name') }}:</strong> {{ $session->name }}</p>
        <p><strong>{{ __('emails.start_time') }}:</strong> {{ date('H:i', strtotime($session->start_time)) }}</p>
        <p><strong>{{ __('emails.end_time') }}:</strong> {{ date('H:i', strtotime($session->end_time)) }}</p>
        @if(isset($metadata['location']))
            <p><strong>{{ __('emails.location') }}:</strong> {{ $metadata['location'][app()->getLocale()] ?? null }}</p>
        @endif
        <p><strong>{{ __('emails.description') }}:</strong> {{ $session->description }}</p>
    </div>

    @if($userType === \Modules\User\Enums\UserRoleEnum::ATTENDEE->value && !is_null($session->link))
        <p>{{ __('emails.join_session') }}</p>
        <a href="{{ $session->link }}" style="color: #007BFF; text-decoration: none;">{{ __('emails.join_session') }}</a>
    @endif

    <p>{{ __('emails.contact_us') }}</p>

    <div class="footer">
        {{ __('emails.best_regards') }}<br>
        <strong>{{ config('app.name') }}</strong>
    </div>
</div>
</body>
</html>
