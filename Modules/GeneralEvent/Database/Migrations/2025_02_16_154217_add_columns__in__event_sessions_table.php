<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_sessions', function (Blueprint $table) {
            $table->date('session_date'); 
            $table->time('start_time')->change();
            $table->time('end_time')->change();
            $table->foreignId('type_id')->constrained('types')->onDelete('cascade')->comment('Foreign key referencing the types table to define session type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_sessions', function (Blueprint $table) {
            $table->dropForeign(['type_id']);
            $table->dropColumn('type_id');
            $table->string('start_time')->change();
            $table->string('end_time')->change();
            $table->dropColumn('session_date');
        });
    }
};
