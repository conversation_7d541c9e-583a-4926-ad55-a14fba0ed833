<?php
namespace Modules\GeneralEvent\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\GeneralEvent\Entities\GeneralEvent;

class EventAddressFactory extends Factory
{
    protected $model = \Modules\GeneralEvent\Entities\EventAddress::class;

    public function definition(): array
    {
        return [
            'event_id' => GeneralEvent::factory(),
            'title' => $this->faker->address,
            'lat' => $this->faker->latitude,
            'long' => $this->faker->longitude,
            'city' => $this->faker->city,
            'district' => $this->faker->streetAddress,
            'region' => $this->faker->city,
            'street' => $this->faker->streetName,
            'building_number' => $this->faker->buildingNumber,
        ];
    }

    /**
     * Set the event ID for the address.
     *
     * @param int $eventId
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function forEvent(int $eventId)
    {
        return $this->state(function (array $attributes) use ($eventId) {
            return [
                'event_id' => $eventId,
            ];
        });
    }
}
