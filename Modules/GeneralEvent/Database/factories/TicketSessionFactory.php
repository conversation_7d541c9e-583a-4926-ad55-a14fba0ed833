<?php

namespace Modules\GeneralEvent\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\GeneralEvent\Entities\TicketSession;
use Modules\GeneralEvent\Entities\Ticket;
use Modules\GeneralEvent\Entities\EventSession;

class TicketSessionFactory extends Factory
{
    protected $model = TicketSession::class;

    public function definition(): array
    {
        return [
            'ticket_id' => $this->getOrCreateTicketId(),
            'event_session_id' => $this->getOrCreateEventSessionId(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    private function getOrCreateTicketId()
    {
        return Ticket::query()->inRandomOrder()->value('id')
            ?? Ticket::factory()->create()->id;
    }

    private function getOrCreateEventSessionId()
    {
        return EventSession::query()->inRandomOrder()->value('id')
            ?? EventSession::factory()->create()->id;
    }
}