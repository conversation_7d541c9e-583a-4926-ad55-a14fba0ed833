<?php
namespace Modules\GeneralEvent\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\User\app\Models\User;

class TypeFactory extends Factory
{
    protected $model = \Modules\GeneralEvent\Entities\Type::class;

    public function definition(): array
    {
        $number = fake()->unique()->numberBetween(1, 10);
        
        return [
            'name' => [
                'en' => "Type {$number}",
                'ar' => "النوع {$number}",
            ],
            'description' => [
                'en' => "Description for type {$number}",
                'ar' => "وصف النوع {$number}",
            ],
        ];
    }
}
