<?php
namespace Modules\GeneralEvent\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryFactory extends Factory
{
    protected $model = \Modules\GeneralEvent\Entities\Category::class;

    public function definition(): array
    {
        return [
            'name' => [
                'en' => $this->faker->word(),
                'ar' => 'فئة ' . $this->faker->numberBetween(1, 100)
            ],
            'description' => [
                'en' => $this->faker->sentence(),
                'ar' => 'وصف الفئة ' . $this->faker->numberBetween(1, 100)
            ],
            'active' => $this->faker->boolean(100), // 100% chance of being true
        ];
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => false,
            ];
        });
    }
}
