<?php

namespace Modules\GeneralEvent\Database\Seeders;

use Modules\User\app\Models\User;
use Illuminate\Database\Seeder;
use Modules\GeneralEvent\Entities\Category;
use Modules\GeneralEvent\Entities\EventAddress;
use Modules\GeneralEvent\Entities\GeneralEvent;

class GeneralEventTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                "name" => [
                    "en" => "First event",
                    "ar" => "الفاعلية الاولي",
                ],
                "description" => [
                    "en" => "First event details",
                    "ar" => "تفاصيل الفاعلية الاولي"
                ],
                "category_id" => Category::first()->id,
                "organizer_id" => User::first() ? User::first() ->id: User::factory()->create()->id,
                "type" => 'offline',
                "start_date" => date('Y-m-d H:i:s'),
                "end_date" => date('Y-m-d H:i:s'),
                "event_hours" => 7,
                "max_attendees" => 1000,
            ],
            [
                "name" => [
                    "en" => "Second event",
                    "ar" => "الفاعلية الثانية",
                ],
                "description" => [
                    "en" => "Second event details",
                    "ar" => "تفاصيل الفاعلية الثانية"
                ],
                "category_id" => Category::first()->id,
                "organizer_id" => User::first() ? User::first() ->id: User::factory()->create()->id,
                "type" => 'offline',
                "start_date" => date('Y-m-d H:i:s'),
                "end_date" => date('Y-m-d H:i:s'),
                "event_hours" => 7,
                "max_attendees" => 1000,
            ],
        ];
        $eventAddress = [
            "title" => "Jeddah, Saudi Arabia",
            "lat" => 10922167,
            "long" => 10909167,
            "city" => "Jeddah",
            "district" => "Jeddah",
            "region" => "Jeddah",
            "street" => "15 sens street ,Jeddah",
            "building_number" => 10
        ];
        foreach ($data as $eventData) {
            if (!GeneralEvent::where('name->en', $eventData['name']['en'])->exists()) {
                $event = GeneralEvent::create($eventData);
                $eventAddress["event_id"] = $event->id;
                EventAddress::create($eventAddress);
            }
        }
    }
}
