<?php

namespace Modules\GeneralEvent\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\GeneralEvent\Entities\Category;
use Modules\GeneralEvent\Entities\GeneralEvent;

class EventCategoryTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                "name" => [
                    "en" => "Entrepreneurship",
                    "ar" => "ريادة الاعمال",
                ],
                "description" => [
                    "en" => "Entrepreneurship",
                    "ar" => "ريادة الاعمال",
                ],
            ],
            [
                "name" => [
                    "en" => "Computer Science",
                    "ar" => "علوم الحاسب",
                ],
                "description" => [
                    "en" => "Computer Science",
                    "ar" => "علوم الحاسب",
                ],
            ],
        ];

        foreach ($data as $categoryData) {
            if (!Category::where('name->en', $categoryData['name']['en'])->exists()) {
                Category::create($categoryData);
            }
        }
    }
}
