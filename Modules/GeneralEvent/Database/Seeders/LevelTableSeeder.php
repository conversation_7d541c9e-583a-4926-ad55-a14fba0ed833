<?php

namespace Modules\GeneralEvent\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\GeneralEvent\Entities\Level;

class LevelTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();
        Level::factory()->count(10)->create();
        // $this->call("OthersTableSeeder");
    }
}
