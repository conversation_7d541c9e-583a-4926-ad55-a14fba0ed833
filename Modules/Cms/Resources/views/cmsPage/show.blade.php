{{-- @php
    $livewireStub = new class {
        public function getRenderHookScopes() {
            return [];
        }
        public function getExtraBodyAttributes() {
            return [];
        }
        public function getTitle() {
            return null;
        }
    };
@endphp

<x-filament-panels::layout.base :livewire="$livewireStub"> --}}
<!DOCTYPE html>
<html
    lang="{{ auth()->user()->lang ?? session()->get(key: 'locale') }}"
    dir="{{ (auth()->user()->lang ?? session()->get('locale')) == 'ar' ? 'rtl' : 'ltr' }}"
    @class([
        'fi min-h-screen',
        'dark' => filament()->hasDarkModeForced(),
    ])
>
    <head>
        <style>
            .static-page-content * {
                background-color: white !important;
            }

            .static-page-content ol {
                padding: 0 16px !important;
                list-style: auto;
            }
        </style>

        <meta charset="utf-8" />

        <meta name="viewport" content="width=device-width, initial-scale=1" />

        @if ($favicon = filament()->getFavicon())
            <link rel="icon" href="{{ $favicon }}" />
        @endif
        {{ filament()->getTheme()->getHtml() }}
        {{ filament()->getFontHtml() }}
    </head>

    <div class="container mx-auto p-6">
        @empty($content)
            <p>{{__('no content found')}}</p>
        @else
            <div class="">
                <h2 class=" text-2xl font-bold mb-4">{!! $name !!} </h2>
            </div>
            <div class="app-content flex-row-fluid me-0 px-5 static-page-content"> {!! $content !!}</div>
        @endempty
    </div>
</html>
{{-- </x-filament-panels::layout.base> --}}
