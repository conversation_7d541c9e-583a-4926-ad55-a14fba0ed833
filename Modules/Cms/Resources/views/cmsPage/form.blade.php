@extends('theme::admin.layout.app')


@section('title', 'Cms page')


@section('content')


    <div class="card">
        <div class="card-header">
            <h3>{{ __('general.Edit Page') }}</h3>
        </div>

        <div class="card-body">
            <form action="{{ route('admin.cms-page.update', $model->id) }}" method="POST">
                @csrf
                @method('PUT')

                <x-theme::translation-input names="name" label="{{ __('cms::messages.model.Name') }}" required :model="$model" />
                <x-theme::translation-input type="textarea"  label="{{ __('cms::messages.model.Content') }}" names="content" required :model="$model" />


                {{-- Submit --}}
                <button type="submit" class="btn btn-primary">{{ __('general.Save') }}</button>
            </form>
        </div>
    </div>
@endsection

