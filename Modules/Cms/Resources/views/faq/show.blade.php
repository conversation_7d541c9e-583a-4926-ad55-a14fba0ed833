
<!DOCTYPE html>
<html
    lang="{{ auth()->user()->lang ?? session()->get(key: 'locale') }}"
    dir="{{ (auth()->user()->lang ?? session()->get('locale')) == 'ar' ? 'rtl' : 'ltr' }}"
    @class([
        'fi min-h-screen',
        'dark' => filament()->hasDarkModeForced(),
    ])
>
    <head>

        <meta charset="utf-8" />

        <meta name="viewport" content="width=device-width, initial-scale=1" />

        @if ($favicon = filament()->getFavicon())
            <link rel="icon" href="{{ $favicon }}" />
        @endif
        <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
        {{ filament()->getTheme()->getHtml() }}
        {{ filament()->getFontHtml() }}
    </head>
    <div class="container mx-auto p-6">
        <div class="text-2xl font-bold mb-4">
            <h2>{{__('Faq')}} </h2>
        </div>
        <div class="app-content flex-row-fluid me-0 px-5 static-page-content">
            @empty($contents)
                <p> {{ __('No FAQ found') }}</p>
            @else
            <div id="accordion-flush" data-accordion="collapse" data-active-classes="bg-white dark:bg-gray-900 text-gray-900 dark:text-white" data-inactive-classes="text-gray-500 dark:text-gray-400">
                @foreach($contents as $key => $content)
                <h2 id="accordion-flush-heading-{{$key}}">
                    <button type="button" class="flex items-center justify-between w-full py-5 font-medium rtl:text-right text-gray-500 border-b border-[#b7b7b7] dark:border-gray-700 dark:text-gray-400 gap-3" data-accordion-target="#accordion-flush-body-{{$key}}" aria-expanded="false" aria-controls="accordion-flush-body-{{$key}}">
                        <span>{{$content['question']}}</span>
                        <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                        </svg>
                    </button>
                </h2>
                <div id="accordion-flush-body-{{$key}}" class="hidden" aria-labelledby="accordion-flush-heading-{{$key}}">
                    <div class="py-5 border-b border-[#b7b7b7] dark:border-gray-700">
                        <div class="flex gap-2">
                            <span class="text-[#8c8888]"> {{__('Answer')}} : </span>
                            {!!  $content['answer'] !!}
                        </div>
                    </div>
                    </div>
                @endforeach
            </div>
            @endempty
        </div>


    </div>
</html>
