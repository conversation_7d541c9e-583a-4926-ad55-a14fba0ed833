@extends('theme::admin.layout.app')

@section('title', 'Faq')

@section('content')
    <div class="card d-flex flex-row-fluid flex-center">
        <form class="card-body py-20 w-100  px-9 fv-plugins-bootstrap5 fv-plugins-framework"
              novalidate="novalidate"
              action="{{ ($method == 'PUT') ? route($action, $model->id) : route($action) }}"
              method="POST"
              id="kt_type_form">
            @csrf
            @method($method)

            <div class="current" data-kt-stepper-element="content">
                <div class="w-100">
                    <div class="pb-10 pb-lg-15">
                        <h2 class="fw-bold d-flex align-items-center text-gray-900">
                            {{ isset($model->id) ? 'Update Faq' : 'Create Faq' }}
                            <span class="ms-1" data-bs-toggle="tooltip" title="Type information">
                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6"></i>
                        </span>
                        </h2>
                        <div class="text-muted fw-semibold fs-6">
                            Please fill in the type information
                        </div>
                    </div>

                    <!-- Name Input -->
                    <x-theme::translation-input names="question" required :model="$model??null" />
                    <x-theme::translation-input names="answer" required :model="$model??null" />


                    @if(!isset($model))
                    <x-theme::select
                        name="source"
                        label="Source:"
                        placeholder="Select a source"
                        :options="\Modules\Cms\Enums\CmsSourceTypeEnum::getData()"/>
                    @endif

                    <input type="hidden" name="is_active" value="0">

                    <x-theme::toggle
                        name="is_active"
                        label="Active Status"
                        :checked="$model->is_active??true"
                    />



                    <div class="d-flex flex-stack pt-10">
                        <div class="mr-2">
                            <a href="{{ route('admin.faq.index') }}" class="btn btn-lg btn-light-primary me-3">
                                <i class="ki-duotone ki-arrow-left fs-4 me-1"></i> Back
                            </a>
                        </div>

                        <div>
                            <button type="submit" class="btn btn-lg btn-primary flex-end">
                                {{ __("Save") }}
                            </button>
                        </div>
                    </div>
        </form>
    </div>
@endsection
