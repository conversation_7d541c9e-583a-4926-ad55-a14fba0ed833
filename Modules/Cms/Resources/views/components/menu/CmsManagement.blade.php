<div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ Request::routeIs("users") || Request::routeIs("users.show") || Request::routeIs("role") ? "show" : "" }} ">
    <!--begin:Menu link-->
    <span class="menu-link">
        <span class="menu-icon">
            <i class="ki-duotone ki-address-book fs-2">
                <span class="path1"></span>
                <span class="path2"></span>
                <span class="path3"></span>
            </i>
        </span>
        <span class="menu-title">{{__('cms::messages.Cms Management')}}</span>
        <span class="menu-arrow"></span>
    </span>
    <!--end:Menu link-->
    <!--begin:Menu sub-->
    <div class="menu-sub menu-sub-accordion">
        <!--begin:Menu item-->
        <div class="menu-item">
            <!--begin:Menu link-->
            <a class="menu-link {{ Request::routeIs('cms-page') ? 'active' : '' }}" href="{{route('admin.cms-page.index')}}">
            <span class="menu-bullet">
                <span class="bullet bullet-dot"></span>
            </span>
            <span class="menu-title">{{__('cms::messages.Pages')}}</span>
            </a>
            <!--end:Menu link-->
        </div>
        <div class="menu-item">
            <!--begin:Menu link-->
            <a class="menu-link {{ Request::routeIs('faq') ? 'active' : '' }}" href="{{ route('admin.faq.index') }}">
            <span class="menu-bullet">
                <span class="bullet bullet-dot"></span>
            </span>
                <span class="menu-title">{{__('cms::messages.Faq')}}</span>
            </a>
            <!--end:Menu link-->
        </div>
{{--        <div class="menu-item">--}}
{{--            <!--begin:Menu link-->--}}
{{--            <a class="menu-link <?php echo e(Request::routeIs('orders') ? 'active' : ''); ?>" href="<?php echo e(route('admin.orders.index')); ?>">--}}
{{--            <span class="menu-bullet">--}}
{{--                <span class="bullet bullet-dot"></span>--}}
{{--            </span>--}}
{{--                <span class="menu-title">Orders</span>--}}
{{--            </a>--}}
{{--            <!--end:Menu link-->--}}
{{--        </div>--}}
{{--        <!--end:Menu item-->--}}
{{--        <div class="menu-item">--}}
{{--            <!--begin:Menu link-->--}}
{{--            <a class="menu-link <?php echo e(Request::routeIs('tag') ? 'active' : ''); ?>" href="<?php echo e(route('admin.tag.index')); ?>">--}}
{{--            <span class="menu-bullet">--}}
{{--                <span class="bullet bullet-dot"></span>--}}
{{--            </span>--}}
{{--                <span class="menu-title">Tags</span>--}}
{{--            </a>--}}
{{--            <!--end:Menu link-->--}}
{{--        </div>--}}
{{--        <!--end:Menu item-->--}}
{{--        <div class="menu-item">--}}
{{--            <!--begin:Menu link-->--}}
{{--            <a class="menu-link <?php echo e(Request::routeIs('types') ? 'active' : ''); ?>" href="<?php echo e(route('admin.types.index')); ?>">--}}
{{--            <span class="menu-bullet">--}}
{{--                <span class="bullet bullet-dot"></span>--}}
{{--            </span>--}}
{{--                <span class="menu-title">Types</span>--}}
{{--            </a>--}}
{{--            <!--end:Menu link-->--}}
{{--        </div>--}}

        <?php
        app('hooks')->do_action('admin_sidebar_events_management_group');
        ?>
    </div>
    <!--end:Menu sub-->
</div>
{{--todo : create profile content with edit forms--}}
{{--todo : theme multible or single --}}
