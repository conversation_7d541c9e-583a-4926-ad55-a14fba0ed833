<?php

namespace Modules\Cms\Enums;

enum CmsSourceTypeEnum: string
{
    case WEB = 'web';
    case MOBILE = 'mobile';


    public static function getData(): array
    {
        return [
            self::WEB->value => __('cms::messages.web'),
            self::MOBILE->value => __('cms::messages.mobile'),
        ];
    }
    public function label(): string
    {
        return match($this) {
            self::WEB => __('web'),
            self::MOBILE => __('mobile'),
        };
    }

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    public function color(): string
    {
        return match ($this) {
            self::WEB => 'primary',
            self::MOBILE => 'info',
        };

    }
    public function badge(): string
    {
        return '<span class="badge badge-light-' . $this->color() . '">' . $this->label() . '</span>';
    }

    public static function badgeFromValue(string $value,$withbagde = true): ?string
    {
        $status = self::tryFrom($value);

        return $status && $withbagde ? $status->badge() : $status->label();
    }
}
