<?php

namespace Modules\Cms\Hooks;

use Illuminate\Support\Facades\Request;

class MenuHook
{
    private function __construct()
    {
    }



    public static function register(){
        app('hooks')->add_action('admin_sidebar_menu',[self::getInstance(), 'add_admin_sidebar_menu']);

   }

    public function add_admin_sidebar_menu()
    {
        echo view('cms::components.menu.CmsManagement')->render();
    }



    /**
     * Returns a Singleton instance of this class.
     *
     * @return MenuHook
     */
    public static function getInstance(): self
    {
        static $instance;

        if (null === $instance) {
            $instance = new self();
        }

        return $instance;
    }
}
/*
 *
 * */
