<?php

namespace Modules\Cms\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Cms\Entities\Faq;

class FaqController extends Controller
{

    public function show()
    {
        $faqs = Faq::where('is_active',true)->get();
        $locale = auth()->user()->lang ?? session()->get(key: 'locale',default: 'en');
        $contents = [];


        foreach($faqs as $faq){

            $questionData = json_decode($faq , true);

            $question = $questionData['question'][$locale]?? '';
            $answer = $questionData['answer'][$locale]?? '';

            $contents[] = [
                'id' => $faq->id,
                'question' => $question,
                'answer' => $answer,
            ];
        }

        return view('cms::faq.show',compact('contents'));
    }

}
