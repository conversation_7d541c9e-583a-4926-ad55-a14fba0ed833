<?php

namespace  Modules\Cms\Http\Controllers\Admin;



use Khaleds\Shared\Http\Controllers\Admin\ControllerAbstract;
use Modules\Cms\Http\Requests\Api\FaqStoreRequest;
use Modules\Cms\Http\Requests\Api\FaqUpdateRequest;
use Modules\Cms\DataTables\FaqDataTable;
use Modules\Cms\Http\Requests\Api\CmsPageUpdateRequest;
use Modules\Cms\DataTables\CmsPageDataTable;
use Modules\Cms\Http\Requests\Api\CmsPageStoreRequest;
use Modules\Cms\Services\CmsPageService;
use Modules\Cms\Services\FaqService;
use Modules\GeneralEvent\DataTables\categoriesDataTable;
use Modules\GeneralEvent\DataTables\typesDataTable;
use Modules\GeneralEvent\Services\CategoryService;
use Modules\GeneralEvent\Services\GeneralEventService;
use Modules\GeneralEvent\Http\Requests\Admin\Types\StoreTypesRequest;
use Modules\GeneralEvent\Http\Requests\Admin\Types\UpdateTypesRequest;
use Modules\GeneralEvent\Services\TypeService;


class FaqController extends ControllerAbstract
{
    protected string $storeRequest = FaqStoreRequest::class;

    protected string $updateRequest = FaqUpdateRequest::class;

    protected string $group = 'Cms Management';
    protected string $dataTable = FaqDataTable::class;
    protected string $viewPath = 'cms::faq';
    protected string $routeName="admin.faq";
    protected string $title = 'Faq';
    public function __construct(FaqService $service){
        parent::__construct($service);
    }
}
