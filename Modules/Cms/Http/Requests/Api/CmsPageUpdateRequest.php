<?php

namespace Modules\Cms\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;

class CmsPageUpdateRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => 'required|array',
            'name.en' => 'required|string|min:5|max:255',
            'name.ar' => 'required|string|min:5|max:255',
            'content' => 'required|array',
            'content.en' => 'required|string|min:5',
            'content.ar' => 'required|string|min:5',
        ];
        return $rules;
    }


}
