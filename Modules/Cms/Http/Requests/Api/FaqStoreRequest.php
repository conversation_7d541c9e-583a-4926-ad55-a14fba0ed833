<?php

namespace Modules\Cms\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;

class FaqStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'question' => 'required|array',
            'question.en' => 'required|string|min:5|max:255',
            'question.ar' => 'required|string|min:5|max:255',
            'answer' => 'required|array',
            'answer.en' => 'required|string|min:5',
            'answer.ar' => 'required|string|min:5',
            'is_active' => 'sometimes',
            'source'=>'string'
        ];
        return $rules;
    }

}
