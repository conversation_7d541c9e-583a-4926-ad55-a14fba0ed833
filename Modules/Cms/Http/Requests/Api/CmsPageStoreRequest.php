<?php

namespace Modules\Cms\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;

class CmsPageStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'name' => 'required|array',
            'name.en' => 'required|string|min:5|max:255',
            'name.ar' => 'required|string|min:5|max:255',
            'content' => 'required|array',
            'content.en' => 'required|string|min:5',
            'content.ar' => 'required|string|min:5',
        ];
        return $rules;
    }

}
