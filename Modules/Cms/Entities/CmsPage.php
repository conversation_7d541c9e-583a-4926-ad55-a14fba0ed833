<?php

namespace Modules\Cms\Entities;

use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property string $name
 * @property mixed $content
 * @property boolean $is_active
 * @property string $created_at
 * @property string $updated_at
 */
class CmsPage extends BaseModel
{
    use SoftDeletes;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $table = 'cms_pages';
    protected $keyType = 'integer';
    protected $translatable=['content','name'];
    protected $guarded = ['id'];

    /**
     * @var array
     */
    protected $fillable = ['name', 'content', 'is_active', 'created_at', 'updated_at'];
}
