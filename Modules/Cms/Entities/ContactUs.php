<?php

namespace Modules\Cms\Entities;

use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property string $name
 * @property string $subject
 * @property string $phone
 * @property string $email
 * @property string $message
 * @property boolean $is_read
 * @property string $created_at
 * @property string $updated_at
 */
class ContactUs extends BaseModel
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'contact_us';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['name', 'subject', 'phone', 'email', 'message', 'is_read', 'created_at', 'updated_at'];

    // Add translatable fields
    public $translatable = ['name'];
}
